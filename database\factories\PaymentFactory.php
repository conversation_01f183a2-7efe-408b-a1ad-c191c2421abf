<?php

namespace Database\Factories;

use App\Models\Payment;
use App\Models\User;
use App\Models\Course;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Payment>
 */
class PaymentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $amount = $this->faker->randomFloat(2, 10, 500);
        $platformFeePercentage = 20; // 20% platform fee
        $platformFee = $amount * ($platformFeePercentage / 100);
        $instructorAmount = $amount - $platformFee;

        return [
            'user_id' => User::factory(),
            'course_id' => Course::factory(),
            'instructor_id' => User::factory(),
            'payment_method' => $this->faker->randomElement(['paypal', 'stripe', 'bank_transfer']),
            'payment_id' => 'test_' . $this->faker->unique()->alphaNumeric(10),
            'amount' => $amount,
            'instructor_amount' => $instructorAmount,
            'platform_fee' => $platformFee,
            'currency' => 'USD',
            'status' => $this->faker->randomElement([
                Payment::STATUS_PENDING,
                Payment::STATUS_COMPLETED,
                Payment::STATUS_FAILED,
                Payment::STATUS_CANCELLED
            ]),
            'type' => Payment::TYPE_COURSE_PURCHASE,
            'metadata' => [
                'course_title' => $this->faker->sentence(3),
                'user_email' => $this->faker->safeEmail(),
                'created_via' => 'factory'
            ],
            'paid_at' => $this->faker->optional(0.7)->dateTimeBetween('-1 month', 'now'),
            'refunded_at' => null,
            'notes' => $this->faker->optional(0.3)->sentence(),
        ];
    }

    /**
     * Indicate that the payment is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Payment::STATUS_PENDING,
            'paid_at' => null,
        ]);
    }

    /**
     * Indicate that the payment is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Payment::STATUS_COMPLETED,
            'paid_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ]);
    }

    /**
     * Indicate that the payment is failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Payment::STATUS_FAILED,
            'paid_at' => null,
        ]);
    }

    /**
     * Indicate that the payment is refunded.
     */
    public function refunded(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Payment::STATUS_REFUNDED,
            'paid_at' => $this->faker->dateTimeBetween('-2 months', '-1 month'),
            'refunded_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ]);
    }

    /**
     * Indicate that the payment is for PayPal.
     */
    public function paypal(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => 'paypal',
            'payment_id' => 'PAYPAL_' . $this->faker->unique()->alphaNumeric(15),
            'metadata' => array_merge($attributes['metadata'] ?? [], [
                'paypal_order_id' => $this->faker->alphaNumeric(17),
                'paypal_status' => 'COMPLETED',
            ]),
        ]);
    }

    /**
     * Set specific amount for the payment.
     */
    public function amount(float $amount): static
    {
        $platformFeePercentage = 20;
        $platformFee = $amount * ($platformFeePercentage / 100);
        $instructorAmount = $amount - $platformFee;

        return $this->state(fn (array $attributes) => [
            'amount' => $amount,
            'instructor_amount' => $instructorAmount,
            'platform_fee' => $platformFee,
        ]);
    }
}
