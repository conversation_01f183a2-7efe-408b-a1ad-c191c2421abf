<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', 'Instructor Dashboard - Escape Matrix Academy')</title>
    <meta name="description" content="@yield('description', 'Manage your educational content and courses.')">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700,800,900" rel="stylesheet" />
    
    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <!-- Additional Styles -->
    @stack('styles')
</head>
<body class="bg-black text-white font-sans antialiased">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b border-gray-800">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center space-x-8">
                    <a href="{{ route('instructor.dashboard') }}" class="text-xl font-bold text-red-500">
                        Instructor Dashboard
                    </a>
                    
                    <!-- Navigation Links -->
                    <div class="hidden md:flex items-center space-x-6">
                        <a href="{{ route('instructor.dashboard') }}" class="text-white hover:text-red-500 transition-colors {{ request()->routeIs('instructor.dashboard') ? 'text-red-500' : '' }}">
                            Dashboard
                        </a>
                        <a href="{{ route('instructor.courses.index') }}" class="text-white hover:text-red-500 transition-colors {{ request()->routeIs('instructor.courses.*') ? 'text-red-500' : '' }}">
                            My Courses
                        </a>
                        <div class="relative group">
                            <button class="text-white hover:text-red-500 transition-colors flex items-center {{ request()->routeIs('instructor.learning-materials.*', 'instructor.videos.*', 'instructor.blog-posts.*', 'instructor.ebooks.*', 'instructor.resources.*', 'instructor.content-files.*') ? 'text-red-500' : '' }}">
                                Content Library <i class="fas fa-chevron-down ml-1 text-xs"></i>
                            </button>
                            <div class="absolute left-0 mt-2 w-56 bg-gray-800 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                                <div class="px-4 py-2 text-xs text-gray-400 uppercase tracking-wide border-b border-gray-700">Course Materials</div>
                                <a href="{{ route('instructor.learning-materials.index') }}" class="block px-4 py-2 text-sm text-white hover:bg-gray-700 {{ request()->routeIs('instructor.learning-materials.*') ? 'bg-gray-700' : '' }}">📚 Learning Materials</a>
                                <a href="{{ route('instructor.videos.index') }}" class="block px-4 py-2 text-sm text-white hover:bg-gray-700 {{ request()->routeIs('instructor.videos.*') ? 'bg-gray-700' : '' }}">🎥 Videos</a>
                                <a href="{{ route('instructor.ebooks.index') }}" class="block px-4 py-2 text-sm text-white hover:bg-gray-700 {{ request()->routeIs('instructor.ebooks.*') ? 'bg-gray-700' : '' }}">📖 Ebooks</a>
                                <a href="{{ route('instructor.resources.index') }}" class="block px-4 py-2 text-sm text-white hover:bg-gray-700 {{ request()->routeIs('instructor.resources.*') ? 'bg-gray-700' : '' }}">🔗 Resources</a>
                                <div class="px-4 py-2 text-xs text-gray-400 uppercase tracking-wide border-b border-gray-700 border-t">Other Content</div>
                                <a href="{{ route('instructor.blog-posts.index') }}" class="block px-4 py-2 text-sm text-white hover:bg-gray-700 {{ request()->routeIs('instructor.blog-posts.*') ? 'bg-gray-700' : '' }}">✍️ Blog Posts</a>
                                <a href="{{ route('instructor.content-files.index') }}" class="block px-4 py-2 text-sm text-white hover:bg-gray-700 {{ request()->routeIs('instructor.content-files.*') ? 'bg-gray-700' : '' }}">📁 File Library</a>
                            </div>
                        </div>
                        <a href="{{ route('instructor.users.index') }}" class="text-white hover:text-red-500 transition-colors {{ request()->routeIs('instructor.users.*') ? 'text-red-500' : '' }}">
                            Students
                        </a>
                        <a href="{{ route('instructor.payments.index') }}" class="text-white hover:text-red-500 transition-colors {{ request()->routeIs('instructor.payments.*') ? 'text-red-500' : '' }}">
                            Payments
                        </a>
                    </div>
                </div>

                <!-- User Menu -->
                <div class="flex items-center space-x-4">
                    <a href="{{ route('home') }}" class="text-gray-400 hover:text-white transition-colors">
                        View Site
                    </a>
                    
                    <div class="relative group">
                        <button class="flex items-center space-x-2 text-white hover:text-red-500 transition-colors">
                            <span>{{ auth()->user()->name }}</span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="absolute right-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <a href="{{ route('dashboard') }}" class="block px-4 py-2 text-sm text-white hover:bg-gray-700">Student Dashboard</a>
                            <a href="{{ route('profile.show') }}" class="block px-4 py-2 text-sm text-white hover:bg-gray-700">Profile</a>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-white hover:bg-gray-700">Logout</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    @if(session('success'))
        <div class="bg-green-600 text-white px-4 py-3 text-center">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-600 text-white px-4 py-3 text-center">
            {{ session('error') }}
        </div>
    @endif

    @if($errors->any())
        <div class="bg-red-600 text-white px-4 py-3">
            <div class="container mx-auto">
                <ul class="list-disc list-inside">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        </div>
    @endif

    <!-- Main Content -->
    <main class="min-h-screen bg-black">
        @yield('content')
    </main>

    <!-- Scripts -->
    @stack('scripts')

<!-- Instructor Form Components -->
<script>
// Reusable Status Dropdown Component
function createStatusDropdown(config) {
    const {
        name,
        id,
        value = '',
        options = [],
        label = 'Status',
        helpText = '',
        required = false,
        error = ''
    } = config;

    return `
        <div>
            <label for="${id}" class="block text-sm font-medium text-gray-300 mb-2">
                ${label}${required ? ' *' : ''}
            </label>
            <div class="relative">
                <select name="${name}" id="${id}"
                        class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent ${error ? 'border-red-500' : ''} appearance-none" ${required ? 'required' : ''}>
                    ${options.map(option => `
                        <option value="${option.value}" ${value === option.value ? 'selected' : ''}>
                            ${option.icon ? option.icon + ' ' : ''}${option.label}${option.description ? ' - ' + option.description : ''}
                        </option>
                    `).join('')}
                </select>
                <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>
            </div>
            ${error ? `<p class="mt-1 text-sm text-red-500">${error}</p>` : ''}
            ${helpText ? `<p class="text-xs text-gray-500 mt-1">${helpText}</p>` : ''}
        </div>
    `;
}

// Publication Status Options
const PUBLICATION_STATUS_OPTIONS = [
    {
        value: '0',
        label: 'Draft',
        description: 'Not visible to students',
        icon: '📝'
    },
    {
        value: '1',
        label: 'Published',
        description: 'Live and available to students',
        icon: '🌟'
    }
];

// Course Status Options
const COURSE_STATUS_OPTIONS = [
    {
        value: 'draft',
        label: 'Draft',
        description: 'Not visible to students',
        icon: '📝'
    },
    {
        value: 'published',
        label: 'Published',
        description: 'Live and available to students',
        icon: '🌟'
    },
    {
        value: 'archived',
        label: 'Archived',
        description: 'Hidden from public but accessible to enrolled students',
        icon: '📦'
    }
];

// File Preview Component
function createFilePreview(file, containerId) {
    const container = document.getElementById(containerId);
    if (!container || !file) return;

    const fileType = file.type;
    const fileName = file.name;
    const fileSize = (file.size / 1024 / 1024).toFixed(2) + ' MB';

    let previewHTML = '';

    if (fileType.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewHTML = `
                <div class="file-preview bg-gray-800 border border-gray-700 rounded-lg p-4 mt-3">
                    <div class="flex items-start space-x-4">
                        <img src="${e.target.result}" alt="Preview" class="w-20 h-20 object-cover rounded-lg">
                        <div class="flex-1">
                            <p class="text-white font-medium">${fileName}</p>
                            <p class="text-gray-400 text-sm">${fileSize}</p>
                            <p class="text-green-400 text-sm">✓ Image preview ready</p>
                        </div>
                    </div>
                </div>
            `;
            container.innerHTML = previewHTML;
        };
        reader.readAsDataURL(file);
    } else if (fileType === 'application/pdf') {
        previewHTML = `
            <div class="file-preview bg-gray-800 border border-gray-700 rounded-lg p-4 mt-3">
                <div class="flex items-start space-x-4">
                    <div class="w-20 h-20 bg-red-600 rounded-lg flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <p class="text-white font-medium">${fileName}</p>
                        <p class="text-gray-400 text-sm">${fileSize}</p>
                        <p class="text-green-400 text-sm">✓ PDF file ready for upload</p>
                    </div>
                </div>
            </div>
        `;
        container.innerHTML = previewHTML;
    } else {
        // Generic file preview
        const fileIcon = getFileIcon(fileType);
        previewHTML = `
            <div class="file-preview bg-gray-800 border border-gray-700 rounded-lg p-4 mt-3">
                <div class="flex items-start space-x-4">
                    <div class="w-20 h-20 bg-gray-600 rounded-lg flex items-center justify-center">
                        ${fileIcon}
                    </div>
                    <div class="flex-1">
                        <p class="text-white font-medium">${fileName}</p>
                        <p class="text-gray-400 text-sm">${fileSize}</p>
                        <p class="text-green-400 text-sm">✓ File ready for upload</p>
                    </div>
                </div>
            </div>
        `;
        container.innerHTML = previewHTML;
    }
}

// Get appropriate file icon based on file type
function getFileIcon(fileType) {
    if (fileType.startsWith('video/')) {
        return '<svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20"><path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path></svg>';
    } else if (fileType.startsWith('audio/')) {
        return '<svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12a7.971 7.971 0 00-1.343-4.243 1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>';
    } else if (fileType.includes('zip') || fileType.includes('archive')) {
        return '<svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>';
    } else {
        return '<svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path></svg>';
    }
}

// Enhanced file upload with progress and validation
function enhanceFileUpload(inputId, previewContainerId, options = {}) {
    const input = document.getElementById(inputId);
    const previewContainer = document.getElementById(previewContainerId);

    if (!input) return;

    const {
        maxSize = 50 * 1024 * 1024, // 50MB default
        allowedTypes = [],
        onFileSelect = null,
        onError = null
    } = options;

    input.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (!file) {
            if (previewContainer) previewContainer.innerHTML = '';
            return;
        }

        // Validate file size
        if (file.size > maxSize) {
            const error = `File size (${(file.size / 1024 / 1024).toFixed(2)}MB) exceeds maximum allowed size (${(maxSize / 1024 / 1024).toFixed(2)}MB)`;
            if (onError) onError(error);
            else alert(error);
            input.value = '';
            return;
        }

        // Validate file type
        if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
            const error = `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`;
            if (onError) onError(error);
            else alert(error);
            input.value = '';
            return;
        }

        // Create preview
        if (previewContainer) {
            createFilePreview(file, previewContainerId);
        }

        // Call custom callback
        if (onFileSelect) onFileSelect(file);
    });
}

// Form validation utilities
const FormValidation = {
    // Real-time field validation
    validateField: function(fieldId, validationRules) {
        const field = document.getElementById(fieldId);
        if (!field) return;

        field.addEventListener('input', function() {
            const value = this.value;
            let feedback = this.parentNode.querySelector(`.${fieldId}-feedback`);

            if (!feedback) {
                feedback = document.createElement('div');
                feedback.className = `${fieldId}-feedback text-xs mt-1`;
                this.parentNode.appendChild(feedback);
            }

            // Apply validation rules
            for (const rule of validationRules) {
                if (rule.condition(value)) {
                    feedback.className = `${fieldId}-feedback text-xs mt-1 ${rule.className}`;
                    feedback.textContent = rule.message(value);
                    break;
                }
            }
        });
    },

    // Common validation rules
    rules: {
        title: [
            {
                condition: (value) => value.length < 10,
                className: 'text-yellow-400',
                message: (value) => `${value.length}/255 characters - Consider a more descriptive title`
            },
            {
                condition: (value) => value.length > 200,
                className: 'text-red-400',
                message: (value) => `${value.length}/255 characters - Title is getting too long`
            },
            {
                condition: (value) => value.length >= 10 && value.length <= 200,
                className: 'text-green-400',
                message: (value) => `${value.length}/255 characters - Good title length`
            }
        ],
        description: [
            {
                condition: (value) => value.length < 50,
                className: 'text-yellow-400',
                message: (value) => `${value.length}/2000 characters - Add more details to help students understand`
            },
            {
                condition: (value) => value.length > 1800,
                className: 'text-red-400',
                message: (value) => `${value.length}/2000 characters - Description is getting too long`
            },
            {
                condition: (value) => value.length >= 50 && value.length <= 1800,
                className: 'text-green-400',
                message: (value) => `${value.length}/2000 characters - Good description length`
            }
        ],
        price: [
            {
                condition: (value) => isNaN(parseFloat(value)) || parseFloat(value) < 0,
                className: 'text-red-400',
                message: () => 'Please enter a valid price'
            },
            {
                condition: (value) => parseFloat(value) === 0,
                className: 'text-blue-400',
                message: () => 'Free course - great for building your audience!'
            },
            {
                condition: (value) => parseFloat(value) > 0 && parseFloat(value) < 10,
                className: 'text-yellow-400',
                message: () => 'Consider if this price reflects the value of your course'
            },
            {
                condition: (value) => parseFloat(value) >= 10,
                className: 'text-green-400',
                message: () => 'Good pricing for a quality course'
            }
        ]
    }
};

// Status dropdown utilities
const StatusDropdown = {
    // Initialize form submission handlers for action buttons
    initActionButtons: function() {
        document.querySelectorAll('button[name="action"]').forEach(button => {
            button.addEventListener('click', function() {
                const statusSelect = document.querySelector('select[name="status"], select[name="is_published"]');
                if (!statusSelect) return;

                if (this.value === 'draft' || this.value === 'save_draft') {
                    statusSelect.value = statusSelect.name === 'status' ? 'draft' : '0';
                } else if (this.value === 'publish') {
                    statusSelect.value = statusSelect.name === 'status' ? 'published' : '1';
                }
            });
        });
    }
};

// Checkbox styling utilities
const CheckboxStyling = {
    init: function() {
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const checkmark = this.parentElement.querySelector('svg');
                if (checkmark) {
                    if (this.checked) {
                        checkmark.classList.remove('hidden');
                    } else {
                        checkmark.classList.add('hidden');
                    }
                }
            });
        });

        // Set initial state for checkboxes
        document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
            const checkmark = checkbox.parentElement.querySelector('svg');
            if (checkmark) {
                checkmark.classList.remove('hidden');
            }
        });
    }
};

// Initialize common form components
function initInstructorFormComponents() {
    // Initialize checkbox styling
    CheckboxStyling.init();

    // Initialize status dropdown action buttons
    StatusDropdown.initActionButtons();

    // Initialize common form validations
    FormValidation.validateField('title', FormValidation.rules.title);
    FormValidation.validateField('description', FormValidation.rules.description);
    FormValidation.validateField('price', FormValidation.rules.price);
}
</script>
</body>
</html>
