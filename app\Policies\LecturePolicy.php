<?php

namespace App\Policies;

use App\Models\Lecture;
use App\Models\Chapter;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class LecturePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any lectures.
     */
    public function viewAny(User $user, Chapter $chapter): bool
    {
        return $user->can('view', $chapter);
    }

    /**
     * Determine whether the user can view the lecture.
     */
    public function view(User $user, Lecture $lecture): bool
    {
        return $user->can('view', $lecture->chapter);
    }

    /**
     * Determine whether the user can create lectures.
     */
    public function create(User $user, Chapter $chapter): bool
    {
        return $user->can('update', $chapter);
    }

    /**
     * Determine whether the user can update the lecture.
     */
    public function update(User $user, Lecture $lecture): bool
    {
        return $user->can('update', $lecture->chapter);
    }

    /**
     * Determine whether the user can delete the lecture.
     */
    public function delete(User $user, Lecture $lecture): bool
    {
        return $user->can('update', $lecture->chapter);
    }

    /**
     * Determine whether the user can restore the lecture.
     */
    public function restore(User $user, Lecture $lecture): bool
    {
        return $user->can('update', $lecture->chapter);
    }

    /**
     * Determine whether the user can permanently delete the lecture.
     */
    public function forceDelete(User $user, Lecture $lecture): bool
    {
        return $user->isSuperAdmin();
    }

    /**
     * Determine whether the user can reorder lectures.
     */
    public function reorder(User $user, Chapter $chapter): bool
    {
        return $user->can('update', $chapter);
    }

    /**
     * Determine whether the user can publish/unpublish the lecture.
     */
    public function publish(User $user, Lecture $lecture): bool
    {
        return $user->can('update', $lecture->chapter);
    }

    /**
     * Determine whether the user can duplicate the lecture.
     */
    public function duplicate(User $user, Lecture $lecture): bool
    {
        return $user->can('update', $lecture->chapter);
    }
}
