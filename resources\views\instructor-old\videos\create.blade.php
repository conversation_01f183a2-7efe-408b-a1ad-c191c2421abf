@extends('instructor.layouts.app')

@section('title', isset($video) ? 'Edit Video - Instructor Dashboard' : 'Upload Video - Instructor Dashboard')

@section('content')
<div class="py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-4 mb-4">
                <a href="{{ route('instructor.videos.index') }}" class="text-gray-400 hover:text-white transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <h1 class="text-3xl font-bold text-white">{{ isset($video) ? 'Edit' : 'Upload' }} Video</h1>
            </div>
            <p class="text-gray-400">{{ isset($video) ? 'Update your video content' : 'Add a new video to your course library' }}</p>
        </div>

        <!-- Form -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
            <form method="POST" action="{{ isset($video) ? route('instructor.videos.update', $video) : route('instructor.videos.store') }}" enctype="multipart/form-data" class="space-y-6">
                @csrf
                @if(isset($video))
                    @method('PUT')
                @endif

                <!-- Title -->
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-300 mb-2">Title *</label>
                    <input type="text" name="title" id="title" value="{{ old('title', $video->title ?? '') }}" 
                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('title') border-red-500 @enderror"
                           placeholder="Enter video title" required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                    <textarea name="description" id="description" rows="4" 
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('description') border-red-500 @enderror"
                              placeholder="Brief description of the video content">{{ old('description', $video->description ?? '') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Duration and Course Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Duration -->
                    <div>
                        <label for="duration_seconds" class="block text-sm font-medium text-gray-300 mb-2">Duration (seconds)</label>
                        <input type="number" name="duration_seconds" id="duration_seconds" value="{{ old('duration_seconds', $video->duration_seconds ?? '') }}"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('duration_seconds') border-red-500 @enderror"
                               placeholder="Video duration in seconds" min="1">
                        @error('duration_seconds')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">Enter duration in seconds (e.g., 300 for 5 minutes)</p>
                    </div>

                    <!-- Course -->
                    <div>
                        <label for="course_id" class="block text-sm font-medium text-gray-300 mb-2">Course (Optional)</label>
                        <select name="course_id" id="course_id" 
                                class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('course_id') border-red-500 @enderror">
                            <option value="">No course association</option>
                            @if(isset($courses))
                                @foreach($courses as $course)
                                    <option value="{{ $course->id }}" {{ old('course_id', $video->course_id ?? '') === $course->id ? 'selected' : '' }}>
                                        {{ $course->title }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                        @error('course_id')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Video URL or File Upload -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Video Source</label>
                    <div class="space-y-4">
                        <!-- Video URL Option -->
                        <div>
                            <label for="youtube_url" class="block text-sm font-medium text-gray-400 mb-2">YouTube URL *</label>
                            <input type="url" name="youtube_url" id="youtube_url" value="{{ old('youtube_url', $video->youtube_url ?? '') }}"
                                   class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('youtube_url') border-red-500 @enderror"
                                   placeholder="https://www.youtube.com/watch?v=..." required>
                            @error('youtube_url')
                                <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="text-center text-gray-500">OR</div>

                        <!-- File Upload Option -->
                        @if(!isset($video))
                            <div>
                                <label for="video_file" class="block text-sm font-medium text-gray-400 mb-2">Upload Video File</label>
                                <div class="flex items-center justify-center w-full">
                                    <label for="video_file" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-700 border-dashed rounded-lg cursor-pointer bg-gray-800 hover:bg-gray-750 transition-colors">
                                        <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                            <svg class="w-8 h-8 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                            </svg>
                                            <p class="mb-2 text-sm text-gray-400">
                                                <span class="font-semibold">Click to upload</span> or drag and drop
                                            </p>
                                            <p class="text-xs text-gray-500">MP4, AVI, MOV files only. Max file size: 500MB</p>
                                        </div>
                                        <input id="video_file" name="video_file" type="file" class="hidden" accept="video/*">
                                    </label>
                                </div>
                                @error('video_file')
                                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                                @enderror
                            </div>
                        @else
                            <div>
                                <label for="video_file" class="block text-sm font-medium text-gray-400 mb-2">Replace Video File (Optional)</label>
                                @if($video->video_path)
                                    <div class="mb-3 p-3 bg-gray-800 rounded-lg">
                                        <p class="text-white text-sm">Current file: {{ basename($video->video_path) }}</p>
                                    </div>
                                @endif
                                <input type="file" name="video_file" id="video_file" accept="video/*" 
                                       class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-red-600 file:text-white hover:file:bg-red-700 @error('video_file') border-red-500 @enderror">
                                @error('video_file')
                                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                                @enderror
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Thumbnail Upload -->
                <div>
                    <label for="thumbnail" class="block text-sm font-medium text-gray-300 mb-2">Thumbnail Image (Optional)</label>
                    @if(isset($video) && $video->thumbnail_url)
                        <div class="mb-3">
                            <img src="{{ $video->thumbnail_url }}" alt="Current thumbnail" class="w-32 h-20 object-cover rounded-lg">
                            <p class="text-gray-400 text-xs mt-1">Current thumbnail</p>
                        </div>
                    @endif
                    <div class="flex items-center justify-center w-full">
                        <label for="thumbnail" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-700 border-dashed rounded-lg cursor-pointer bg-gray-800 hover:bg-gray-750 transition-colors">
                            <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                <svg class="w-8 h-8 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <p class="mb-2 text-sm text-gray-400">
                                    <span class="font-semibold">Click to upload</span> or drag and drop
                                </p>
                                <p class="text-xs text-gray-500">PNG, JPG, GIF, WEBP up to 5MB (Recommended: 1280x720px)</p>
                            </div>
                            <input id="thumbnail" name="thumbnail" type="file" class="hidden" accept="image/*">
                        </label>
                    </div>
                    @error('thumbnail')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                    <!-- Thumbnail Preview Container -->
                    <div id="thumbnail-preview-container"></div>
                </div>

                <!-- Status and Sort Order -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Publication Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-300 mb-2">Publication Status *</label>
                        <div class="relative">
                            <select name="status" id="status"
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('status') border-red-500 @enderror appearance-none" required>
                                <option value="draft" {{ old('status', $video->status ?? 'draft') == 'draft' ? 'selected' : '' }}>
                                    📝 Draft - Not visible to students
                                </option>
                                <option value="published" {{ old('status', $video->status ?? 'draft') == 'published' ? 'selected' : '' }}>
                                    🌟 Published - Live and available to students
                                </option>
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        @error('status')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">Choose the visibility status for this video</p>
                    </div>

                    <!-- Sort Order -->
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-300 mb-2">Sort Order</label>
                        <input type="number" name="sort_order" id="sort_order" value="{{ old('sort_order', $video->sort_order ?? 0) }}" 
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('sort_order') border-red-500 @enderror"
                               placeholder="0" min="0">
                        @error('sort_order')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-800">
                    <a href="{{ route('instructor.videos.index') }}" class="px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                        {{ isset($video) ? 'Update Video' : 'Upload Video' }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Enhanced thumbnail upload with preview
        enhanceFileUpload('thumbnail', 'thumbnail-preview-container', {
            maxSize: 5 * 1024 * 1024, // 5MB
            allowedTypes: [
                'image/jpeg',
                'image/png',
                'image/gif',
                'image/webp'
            ],
            onError: function(error) {
                // Create error message element
                const errorDiv = document.createElement('div');
                errorDiv.className = 'mt-2 p-3 bg-red-900 border border-red-700 rounded-lg text-red-300 text-sm';
                errorDiv.textContent = error;

                // Remove any existing error messages
                const existingError = document.querySelector('.thumbnail-upload-error');
                if (existingError) existingError.remove();

                // Add error class and insert error message
                errorDiv.classList.add('thumbnail-upload-error');
                document.getElementById('thumbnail-preview-container').appendChild(errorDiv);

                // Remove error after 5 seconds
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.remove();
                    }
                }, 5000);
            }
        });

        // URL validation for YouTube videos
        const urlInput = document.getElementById('youtube_url');
        if (urlInput) {
            urlInput.addEventListener('blur', function() {
                const url = this.value.trim();
                if (url && !isValidYouTubeUrl(url)) {
                    // Show validation message
                    let errorDiv = this.parentNode.querySelector('.url-error');
                    if (!errorDiv) {
                        errorDiv = document.createElement('div');
                        errorDiv.className = 'url-error mt-1 text-sm text-red-500';
                        this.parentNode.appendChild(errorDiv);
                    }
                    errorDiv.textContent = 'Please enter a valid YouTube URL';
                    this.classList.add('border-red-500');
                } else {
                    // Remove error message
                    const errorDiv = this.parentNode.querySelector('.url-error');
                    if (errorDiv) errorDiv.remove();
                    this.classList.remove('border-red-500');
                }
            });
        }

        function isValidYouTubeUrl(url) {
            const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|embed\/)|youtu\.be\/)[\w-]+/;
            return youtubeRegex.test(url);
        }
    });
</script>
@endpush
@endsection
