<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Step 2 JavaScript</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .chapter-item { border: 1px solid #ccc; margin: 10px 0; padding: 10px; }
        .sub-chapter-item { border: 1px solid #ddd; margin: 5px 0; padding: 5px; background: #f9f9f9; }
        button { margin: 5px; padding: 5px 10px; }
        input, textarea { margin: 2px 0; padding: 5px; width: 200px; }
        .hidden { display: none; }
        #debug { background: #f0f0f0; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Step 2 JavaScript Test</h1>
    
    <div id="debug">
        <h3>Debug Info:</h3>
        <div id="debug-output"></div>
        <button onclick="debugFormData()">Debug Form Data</button>
    </div>

    <form id="test-form">
        <div id="chapters-container">
            <button type="button" id="add-chapter">+ Add Chapter</button>
            
            <!-- Chapter Template (hidden) -->
            <div id="chapter-template" class="hidden">
                <div class="chapter-item">
                    <h4>Chapter <span class="chapter-number">1</span></h4>
                    <button type="button" class="remove-chapter">Remove Chapter</button>
                    
                    <div>
                        <label>Chapter Title:</label><br>
                        <input type="text" name="chapters[0][title]" placeholder="Chapter title" required>
                    </div>
                    
                    <div>
                        <label>Chapter Description:</label><br>
                        <textarea name="chapters[0][description]" placeholder="Chapter description"></textarea>
                    </div>
                    
                    <div class="sub-chapters-container">
                        <h5>Sub-chapters</h5>
                        <button type="button" class="add-sub-chapter">+ Add Sub-chapter</button>
                        <div class="sub-chapters-list"></div>
                    </div>
                </div>
            </div>

            <!-- Sub-chapter Template (hidden) -->
            <div id="sub-chapter-template" class="hidden">
                <div class="sub-chapter-item">
                    <h6>Sub-chapter <span class="sub-chapter-number">1</span></h6>
                    <button type="button" class="remove-sub-chapter">Remove Sub-chapter</button>
                    
                    <div>
                        <input type="text" name="chapters[0][sub_chapters][0][title]" placeholder="Sub-chapter title" required>
                    </div>
                    <div>
                        <textarea name="chapters[0][sub_chapters][0][description]" placeholder="Sub-chapter description"></textarea>
                    </div>
                </div>
            </div>

            <!-- Chapters will be added here -->
            <div id="chapters-list"></div>
        </div>
        
        <button type="submit">Submit Form</button>
    </form>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        let chapterCount = 0;

        const chaptersContainer = document.getElementById('chapters-list');
        const chapterTemplate = document.getElementById('chapter-template');
        const subChapterTemplate = document.getElementById('sub-chapter-template');
        const addChapterBtn = document.getElementById('add-chapter');

        // Add initial chapter
        addChapter();

        // Add chapter button event
        addChapterBtn.addEventListener('click', addChapter);

        function addChapter() {
            const chapterClone = chapterTemplate.cloneNode(true);
            chapterClone.id = '';
            chapterClone.classList.remove('hidden');
            
            // Update chapter number
            const chapterNumber = chapterClone.querySelector('.chapter-number');
            chapterNumber.textContent = chapterCount + 1;
            
            // Update input names
            updateChapterInputNames(chapterClone, chapterCount);
            
            // Add event listeners
            addChapterEventListeners(chapterClone, chapterCount);
            
            // Add initial sub-chapter
            addSubChapter(chapterClone, chapterCount, 0);
            
            chaptersContainer.appendChild(chapterClone);
            chapterCount++;
            
            debugFormData(); // Auto-debug after adding
        }

        function updateChapterInputNames(chapterElement, chapterIndex) {
            const inputs = chapterElement.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                const name = input.getAttribute('name');
                if (name) {
                    input.setAttribute('name', name.replace(/chapters\[\d+\]/, `chapters[${chapterIndex}]`));
                }
            });
        }

        function addChapterEventListeners(chapterElement, chapterIndex) {
            // Remove chapter button
            const removeBtn = chapterElement.querySelector('.remove-chapter');
            removeBtn.addEventListener('click', function() {
                if (chaptersContainer.children.length > 1) {
                    chapterElement.remove();
                    updateChapterNumbers();
                } else {
                    alert('You must have at least one chapter.');
                }
            });

            // Add sub-chapter button
            const addSubChapterBtn = chapterElement.querySelector('.add-sub-chapter');
            addSubChapterBtn.addEventListener('click', function() {
                const subChaptersList = chapterElement.querySelector('.sub-chapters-list');
                const subChapterCount = subChaptersList.children.length;
                addSubChapter(chapterElement, chapterIndex, subChapterCount);
            });
        }

        function addSubChapter(chapterElement, chapterIndex, subChapterIndex) {
            const subChapterClone = subChapterTemplate.cloneNode(true);
            subChapterClone.id = '';
            subChapterClone.classList.remove('hidden');
            
            // Update sub-chapter number
            const subChapterNumber = subChapterClone.querySelector('.sub-chapter-number');
            subChapterNumber.textContent = subChapterIndex + 1;
            
            // Update input names
            const inputs = subChapterClone.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                const name = input.getAttribute('name');
                if (name) {
                    input.setAttribute('name', name.replace(/chapters\[\d+\]\[sub_chapters\]\[\d+\]/, `chapters[${chapterIndex}][sub_chapters][${subChapterIndex}]`));
                }
            });
            
            // Add remove event listener
            const removeBtn = subChapterClone.querySelector('.remove-sub-chapter');
            removeBtn.addEventListener('click', function() {
                const subChaptersList = chapterElement.querySelector('.sub-chapters-list');
                if (subChaptersList.children.length > 1) {
                    subChapterClone.remove();
                    updateSubChapterNumbers(chapterElement);
                } else {
                    alert('Each chapter must have at least one sub-chapter.');
                }
            });
            
            const subChaptersList = chapterElement.querySelector('.sub-chapters-list');
            subChaptersList.appendChild(subChapterClone);
            
            debugFormData(); // Auto-debug after adding
        }

        function updateChapterNumbers() {
            const chapters = chaptersContainer.querySelectorAll('.chapter-item');
            chapters.forEach((chapter, index) => {
                const chapterNumber = chapter.querySelector('.chapter-number');
                chapterNumber.textContent = index + 1;
                
                // Update input names
                updateChapterInputNames(chapter, index);
                
                // Update sub-chapter input names
                const subChapters = chapter.querySelectorAll('.sub-chapter-item');
                subChapters.forEach((subChapter, subIndex) => {
                    const inputs = subChapter.querySelectorAll('input, textarea');
                    inputs.forEach(input => {
                        const name = input.getAttribute('name');
                        if (name) {
                            input.setAttribute('name', name.replace(/chapters\[\d+\]\[sub_chapters\]\[\d+\]/, `chapters[${index}][sub_chapters][${subIndex}]`));
                        }
                    });
                });
            });
            
            debugFormData(); // Auto-debug after updating
        }

        function updateSubChapterNumbers(chapterElement) {
            const subChapters = chapterElement.querySelectorAll('.sub-chapter-item');
            subChapters.forEach((subChapter, index) => {
                const subChapterNumber = subChapter.querySelector('.sub-chapter-number');
                subChapterNumber.textContent = index + 1;
            });
        }

        // Form submission handler
        document.getElementById('test-form').addEventListener('submit', function(e) {
            e.preventDefault();
            debugFormData();
            alert('Form submission prevented for testing. Check debug output.');
        });
    });

    function debugFormData() {
        const form = document.getElementById('test-form');
        const formData = new FormData(form);
        const debugOutput = document.getElementById('debug-output');
        
        let output = '<h4>Current Form Data:</h4><ul>';
        for (let [key, value] of formData.entries()) {
            output += `<li><strong>${key}:</strong> ${value}</li>`;
        }
        output += '</ul>';
        
        debugOutput.innerHTML = output;
    }
    </script>
</body>
</html>
