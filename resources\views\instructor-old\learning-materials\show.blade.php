@extends('instructor.layouts.app')

@section('title', $learningMaterial->title . ' - Learning Materials')

@section('content')
<div class="py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('instructor.learning-materials.index') }}" class="text-gray-400 hover:text-white transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <h1 class="text-3xl font-bold text-white">{{ $learningMaterial->title }}</h1>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{{ route('instructor.learning-materials.edit', $learningMaterial) }}" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit
                    </a>
                    <form method="POST" action="{{ route('instructor.learning-materials.destroy', $learningMaterial) }}" class="inline-block" onsubmit="return confirm('Are you sure you want to delete this material?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Status and Meta Info -->
            <div class="flex items-center space-x-6 text-sm text-gray-400">
                <div class="flex items-center space-x-2">
                    <span>Status:</span>
                    @if($learningMaterial->is_published)
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Published
                        </span>
                    @else
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            Draft
                        </span>
                    @endif
                </div>
                <div class="flex items-center space-x-2">
                    <span>Type:</span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        @if($learningMaterial->type === 'text') bg-blue-100 text-blue-800
                        @elseif($learningMaterial->type === 'video') bg-purple-100 text-purple-800
                        @elseif($learningMaterial->type === 'audio') bg-green-100 text-green-800
                        @elseif($learningMaterial->type === 'document') bg-yellow-100 text-yellow-800
                        @else bg-gray-100 text-gray-800
                        @endif">
                        {{ ucfirst($learningMaterial->type) }}
                    </span>
                </div>
                <div>Course: <span class="text-white">{{ $learningMaterial->course->title ?? 'No course' }}</span></div>
                <div>Updated: {{ $learningMaterial->updated_at->diffForHumans() }}</div>
            </div>
        </div>

        <!-- Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    @if($learningMaterial->description)
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-white mb-3">Description</h3>
                            <p class="text-gray-300">{{ $learningMaterial->description }}</p>
                        </div>
                    @endif

                    <div>
                        <h3 class="text-lg font-semibold text-white mb-3">Content</h3>
                        <div class="prose prose-invert max-w-none">
                            <div class="text-gray-300 whitespace-pre-wrap">{{ $learningMaterial->content }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- File Information -->
                @if($learningMaterial->hasFile())
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">Attached File</h3>
                        <div class="space-y-3">
                            <div class="flex items-center space-x-3 p-3 bg-gray-800 rounded-lg">
                                <div class="flex-shrink-0">
                                    <svg class="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-white font-medium truncate">{{ $learningMaterial->file_name }}</p>
                                    <p class="text-gray-400 text-sm">{{ $learningMaterial->formatted_file_size }}</p>
                                </div>
                            </div>
                            <a href="{{ $learningMaterial->file_url }}" target="_blank" class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 rounded-lg font-medium transition-colors">
                                View File
                            </a>
                        </div>
                    </div>
                @endif

                <!-- Material Details -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Details</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Slug:</span>
                            <span class="text-white">{{ $learningMaterial->slug }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Sort Order:</span>
                            <span class="text-white">{{ $learningMaterial->sort_order }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Created:</span>
                            <span class="text-white">{{ $learningMaterial->created_at->format('M j, Y') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Last Updated:</span>
                            <span class="text-white">{{ $learningMaterial->updated_at->format('M j, Y') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Course Information -->
                @if($learningMaterial->course)
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">Course</h3>
                        <div class="space-y-3">
                            <h4 class="text-white font-medium">{{ $learningMaterial->course->title }}</h4>
                            <p class="text-gray-400 text-sm">{{ Str::limit($learningMaterial->course->description, 100) }}</p>
                            <div class="flex items-center space-x-4 text-sm text-gray-400">
                                <span>{{ ucfirst($learningMaterial->course->level) }}</span>
                                <span>{{ $learningMaterial->course->category }}</span>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
