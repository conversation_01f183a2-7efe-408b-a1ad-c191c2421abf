<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Role;
use App\Services\GoogleOAuthService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;
use Lara<PERSON>\Socialite\Contracts\User as SocialiteUser;
use Mockery;
use Tests\TestCase;

class GoogleOAuthTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed roles for testing
        $this->artisan('db:seed', ['--class' => 'RoleSeeder']);
    }

    public function test_google_oauth_redirect()
    {
        $response = $this->get(route('auth.google'));
        
        // Should redirect to Google OAuth
        $this->assertEquals(302, $response->getStatusCode());
    }

    public function test_google_oauth_callback_creates_new_user()
    {
        // Mock Socialite
        $googleUser = Mockery::mock(SocialiteUser::class);
        $googleUser->shouldReceive('getId')->andReturn('*********');
        $googleUser->shouldReceive('getEmail')->andReturn('<EMAIL>');
        $googleUser->shouldReceive('getName')->andReturn('Test User');
        $googleUser->shouldReceive('getAvatar')->andReturn('https://example.com/avatar.jpg');

        Socialite::shouldReceive('driver->user')->andReturn($googleUser);

        $response = $this->get(route('auth.google.callback'));

        // Should redirect after successful login
        $response->assertRedirect();
        
        // User should be created
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'google_id' => '*********',
            'name' => 'Test User'
        ]);

        // User should be authenticated
        $this->assertAuthenticated();
        
        // User should have student role
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertTrue($user->hasRole(Role::STUDENT));
    }

    public function test_google_oauth_callback_links_existing_user()
    {
        // Create existing user
        $existingUser = User::factory()->create([
            'email' => '<EMAIL>',
            'google_id' => null
        ]);

        // Mock Socialite
        $googleUser = Mockery::mock(SocialiteUser::class);
        $googleUser->shouldReceive('getId')->andReturn('987654321');
        $googleUser->shouldReceive('getEmail')->andReturn('<EMAIL>');
        $googleUser->shouldReceive('getName')->andReturn('Existing User');
        $googleUser->shouldReceive('getAvatar')->andReturn('https://example.com/avatar.jpg');

        Socialite::shouldReceive('driver->user')->andReturn($googleUser);

        $response = $this->get(route('auth.google.callback'));

        // Should redirect after successful login
        $response->assertRedirect();
        
        // User should be updated with Google ID
        $existingUser->refresh();
        $this->assertEquals('987654321', $existingUser->google_id);

        // User should be authenticated
        $this->assertAuthenticated();
        $this->assertEquals($existingUser->id, Auth::id());
    }

    public function test_google_oauth_callback_handles_existing_google_user()
    {
        // Create user with Google ID
        $existingUser = User::factory()->create([
            'email' => '<EMAIL>',
            'google_id' => '111222333'
        ]);

        // Mock Socialite
        $googleUser = Mockery::mock(SocialiteUser::class);
        $googleUser->shouldReceive('getId')->andReturn('111222333');
        $googleUser->shouldReceive('getEmail')->andReturn('<EMAIL>');
        $googleUser->shouldReceive('getName')->andReturn('Google User');
        $googleUser->shouldReceive('getAvatar')->andReturn('https://example.com/avatar.jpg');

        Socialite::shouldReceive('driver->user')->andReturn($googleUser);

        $response = $this->get(route('auth.google.callback'));

        // Should redirect after successful login
        $response->assertRedirect();
        
        // User should be authenticated
        $this->assertAuthenticated();
        $this->assertEquals($existingUser->id, Auth::id());
    }

    public function test_google_oauth_callback_handles_invalid_data()
    {
        // Mock Socialite with invalid data
        $googleUser = Mockery::mock(SocialiteUser::class);
        $googleUser->shouldReceive('getId')->andReturn(null);
        $googleUser->shouldReceive('getEmail')->andReturn('invalid-email');
        $googleUser->shouldReceive('getName')->andReturn('');
        $googleUser->shouldReceive('getAvatar')->andReturn('');

        Socialite::shouldReceive('driver->user')->andReturn($googleUser);

        $response = $this->get(route('auth.google.callback'));

        // Should redirect to login with error
        $response->assertRedirect(route('login'));
        $response->assertSessionHas('error');
        
        // User should not be authenticated
        $this->assertGuest();
    }

    public function test_google_oauth_service_validates_user_data()
    {
        $service = new GoogleOAuthService();

        // Valid user
        $validUser = Mockery::mock(SocialiteUser::class);
        $validUser->shouldReceive('getId')->andReturn('*********');
        $validUser->shouldReceive('getEmail')->andReturn('<EMAIL>');

        $this->assertTrue($service->validateGoogleUser($validUser));

        // Invalid user - no ID
        $invalidUser1 = Mockery::mock(SocialiteUser::class);
        $invalidUser1->shouldReceive('getId')->andReturn(null);
        $invalidUser1->shouldReceive('getEmail')->andReturn('<EMAIL>');

        $this->assertFalse($service->validateGoogleUser($invalidUser1));

        // Invalid user - no email
        $invalidUser2 = Mockery::mock(SocialiteUser::class);
        $invalidUser2->shouldReceive('getId')->andReturn('*********');
        $invalidUser2->shouldReceive('getEmail')->andReturn(null);

        $this->assertFalse($service->validateGoogleUser($invalidUser2));

        // Invalid user - bad email format
        $invalidUser3 = Mockery::mock(SocialiteUser::class);
        $invalidUser3->shouldReceive('getId')->andReturn('*********');
        $invalidUser3->shouldReceive('getEmail')->andReturn('not-an-email');

        $this->assertFalse($service->validateGoogleUser($invalidUser3));
    }

    public function test_user_model_google_helper_methods()
    {
        // User with Google account
        $googleUser = User::factory()->create([
            'google_id' => '*********',
            'password' => bcrypt('password')
        ]);

        $this->assertTrue($googleUser->hasGoogleAccount());
        $this->assertTrue($googleUser->hasPassword());
        $this->assertFalse($googleUser->isGoogleOnly());

        // Regular user (no Google account)
        $regularUser = User::factory()->create([
            'google_id' => null,
            'password' => bcrypt('password')
        ]);

        $this->assertFalse($regularUser->hasGoogleAccount());
        $this->assertTrue($regularUser->hasPassword());
        $this->assertFalse($regularUser->isGoogleOnly());

        // Test avatar URL generation
        $userWithAvatar = User::factory()->create([
            'avatar' => 'https://example.com/avatar.jpg'
        ]);

        $this->assertEquals('https://example.com/avatar.jpg', $userWithAvatar->getAvatarUrl());

        // Test default avatar generation
        $userWithoutAvatar = User::factory()->create([
            'name' => 'John Doe',
            'avatar' => null
        ]);

        $this->assertStringContainsString('ui-avatars.com', $userWithoutAvatar->getAvatarUrl());
        $this->assertStringContainsString('JD', $userWithoutAvatar->getAvatarUrl());
    }

    public function test_google_oauth_routes_are_rate_limited()
    {
        // Test rate limiting on OAuth redirect
        for ($i = 0; $i < 11; $i++) {
            $response = $this->get(route('auth.google'));
        }
        
        // 11th request should be rate limited
        $response->assertStatus(429);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
