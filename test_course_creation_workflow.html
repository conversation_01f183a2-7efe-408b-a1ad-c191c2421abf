<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Course Creation Workflow Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-black text-white">
    <div class="min-h-screen bg-black">
        <!-- Header -->
        <div class="bg-gradient-to-br from-black via-gray-900 to-black border-b border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="py-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-4xl font-bold text-white">My <span class="text-red-500">Courses</span></h1>
                            <p class="mt-2 text-lg text-gray-400">Manage and organize your course content</p>
                        </div>
                        <div class="flex space-x-3">
                            <button onclick="showCreateCourseModal()"
                               class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-flex items-center gap-2">
                                <i class="fas fa-plus"></i>Create New Course
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center py-16">
                <div class="max-w-md mx-auto">
                    <div class="bg-gray-900 rounded-lg p-8 border border-gray-800">
                        <i class="fas fa-graduation-cap text-6xl text-gray-600 mb-4"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">Test Course Creation Workflow</h3>
                        <p class="text-gray-400 mb-6">Click the "Create New Course" button above to test the new workflow.</p>
                        <button onclick="showCreateCourseModal()"
                           class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors inline-flex items-center gap-2">
                            <i class="fas fa-plus"></i>Create Your First Course
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Course Modal -->
    <div id="create-course-modal" class="fixed inset-0 bg-black bg-opacity-75 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border border-gray-700 w-96 shadow-lg rounded-md bg-gray-800">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-white">Create New Course</h3>
                    <button onclick="hideCreateCourseModal()" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="create-course-form" onsubmit="handleFormSubmit(event)">
                    <div class="mb-4">
                        <label for="course-title" class="block text-sm font-medium text-gray-300 mb-2">Course Title</label>
                        <input type="text" id="course-title" name="title" required
                               class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                               placeholder="Enter your course title">
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="hideCreateCourseModal()"
                                class="px-4 py-2 bg-gray-700 text-gray-300 text-base font-medium rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300">
                            Create Course
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div id="success-modal" class="fixed inset-0 bg-black bg-opacity-75 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border border-gray-700 w-96 shadow-lg rounded-md bg-gray-800">
            <div class="mt-3 text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-900 mb-4">
                    <i class="fas fa-check text-green-400"></i>
                </div>
                <h3 class="text-lg font-medium text-white mb-2">Course Created Successfully!</h3>
                <p class="text-sm text-gray-400 mb-4">
                    Your course "<span id="created-course-title"></span>" has been created and you would now be redirected to the course builder.
                </p>
                <div class="bg-gray-900 border border-gray-700 rounded-lg p-4 mb-4">
                    <h4 class="text-sm font-medium text-white mb-2">Next Steps:</h4>
                    <ul class="text-sm text-gray-400 text-left space-y-1">
                        <li>• Complete course details in the course builder</li>
                        <li>• Add chapters and lectures</li>
                        <li>• Upload course materials</li>
                        <li>• Publish your course</li>
                    </ul>
                </div>
                <button onclick="hideSuccessModal()"
                        class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300">
                    Continue to Course Builder
                </button>
            </div>
        </div>
    </div>

    <script>
        // Create course modal functions
        function showCreateCourseModal() {
            document.getElementById('create-course-modal').classList.remove('hidden');
            document.getElementById('course-title').focus();
        }

        function hideCreateCourseModal() {
            document.getElementById('create-course-modal').classList.add('hidden');
            document.getElementById('course-title').value = '';
        }

        function showSuccessModal(courseTitle) {
            document.getElementById('created-course-title').textContent = courseTitle;
            document.getElementById('success-modal').classList.remove('hidden');
        }

        function hideSuccessModal() {
            document.getElementById('success-modal').classList.add('hidden');
            hideCreateCourseModal();
        }

        function handleFormSubmit(event) {
            event.preventDefault();
            const courseTitle = document.getElementById('course-title').value;
            
            if (courseTitle.trim()) {
                // Simulate course creation
                setTimeout(() => {
                    hideCreateCourseModal();
                    showSuccessModal(courseTitle);
                }, 500);
            }
        }

        // Close modals when clicking outside
        document.addEventListener('click', function(event) {
            const createModal = document.getElementById('create-course-modal');
            const successModal = document.getElementById('success-modal');
            
            if (event.target === createModal) {
                hideCreateCourseModal();
            }
            if (event.target === successModal) {
                hideSuccessModal();
            }
        });

        // Close modals with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                hideCreateCourseModal();
                hideSuccessModal();
            }
        });
    </script>
</body>
</html>
