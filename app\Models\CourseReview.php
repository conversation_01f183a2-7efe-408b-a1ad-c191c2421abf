<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class CourseReview extends Model
{
    use HasFactory;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'user_id',
        'course_id',
        'instructor_id',
        'rating',
        'review',
        'is_published',
        'is_featured',
        'helpful_votes',
        'unhelpful_votes',
        'moderated_by',
        'moderation_notes',
    ];

    protected $casts = [
        'rating' => 'integer',
        'is_published' => 'boolean',
        'is_featured' => 'boolean',
        'helpful_votes' => 'integer',
        'unhelpful_votes' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
        });

        static::saved(function ($model) {
            // Update course statistics when review is saved
            if ($model->course) {
                $model->course->updateStatistics();
            }
        });

        static::deleted(function ($model) {
            // Update course statistics when review is deleted
            if ($model->course) {
                $model->course->updateStatistics();
            }
        });
    }

    /**
     * Get the user that wrote the review.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the course for the review.
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get the instructor for the review.
     */
    public function instructor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'instructor_id');
    }

    /**
     * Get the moderator who moderated the review.
     */
    public function moderatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'moderated_by');
    }

    /**
     * Scope to filter published reviews.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope to filter featured reviews.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to filter by rating.
     */
    public function scopeByRating($query, $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Scope to filter by course.
     */
    public function scopeByCourse($query, $courseId)
    {
        return $query->where('course_id', $courseId);
    }

    /**
     * Scope to filter by instructor.
     */
    public function scopeByInstructor($query, $instructorId)
    {
        return $query->where('instructor_id', $instructorId);
    }

    /**
     * Scope to order by helpfulness.
     */
    public function scopeByHelpfulness($query)
    {
        return $query->orderByRaw('(helpful_votes - unhelpful_votes) DESC');
    }

    /**
     * Check if the review is published.
     */
    public function isPublished(): bool
    {
        return $this->is_published;
    }

    /**
     * Check if the review is featured.
     */
    public function isFeatured(): bool
    {
        return $this->is_featured;
    }

    /**
     * Get the net helpfulness score.
     */
    public function getHelpfulnessScore(): int
    {
        return $this->helpful_votes - $this->unhelpful_votes;
    }

    /**
     * Mark review as helpful.
     */
    public function markAsHelpful(): void
    {
        $this->increment('helpful_votes');
    }

    /**
     * Mark review as unhelpful.
     */
    public function markAsUnhelpful(): void
    {
        $this->increment('unhelpful_votes');
    }

    /**
     * Publish the review.
     */
    public function publish(): void
    {
        $this->update(['is_published' => true]);
    }

    /**
     * Unpublish the review.
     */
    public function unpublish(): void
    {
        $this->update(['is_published' => false]);
    }

    /**
     * Feature the review.
     */
    public function feature(): void
    {
        $this->update(['is_featured' => true]);
    }

    /**
     * Unfeature the review.
     */
    public function unfeature(): void
    {
        $this->update(['is_featured' => false]);
    }

    /**
     * Moderate the review.
     */
    public function moderate(User $moderator, string $notes = null): void
    {
        $this->update([
            'moderated_by' => $moderator->id,
            'moderation_notes' => $notes,
        ]);
    }

    /**
     * Get star rating as HTML.
     */
    public function getStarRatingHtml(): string
    {
        $html = '';
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $this->rating) {
                $html .= '<i class="fas fa-star text-yellow-400"></i>';
            } else {
                $html .= '<i class="far fa-star text-gray-400"></i>';
            }
        }
        return $html;
    }

    /**
     * Get truncated review text.
     */
    public function getTruncatedReview(int $length = 150): string
    {
        if (strlen($this->review) <= $length) {
            return $this->review;
        }
        return substr($this->review, 0, $length) . '...';
    }
}
