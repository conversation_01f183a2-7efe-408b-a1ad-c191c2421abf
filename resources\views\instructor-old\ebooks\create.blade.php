@extends('instructor.layouts.app')

@section('title', isset($ebook) ? 'Edit Ebook - Instructor Dashboard' : 'Upload Ebook - Instructor Dashboard')

@section('content')
<div class="py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-4 mb-4">
                <a href="{{ route('instructor.ebooks.index') }}" class="text-gray-400 hover:text-white transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <h1 class="text-3xl font-bold text-white">{{ isset($ebook) ? 'Edit' : 'Upload' }} Ebook</h1>
            </div>
            <p class="text-gray-400">{{ isset($ebook) ? 'Update your ebook information' : 'Add a new digital book to your library' }}</p>
        </div>

        <!-- Form -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
            <form method="POST" action="{{ isset($ebook) ? route('instructor.ebooks.update', $ebook) : route('instructor.ebooks.store') }}" enctype="multipart/form-data" class="space-y-6">
                @csrf
                @if(isset($ebook))
                    @method('PUT')
                @endif

                <!-- Title -->
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-300 mb-2">Title *</label>
                    <input type="text" name="title" id="title" value="{{ old('title', $ebook->title ?? '') }}" 
                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('title') border-red-500 @enderror"
                           placeholder="Enter ebook title" required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                    <textarea name="description" id="description" rows="4" 
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('description') border-red-500 @enderror"
                              placeholder="Brief description of the ebook">{{ old('description', $ebook->description ?? '') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Author and ISBN Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Author -->
                    <div>
                        <label for="author" class="block text-sm font-medium text-gray-300 mb-2">Author</label>
                        <input type="text" name="author" id="author" value="{{ old('author', $ebook->author ?? '') }}" 
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('author') border-red-500 @enderror"
                               placeholder="Author name">
                        @error('author')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- ISBN -->
                    <div>
                        <label for="isbn" class="block text-sm font-medium text-gray-300 mb-2">ISBN</label>
                        <input type="text" name="isbn" id="isbn" value="{{ old('isbn', $ebook->isbn ?? '') }}" 
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('isbn') border-red-500 @enderror"
                               placeholder="ISBN number">
                        @error('isbn')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Publication Date and Course Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Publication Date -->
                    <div>
                        <label for="publication_date" class="block text-sm font-medium text-gray-300 mb-2">Publication Date</label>
                        <input type="date" name="publication_date" id="publication_date" value="{{ old('publication_date', (isset($ebook) && $ebook->publication_date) ? $ebook->publication_date->format('Y-m-d') : '') }}"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('publication_date') border-red-500 @enderror">
                        @error('publication_date')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Course -->
                    <div>
                        <label for="course_id" class="block text-sm font-medium text-gray-300 mb-2">Course (Optional)</label>
                        <select name="course_id" id="course_id" 
                                class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('course_id') border-red-500 @enderror">
                            <option value="">No course association</option>
                            @foreach($courses as $course)
                                <option value="{{ $course->id }}" {{ old('course_id', isset($ebook) ? $ebook->course_id : '') === $course->id ? 'selected' : '' }}>
                                    {{ $course->title }}
                                </option>
                            @endforeach
                        </select>
                        @error('course_id')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- File Upload -->
                @if(!isset($ebook))
                    <div>
                        <label for="file" class="block text-sm font-medium text-gray-300 mb-2">Ebook File (PDF/EPUB) *</label>
                        <div class="flex items-center justify-center w-full">
                            <label for="file" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-700 border-dashed rounded-lg cursor-pointer bg-gray-800 hover:bg-gray-750 transition-colors">
                                <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                    <svg class="w-8 h-8 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                    <p class="mb-2 text-sm text-gray-400">
                                        <span class="font-semibold">Click to upload</span> or drag and drop
                                    </p>
                                    <p class="text-xs text-gray-500">PDF or EPUB files only. Max file size: 100MB</p>
                                </div>
                                <input id="file" name="file" type="file" class="hidden" accept=".pdf,.epub" required>
                            </label>
                        </div>
                        @error('file')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                        <!-- File Preview Container -->
                        <div id="file-preview-container"></div>
                    </div>
                @else
                    <div>
                        <label for="file" class="block text-sm font-medium text-gray-300 mb-2">Replace Ebook File (Optional)</label>
                        <div class="mb-3 p-3 bg-gray-800 rounded-lg">
                            <p class="text-white text-sm">Current file: {{ $ebook->file_name }}</p>
                            <p class="text-gray-400 text-xs">{{ $ebook->formatted_file_size }}</p>
                        </div>
                        <div class="flex items-center justify-center w-full">
                            <label for="file" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-700 border-dashed rounded-lg cursor-pointer bg-gray-800 hover:bg-gray-750 transition-colors">
                                <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                    <svg class="w-8 h-8 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                    <p class="mb-2 text-sm text-gray-400">
                                        <span class="font-semibold">Click to replace</span> or drag and drop
                                    </p>
                                    <p class="text-xs text-gray-500">PDF or EPUB files only. Max file size: 100MB</p>
                                </div>
                                <input id="file" name="file" type="file" class="hidden" accept=".pdf,.epub">
                            </label>
                        </div>
                        @error('file')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                        <!-- File Preview Container -->
                        <div id="file-preview-container"></div>
                    </div>
                @endif

                <!-- Cover Image Upload -->
                <div>
                    <label for="cover_image" class="block text-sm font-medium text-gray-300 mb-2">Cover Image (Optional)</label>
                    @if(isset($ebook) && $ebook->cover_image_url)
                        <div class="mb-3">
                            <img src="{{ $ebook->cover_image_url }}" alt="Current cover" class="w-24 h-32 object-cover rounded-lg">
                            <p class="text-gray-400 text-xs mt-1">Current cover image</p>
                        </div>
                    @endif
                    <div class="flex items-center justify-center w-full">
                        <label for="cover_image" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-700 border-dashed rounded-lg cursor-pointer bg-gray-800 hover:bg-gray-750 transition-colors">
                            <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                <svg class="w-8 h-8 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <p class="mb-2 text-sm text-gray-400">
                                    <span class="font-semibold">Click to upload</span> or drag and drop
                                </p>
                                <p class="text-xs text-gray-500">PNG, JPG, GIF, WEBP up to 5MB (Recommended: 400x600px)</p>
                            </div>
                            <input id="cover_image" name="cover_image" type="file" class="hidden" accept="image/*">
                        </label>
                    </div>
                    @error('cover_image')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                    <!-- Cover Image Preview Container -->
                    <div id="cover-image-preview-container"></div>
                </div>

                <!-- Status Options -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Publication Status -->
                    <div>
                        <label for="is_published" class="block text-sm font-medium text-gray-300 mb-2">Publication Status *</label>
                        <div class="relative">
                            <select name="is_published" id="is_published"
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('is_published') border-red-500 @enderror appearance-none" required>
                                <option value="0" {{ old('is_published', isset($ebook) ? $ebook->is_published : '0') == '0' ? 'selected' : '' }}>
                                    📝 Draft - Not visible to students
                                </option>
                                <option value="1" {{ old('is_published', isset($ebook) ? $ebook->is_published : '0') == '1' ? 'selected' : '' }}>
                                    🌟 Published - Live and available to students
                                </option>
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        @error('is_published')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">Choose the visibility status for this ebook</p>
                    </div>

                    <!-- Download Permission -->
                    <div>
                        <label for="is_downloadable" class="block text-sm font-medium text-gray-300 mb-2">Download Permission *</label>
                        <div class="relative">
                            <select name="is_downloadable" id="is_downloadable"
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('is_downloadable') border-red-500 @enderror appearance-none" required>
                                <option value="0" {{ old('is_downloadable', isset($ebook) ? $ebook->is_downloadable : '1') == '0' ? 'selected' : '' }}>
                                    👁️ View Only - Students can read but not download
                                </option>
                                <option value="1" {{ old('is_downloadable', isset($ebook) ? $ebook->is_downloadable : '1') == '1' ? 'selected' : '' }}>
                                    📥 Downloadable - Students can download the file
                                </option>
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        @error('is_downloadable')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">Control whether students can download this ebook</p>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-800">
                    <a href="{{ route('instructor.ebooks.index') }}" class="px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                        {{ isset($ebook) ? 'Update Ebook' : 'Upload Ebook' }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Enhanced file upload with preview for ebook files
        enhanceFileUpload('file', 'file-preview-container', {
            maxSize: 100 * 1024 * 1024, // 100MB
            allowedTypes: [
                'application/pdf',
                'application/epub+zip'
            ],
            onError: function(error) {
                // Create error message element
                const errorDiv = document.createElement('div');
                errorDiv.className = 'mt-2 p-3 bg-red-900 border border-red-700 rounded-lg text-red-300 text-sm';
                errorDiv.textContent = error;

                // Remove any existing error messages
                const existingError = document.querySelector('.file-upload-error');
                if (existingError) existingError.remove();

                // Add error class and insert error message
                errorDiv.classList.add('file-upload-error');
                document.getElementById('file-preview-container').appendChild(errorDiv);

                // Remove error after 5 seconds
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.remove();
                    }
                }, 5000);
            }
        });

        // Enhanced cover image upload with preview
        enhanceFileUpload('cover_image', 'cover-image-preview-container', {
            maxSize: 5 * 1024 * 1024, // 5MB
            allowedTypes: [
                'image/jpeg',
                'image/png',
                'image/gif',
                'image/webp'
            ],
            onError: function(error) {
                // Create error message element
                const errorDiv = document.createElement('div');
                errorDiv.className = 'mt-2 p-3 bg-red-900 border border-red-700 rounded-lg text-red-300 text-sm';
                errorDiv.textContent = error;

                // Remove any existing error messages
                const existingError = document.querySelector('.cover-image-upload-error');
                if (existingError) existingError.remove();

                // Add error class and insert error message
                errorDiv.classList.add('cover-image-upload-error');
                document.getElementById('cover-image-preview-container').appendChild(errorDiv);

                // Remove error after 5 seconds
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.remove();
                    }
                }, 5000);
            }
        });
    });
</script>
@endpush
@endsection
