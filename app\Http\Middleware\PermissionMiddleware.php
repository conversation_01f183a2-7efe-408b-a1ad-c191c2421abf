<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $permission): Response
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            // Store the intended URL for redirect after login
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Unauthenticated.'], 401);
            }

            session(['url.intended' => $request->fullUrl()]);
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Check if user has the required permission
        if (!$user->hasPermission($permission)) {
            abort(403, 'Access denied. Required permission: ' . $permission);
        }

        return $next($request);
    }
}
