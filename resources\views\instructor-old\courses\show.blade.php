@extends('instructor.layouts.app')

@section('title', $course->title . ' - Course Details')

@section('content')
<div class="py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-4 mb-4">
                <a href="{{ route('instructor.courses.index') }}" class="text-gray-400 hover:text-white transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <div class="flex-1">
                    <h1 class="text-3xl font-bold text-white">{{ $course->title }}</h1>
                    <div class="flex items-center space-x-4 mt-2">
                        <span class="px-3 py-1 bg-{{ $course->status === 'published' ? 'green' : ($course->status === 'draft' ? 'yellow' : 'gray') }}-600 text-white rounded-full text-sm">
                            {{ ucfirst($course->status) }}
                        </span>
                        @if($course->featured)
                            <span class="px-3 py-1 bg-red-600 text-white rounded-full text-sm">Featured</span>
                        @endif
                        <span class="text-gray-400">{{ $course->category }} • {{ ucfirst($course->level) }}</span>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('instructor.courses.edit', $course) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                        Edit Course
                    </a>
                    <form method="POST" action="{{ route('instructor.courses.toggle-status', $course) }}" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="bg-{{ $course->status === 'published' ? 'yellow' : 'green' }}-600 hover:bg-{{ $course->status === 'published' ? 'yellow' : 'green' }}-700 text-white px-4 py-2 rounded-lg transition-colors">
                            {{ $course->status === 'published' ? 'Unpublish' : 'Publish' }}
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Course Overview -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- Course Image and Info -->
            <div class="lg:col-span-2">
                <div class="bg-gray-900 border border-gray-800 rounded-lg overflow-hidden mb-6">
                    <img src="{{ $course->image_url ?: 'https://via.placeholder.com/800x400' }}"
                         alt="{{ $course->title }}"
                         class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h2 class="text-xl font-bold text-white mb-4">Course Description</h2>
                        <p class="text-gray-300 leading-relaxed">{{ $course->description }}</p>
                    </div>
                </div>

                <!-- Course Statistics -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-red-500 mb-1">{{ $stats['total_enrollments'] }}</div>
                        <div class="text-gray-400 text-sm">Total Students</div>
                    </div>
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-green-500 mb-1">{{ $stats['active_enrollments'] }}</div>
                        <div class="text-gray-400 text-sm">Active Students</div>
                    </div>
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-blue-500 mb-1">${{ number_format($stats['total_revenue'], 0) }}</div>
                        <div class="text-gray-400 text-sm">Total Revenue</div>
                    </div>
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-500 mb-1">{{ $stats['learning_materials'] }}</div>
                        <div class="text-gray-400 text-sm">Materials</div>
                    </div>
                </div>

                <!-- Course Content Management -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg overflow-hidden">
                    <!-- Tab Navigation -->
                    <div class="border-b border-gray-800">
                        <nav class="flex space-x-8 px-6" aria-label="Tabs">
                            <button onclick="showTab('overview')" id="tab-overview" class="tab-button border-b-2 border-red-500 py-4 px-1 text-sm font-medium text-red-500">
                                Overview
                            </button>
                            <button onclick="showTab('materials')" id="tab-materials" class="tab-button border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-400 hover:text-white hover:border-gray-300">
                                Learning Materials ({{ $course->learningMaterials->count() }})
                            </button>
                            <button onclick="showTab('videos')" id="tab-videos" class="tab-button border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-400 hover:text-white hover:border-gray-300">
                                Videos ({{ $course->videoContents->count() ?? 0 }})
                            </button>
                            <button onclick="showTab('ebooks')" id="tab-ebooks" class="tab-button border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-400 hover:text-white hover:border-gray-300">
                                Ebooks ({{ $course->ebooks->count() }})
                            </button>
                            <button onclick="showTab('resources')" id="tab-resources" class="tab-button border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-400 hover:text-white hover:border-gray-300">
                                Resources ({{ $course->resources->count() }})
                            </button>
                            <button onclick="showTab('files')" id="tab-files" class="tab-button border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-400 hover:text-white hover:border-gray-300">
                                Files ({{ $course->contentFiles->count() }})
                            </button>
                        </nav>
                    </div>

                    <!-- Tab Content -->
                    <div class="p-6">
                        <!-- Overview Tab -->
                        <div id="content-overview" class="tab-content">
                            <h2 class="text-xl font-bold text-white mb-4">Course Content Overview</h2>
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                                <div class="bg-gray-800 rounded-lg p-4 text-center">
                                    <div class="text-2xl mb-2">📚</div>
                                    <div class="text-lg font-bold text-white">{{ $course->learningMaterials->count() }}</div>
                                    <div class="text-gray-400 text-sm">Learning Materials</div>
                                </div>
                                <div class="bg-gray-800 rounded-lg p-4 text-center">
                                    <div class="text-2xl mb-2">🎥</div>
                                    <div class="text-lg font-bold text-white">{{ $course->videoContents->count() ?? 0 }}</div>
                                    <div class="text-gray-400 text-sm">Videos</div>
                                </div>
                                <div class="bg-gray-800 rounded-lg p-4 text-center">
                                    <div class="text-2xl mb-2">📖</div>
                                    <div class="text-lg font-bold text-white">{{ $course->ebooks->count() }}</div>
                                    <div class="text-gray-400 text-sm">Ebooks</div>
                                </div>
                                <div class="bg-gray-800 rounded-lg p-4 text-center">
                                    <div class="text-2xl mb-2">🔗</div>
                                    <div class="text-lg font-bold text-white">{{ $course->resources->count() }}</div>
                                    <div class="text-gray-400 text-sm">Resources</div>
                                </div>
                                <div class="bg-gray-800 rounded-lg p-4 text-center">
                                    <div class="text-2xl mb-2">📁</div>
                                    <div class="text-lg font-bold text-white">{{ $course->contentFiles->count() }}</div>
                                    <div class="text-gray-400 text-sm">Files</div>
                                </div>
                                <div class="bg-gray-800 rounded-lg p-4 text-center">
                                    <div class="text-2xl mb-2">📊</div>
                                    <div class="text-lg font-bold text-white">{{ $stats['published_materials'] }}</div>
                                    <div class="text-gray-400 text-sm">Published</div>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                <a href="{{ route('instructor.learning-materials.create', ['course_id' => $course->id]) }}" class="bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg transition-colors text-center">
                                    + Add Learning Material
                                </a>
                                <a href="{{ route('instructor.videos.create', ['course_id' => $course->id]) }}" class="bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg transition-colors text-center">
                                    + Add Video
                                </a>
                                <a href="{{ route('instructor.ebooks.create', ['course_id' => $course->id]) }}" class="bg-purple-600 hover:bg-purple-700 text-white py-3 px-4 rounded-lg transition-colors text-center">
                                    + Add Ebook
                                </a>
                                <a href="{{ route('instructor.resources.create', ['course_id' => $course->id]) }}" class="bg-yellow-600 hover:bg-yellow-700 text-white py-3 px-4 rounded-lg transition-colors text-center">
                                    + Add Resource
                                </a>
                                <a href="{{ route('instructor.content-files.create', ['course_id' => $course->id]) }}" class="bg-indigo-600 hover:bg-indigo-700 text-white py-3 px-4 rounded-lg transition-colors text-center">
                                    + Upload File
                                </a>
                                <a href="{{ route('instructor.blog-posts.create', ['course_id' => $course->id]) }}" class="bg-pink-600 hover:bg-pink-700 text-white py-3 px-4 rounded-lg transition-colors text-center">
                                    + Write Blog Post
                                </a>
                            </div>
                        </div>

                        <!-- Learning Materials Tab -->
                        <div id="content-materials" class="tab-content hidden">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-white">Learning Materials</h2>
                                <a href="{{ route('instructor.learning-materials.create', ['course_id' => $course->id]) }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                                    + Add Material
                                </a>
                            </div>

                            @if($course->learningMaterials->count() > 0)
                                <div class="space-y-3">
                                    @foreach($course->learningMaterials as $material)
                                        <div class="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                                            <div class="flex items-center space-x-3">
                                                <div class="text-xl">
                                                    @switch($material->type)
                                                        @case('video')
                                                            🎥
                                                            @break
                                                        @case('document')
                                                            📄
                                                            @break
                                                        @case('quiz')
                                                            ❓
                                                            @break
                                                        @default
                                                            📚
                                                    @endswitch
                                                </div>
                                                <div>
                                                    <h3 class="text-white font-medium">{{ $material->title }}</h3>
                                                    <p class="text-gray-400 text-sm">{{ ucfirst($material->type) }} • {{ $material->is_published ? 'Published' : 'Draft' }} • {{ $material->created_at->format('M d, Y') }}</p>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <a href="{{ route('instructor.learning-materials.show', $material) }}" class="text-blue-400 hover:text-blue-300 transition-colors">
                                                    View
                                                </a>
                                                <a href="{{ route('instructor.learning-materials.edit', $material) }}" class="text-yellow-400 hover:text-yellow-300 transition-colors">
                                                    Edit
                                                </a>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-12">
                                    <div class="text-4xl mb-3">📚</div>
                                    <p class="text-gray-400 mb-4">No learning materials added yet</p>
                                    <a href="{{ route('instructor.learning-materials.create', ['course_id' => $course->id]) }}" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors">
                                        Add First Material
                                    </a>
                                </div>
                            @endif
                        </div>

                        <!-- Videos Tab -->
                        <div id="content-videos" class="tab-content hidden">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-white">Video Content</h2>
                                <a href="{{ route('instructor.videos.create', ['course_id' => $course->id]) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                                    + Add Video
                                </a>
                            </div>

                            @if(isset($course->videoContents) && $course->videoContents->count() > 0)
                                <div class="space-y-3">
                                    @foreach($course->videoContents as $video)
                                        <div class="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                                            <div class="flex items-center space-x-3">
                                                <div class="text-xl">🎥</div>
                                                <div>
                                                    <h3 class="text-white font-medium">{{ $video->title }}</h3>
                                                    <p class="text-gray-400 text-sm">{{ $video->duration ?? 'N/A' }} • {{ $video->is_published ? 'Published' : 'Draft' }} • {{ $video->created_at->format('M d, Y') }}</p>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <a href="{{ route('instructor.videos.show', $video) }}" class="text-blue-400 hover:text-blue-300 transition-colors">
                                                    View
                                                </a>
                                                <a href="{{ route('instructor.videos.edit', $video) }}" class="text-yellow-400 hover:text-yellow-300 transition-colors">
                                                    Edit
                                                </a>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-12">
                                    <div class="text-4xl mb-3">🎥</div>
                                    <p class="text-gray-400 mb-4">No videos added yet</p>
                                    <a href="{{ route('instructor.videos.create', ['course_id' => $course->id]) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
                                        Add First Video
                                    </a>
                                </div>
                            @endif
                        </div>

                        <!-- Ebooks Tab -->
                        <div id="content-ebooks" class="tab-content hidden">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-white">Ebooks</h2>
                                <a href="{{ route('instructor.ebooks.create', ['course_id' => $course->id]) }}" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
                                    + Add Ebook
                                </a>
                            </div>

                            @if($course->ebooks->count() > 0)
                                <div class="space-y-3">
                                    @foreach($course->ebooks as $ebook)
                                        <div class="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                                            <div class="flex items-center space-x-3">
                                                <div class="text-xl">📖</div>
                                                <div>
                                                    <h3 class="text-white font-medium">{{ $ebook->title }}</h3>
                                                    <p class="text-gray-400 text-sm">{{ $ebook->pages ?? 'N/A' }} pages • {{ $ebook->is_published ? 'Published' : 'Draft' }} • {{ $ebook->created_at->format('M d, Y') }}</p>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <a href="{{ route('instructor.ebooks.show', $ebook) }}" class="text-blue-400 hover:text-blue-300 transition-colors">
                                                    View
                                                </a>
                                                <a href="{{ route('instructor.ebooks.edit', $ebook) }}" class="text-yellow-400 hover:text-yellow-300 transition-colors">
                                                    Edit
                                                </a>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-12">
                                    <div class="text-4xl mb-3">📖</div>
                                    <p class="text-gray-400 mb-4">No ebooks added yet</p>
                                    <a href="{{ route('instructor.ebooks.create', ['course_id' => $course->id]) }}" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors">
                                        Add First Ebook
                                    </a>
                                </div>
                            @endif
                        </div>

                        <!-- Resources Tab -->
                        <div id="content-resources" class="tab-content hidden">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-white">Resources</h2>
                                <a href="{{ route('instructor.resources.create', ['course_id' => $course->id]) }}" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors">
                                    + Add Resource
                                </a>
                            </div>

                            @if($course->resources->count() > 0)
                                <div class="space-y-3">
                                    @foreach($course->resources as $resource)
                                        <div class="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                                            <div class="flex items-center space-x-3">
                                                <div class="text-xl">🔗</div>
                                                <div>
                                                    <h3 class="text-white font-medium">{{ $resource->title }}</h3>
                                                    <p class="text-gray-400 text-sm">{{ ucfirst($resource->type) }} • {{ $resource->is_published ? 'Published' : 'Draft' }} • {{ $resource->created_at->format('M d, Y') }}</p>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <a href="{{ route('instructor.resources.show', $resource) }}" class="text-blue-400 hover:text-blue-300 transition-colors">
                                                    View
                                                </a>
                                                <a href="{{ route('instructor.resources.edit', $resource) }}" class="text-yellow-400 hover:text-yellow-300 transition-colors">
                                                    Edit
                                                </a>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-12">
                                    <div class="text-4xl mb-3">🔗</div>
                                    <p class="text-gray-400 mb-4">No resources added yet</p>
                                    <a href="{{ route('instructor.resources.create', ['course_id' => $course->id]) }}" class="bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-3 rounded-lg transition-colors">
                                        Add First Resource
                                    </a>
                                </div>
                            @endif
                        </div>

                        <!-- Files Tab -->
                        <div id="content-files" class="tab-content hidden">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-white">Content Files</h2>
                                <a href="{{ route('instructor.content-files.create', ['course_id' => $course->id]) }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                                    + Upload File
                                </a>
                            </div>

                            @if($course->contentFiles->count() > 0)
                                <div class="space-y-3">
                                    @foreach($course->contentFiles as $file)
                                        <div class="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                                            <div class="flex items-center space-x-3">
                                                <div class="text-xl">
                                                    @switch($file->category)
                                                        @case('image')
                                                            🖼️
                                                            @break
                                                        @case('video')
                                                            🎥
                                                            @break
                                                        @case('document')
                                                            📄
                                                            @break
                                                        @case('audio')
                                                            🎵
                                                            @break
                                                        @case('archive')
                                                            📦
                                                            @break
                                                        @default
                                                            📁
                                                    @endswitch
                                                </div>
                                                <div>
                                                    <h3 class="text-white font-medium">{{ $file->title }}</h3>
                                                    <p class="text-gray-400 text-sm">{{ $file->file_name }} • {{ $file->formatted_file_size }} • {{ $file->created_at->format('M d, Y') }}</p>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <a href="{{ route('instructor.content-files.show', $file) }}" class="text-blue-400 hover:text-blue-300 transition-colors">
                                                    View
                                                </a>
                                                <a href="{{ route('instructor.content-files.edit', $file) }}" class="text-yellow-400 hover:text-yellow-300 transition-colors">
                                                    Edit
                                                </a>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-12">
                                    <div class="text-4xl mb-3">📁</div>
                                    <p class="text-gray-400 mb-4">No files uploaded yet</p>
                                    <a href="{{ route('instructor.content-files.create', ['course_id' => $course->id]) }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg transition-colors">
                                        Upload First File
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Course Details -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-6">
                    <h3 class="text-lg font-bold text-white mb-4">Course Details</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Price</span>
                            <span class="text-white font-medium">{{ $course->formatted_price }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Duration</span>
                            <span class="text-white">{{ $course->duration }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Level</span>
                            <span class="text-white">{{ ucfirst($course->level) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Category</span>
                            <span class="text-white">{{ ucfirst($course->category) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Created</span>
                            <span class="text-white">{{ $course->created_at->format('M d, Y') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Last Updated</span>
                            <span class="text-white">{{ $course->updated_at->format('M d, Y') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-6">
                    <h3 class="text-lg font-bold text-white mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="{{ route('instructor.learning-materials.create', ['course_id' => $course->id]) }}" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors block text-center">
                            Add Learning Material
                        </a>
                        <a href="{{ route('instructor.ebooks.create', ['course_id' => $course->id]) }}" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors block text-center">
                            Add Ebook
                        </a>
                        <a href="{{ route('instructor.resources.create', ['course_id' => $course->id]) }}" class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors block text-center">
                            Add Resource
                        </a>
                        <form method="POST" action="{{ route('instructor.courses.duplicate', $course) }}" class="w-full">
                            @csrf
                            <button type="submit" class="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg transition-colors">
                                Duplicate Course
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Recent Enrollments -->
                @if($course->enrollments->count() > 0)
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    <h3 class="text-lg font-bold text-white mb-4">Recent Enrollments</h3>
                    <div class="space-y-3">
                        @foreach($course->enrollments->take(5) as $enrollment)
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                    {{ substr($enrollment->user->name, 0, 1) }}
                                </div>
                                <div class="flex-1">
                                    <p class="text-white text-sm">{{ $enrollment->user->name }}</p>
                                    <p class="text-gray-400 text-xs">{{ $enrollment->enrolled_at->diffForHumans() }}</p>
                                </div>
                                <span class="px-2 py-1 bg-{{ $enrollment->status === 'active' ? 'green' : ($enrollment->status === 'completed' ? 'blue' : 'gray') }}-600 text-white rounded text-xs">
                                    {{ ucfirst($enrollment->status) }}
                                </span>
                            </div>
                        @endforeach
                        
                        @if($course->enrollments->count() > 5)
                            <div class="text-center pt-3">
                                <a href="{{ route('instructor.users.index', ['course_id' => $course->id]) }}" class="text-red-400 hover:text-red-300 transition-colors text-sm">
                                    View all students →
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.add('hidden');
    });

    // Remove active state from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('border-red-500', 'text-red-500');
        button.classList.add('border-transparent', 'text-gray-400');
    });

    // Show selected tab content
    const selectedContent = document.getElementById('content-' + tabName);
    if (selectedContent) {
        selectedContent.classList.remove('hidden');
    }

    // Activate selected tab button
    const selectedButton = document.getElementById('tab-' + tabName);
    if (selectedButton) {
        selectedButton.classList.remove('border-transparent', 'text-gray-400');
        selectedButton.classList.add('border-red-500', 'text-red-500');
    }
}

// Initialize with overview tab active
document.addEventListener('DOMContentLoaded', function() {
    showTab('overview');
});
</script>
@endpush
