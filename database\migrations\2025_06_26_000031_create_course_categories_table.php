<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('course_categories', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('icon')->nullable(); // Font Awesome icon class
            $table->string('color')->nullable(); // Hex color code
            $table->string('image')->nullable(); // Category image
            $table->uuid('parent_id')->nullable(); // For subcategories
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->integer('course_count')->default(0); // Auto-calculated
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('parent_id')->references('id')->on('course_categories')->onDelete('cascade');

            // Indexes for performance
            $table->index(['parent_id', 'is_active', 'sort_order']);
            $table->index(['is_active', 'is_featured']);
            $table->index('slug');
            $table->index('course_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('course_categories');
    }
};
