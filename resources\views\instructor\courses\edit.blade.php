@extends('layouts.app')

@section('title', 'Edit Course - ' . $course->title)

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{{ route('instructor.courses.show', $course) }}" 
                           class="text-gray-600 hover:text-gray-800">
                            <i class="fas fa-arrow-left"></i>
                        </a>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">Edit Course</h1>
                            <p class="text-sm text-gray-600">{{ $course->title }}</p>
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{{ route('instructor.courses.show', $course) }}" 
                           class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form action="{{ route('instructor.courses.update', $course) }}" method="POST" enctype="multipart/form-data" class="space-y-8">
            @csrf
            @method('PUT')
            
            <!-- Course Title & Subtitle -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Course Title & Description</h2>
                
                <div class="space-y-4">
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700">Course Title *</label>
                        <input type="text" name="title" id="title" 
                               value="{{ old('title', $course->title) }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                               required maxlength="255">
                        @error('title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="subtitle" class="block text-sm font-medium text-gray-700">Course Subtitle</label>
                        <input type="text" name="subtitle" id="subtitle" 
                               value="{{ old('subtitle', $course->subtitle) }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                               maxlength="255">
                        @error('subtitle')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700">Course Description *</label>
                        <textarea name="description" id="description" rows="6" 
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                  required maxlength="5000">{{ old('description', $course->description) }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Learning Outcomes -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Learning Outcomes</h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">What will students learn?</label>
                        <div id="learning-outcomes-container" class="space-y-2">
                            @if(old('what_you_will_learn', $course->what_you_will_learn))
                                @foreach(old('what_you_will_learn', $course->what_you_will_learn) as $index => $outcome)
                                    <div class="flex items-center space-x-2 learning-outcome-item">
                                        <input type="text" name="what_you_will_learn[]" 
                                               value="{{ $outcome }}"
                                               class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                               placeholder="e.g., Build responsive websites with HTML and CSS"
                                               maxlength="255">
                                        <button type="button" class="remove-outcome text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                @endforeach
                            @else
                                <div class="flex items-center space-x-2 learning-outcome-item">
                                    <input type="text" name="what_you_will_learn[]" 
                                           class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="e.g., Build responsive websites with HTML and CSS"
                                           maxlength="255">
                                    <button type="button" class="remove-outcome text-red-600 hover:text-red-800">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            @endif
                        </div>
                        <button type="button" id="add-learning-outcome" 
                                class="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium">
                            <i class="fas fa-plus mr-1"></i>Add Learning Outcome
                        </button>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Requirements</label>
                        <div id="requirements-container" class="space-y-2">
                            @if(old('requirements', $course->requirements))
                                @foreach(old('requirements', $course->requirements) as $index => $requirement)
                                    <div class="flex items-center space-x-2 requirement-item">
                                        <input type="text" name="requirements[]" 
                                               value="{{ $requirement }}"
                                               class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                               placeholder="e.g., Basic computer skills"
                                               maxlength="255">
                                        <button type="button" class="remove-requirement text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                @endforeach
                            @else
                                <div class="flex items-center space-x-2 requirement-item">
                                    <input type="text" name="requirements[]" 
                                           class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="e.g., Basic computer skills"
                                           maxlength="255">
                                    <button type="button" class="remove-requirement text-red-600 hover:text-red-800">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            @endif
                        </div>
                        <button type="button" id="add-requirement" 
                                class="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium">
                            <i class="fas fa-plus mr-1"></i>Add Requirement
                        </button>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Target Audience</label>
                        <div id="target-audience-container" class="space-y-2">
                            @if(old('target_audience', $course->target_audience))
                                @foreach(old('target_audience', $course->target_audience) as $index => $audience)
                                    <div class="flex items-center space-x-2 target-audience-item">
                                        <input type="text" name="target_audience[]" 
                                               value="{{ $audience }}"
                                               class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                               placeholder="e.g., Beginners who want to learn web development"
                                               maxlength="255">
                                        <button type="button" class="remove-target-audience text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                @endforeach
                            @else
                                <div class="flex items-center space-x-2 target-audience-item">
                                    <input type="text" name="target_audience[]" 
                                           class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="e.g., Beginners who want to learn web development"
                                           maxlength="255">
                                    <button type="button" class="remove-target-audience text-red-600 hover:text-red-800">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            @endif
                        </div>
                        <button type="button" id="add-target-audience" 
                                class="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium">
                            <i class="fas fa-plus mr-1"></i>Add Target Audience
                        </button>
                    </div>
                </div>
            </div>

            <!-- Category & Level -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Category & Level</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="category_id" class="block text-sm font-medium text-gray-700">Category *</label>
                        <select name="category_id" id="category_id" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                required>
                            <option value="">Select a category</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" 
                                        {{ old('category_id', $course->category_id) == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('category_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="subcategory_id" class="block text-sm font-medium text-gray-700">Subcategory</label>
                        <select name="subcategory_id" id="subcategory_id" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select a subcategory</option>
                        </select>
                        @error('subcategory_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="language" class="block text-sm font-medium text-gray-700">Language *</label>
                        <select name="language" id="language" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                required>
                            @foreach($languages as $language)
                                <option value="{{ $language }}" 
                                        {{ old('language', $course->language) == $language ? 'selected' : '' }}>
                                    {{ $language }}
                                </option>
                            @endforeach
                        </select>
                        @error('language')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="level" class="block text-sm font-medium text-gray-700">Level *</label>
                        <select name="level" id="level" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                required>
                            @foreach($levels as $level)
                                <option value="{{ $level }}" 
                                        {{ old('level', $course->level) == $level ? 'selected' : '' }}>
                                    {{ ucfirst(str_replace('_', ' ', $level)) }}
                                </option>
                            @endforeach
                        </select>
                        @error('level')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Pricing -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Pricing</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="price" class="block text-sm font-medium text-gray-700">Price (USD) *</label>
                        <div class="mt-1 relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">$</span>
                            </div>
                            <input type="number" name="price" id="price" 
                                   value="{{ old('price', $course->price) }}"
                                   class="pl-7 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                   step="0.01" min="0" max="999.99" required>
                        </div>
                        @error('price')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="original_price" class="block text-sm font-medium text-gray-700">Original Price (for discounts)</label>
                        <div class="mt-1 relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">$</span>
                            </div>
                            <input type="number" name="original_price" id="original_price" 
                                   value="{{ old('original_price', $course->original_price) }}"
                                   class="pl-7 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                   step="0.01" min="0" max="999.99">
                        </div>
                        @error('original_price')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Course Media -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Course Media</h2>
                
                <div class="space-y-4">
                    <div>
                        <label for="image" class="block text-sm font-medium text-gray-700">Course Thumbnail</label>
                        
                        @if($course->image)
                            <div class="mt-2 mb-4">
                                <img src="{{ Storage::disk('private')->url($course->image) }}" 
                                     alt="Current course image" 
                                     class="w-32 h-24 object-cover rounded border border-gray-300">
                                <p class="text-sm text-gray-600 mt-1">Current image</p>
                            </div>
                        @endif
                        
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="flex text-sm text-gray-600">
                                    <label for="image" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                        <span>{{ $course->image ? 'Replace image' : 'Upload a file' }}</span>
                                        <input id="image" name="image" type="file" class="sr-only" accept="image/*">
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
                            </div>
                        </div>
                        @error('image')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="promotional_video" class="block text-sm font-medium text-gray-700">Promotional Video URL</label>
                        <input type="url" name="promotional_video" id="promotional_video" 
                               value="{{ old('promotional_video', $course->promotional_video) }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                               placeholder="https://youtube.com/watch?v=...">
                        @error('promotional_video')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Submit -->
            <div class="flex justify-between">
                <a href="{{ route('instructor.courses.show', $course) }}" 
                   class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-lg font-medium transition-colors">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-save mr-2"></i>Update Course
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Dynamic field management
    function setupDynamicFields(containerId, addButtonId, removeButtonClass, inputName, placeholder) {
        const container = document.getElementById(containerId);
        const addButton = document.getElementById(addButtonId);
        
        addButton.addEventListener('click', function() {
            const newItem = document.createElement('div');
            newItem.className = 'flex items-center space-x-2 ' + containerId.replace('-container', '-item');
            newItem.innerHTML = `
                <input type="text" name="${inputName}[]" 
                       class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                       placeholder="${placeholder}"
                       maxlength="255">
                <button type="button" class="${removeButtonClass} text-red-600 hover:text-red-800">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            container.appendChild(newItem);
            
            // Add event listener to the new remove button
            newItem.querySelector('.' + removeButtonClass).addEventListener('click', function() {
                newItem.remove();
            });
        });
        
        // Add event listeners to existing remove buttons
        container.addEventListener('click', function(e) {
            if (e.target.closest('.' + removeButtonClass)) {
                const item = e.target.closest('.' + containerId.replace('-container', '-item'));
                if (container.children.length > 1) {
                    item.remove();
                }
            }
        });
    }
    
    // Setup dynamic fields
    setupDynamicFields('learning-outcomes-container', 'add-learning-outcome', 'remove-outcome', 'what_you_will_learn', 'e.g., Build responsive websites with HTML and CSS');
    setupDynamicFields('requirements-container', 'add-requirement', 'remove-requirement', 'requirements', 'e.g., Basic computer skills');
    setupDynamicFields('target-audience-container', 'add-target-audience', 'remove-target-audience', 'target_audience', 'e.g., Beginners who want to learn web development');
    
    // Category/subcategory handling
    const categorySelect = document.getElementById('category_id');
    const subcategorySelect = document.getElementById('subcategory_id');
    
    const subcategories = @json($categories->mapWithKeys(function($category) {
        return [$category->id => $category->activeChildren];
    }));
    
    function updateSubcategories() {
        const categoryId = categorySelect.value;
        subcategorySelect.innerHTML = '<option value="">Select a subcategory</option>';
        
        if (categoryId && subcategories[categoryId]) {
            subcategories[categoryId].forEach(function(subcategory) {
                const option = document.createElement('option');
                option.value = subcategory.id;
                option.textContent = subcategory.name;
                subcategorySelect.appendChild(option);
            });
        }
    }
    
    categorySelect.addEventListener('change', updateSubcategories);
    
    // Initialize subcategories on page load
    updateSubcategories();
    
    // Set selected subcategory if exists
    const selectedSubcategory = '{{ old("subcategory_id", $course->subcategory_id) }}';
    if (selectedSubcategory) {
        setTimeout(() => {
            subcategorySelect.value = selectedSubcategory;
        }, 100);
    }
});
</script>
@endpush
