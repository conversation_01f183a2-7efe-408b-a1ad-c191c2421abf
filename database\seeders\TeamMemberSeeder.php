<?php

namespace Database\Seeders;

use App\Models\TeamMember;
use Illuminate\Database\Seeder;

class TeamMemberSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $teamMembers = [
            [
                'name' => '<PERSON>',
                'role' => 'Founder & CEO',
                'bio' => 'Visionary entrepreneur with 15+ years of experience in technology and education. Passionate about helping others break free from conventional limitations.',
                'email' => '<EMAIL>',
                'linkedin_url' => 'https://linkedin.com/in/alexrodriguez',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => '<PERSON>',
                'role' => 'Head of Technology',
                'bio' => 'Full-stack developer and AI specialist with expertise in cutting-edge technologies. Leads our technical curriculum development.',
                'email' => '<EMAIL>',
                'linkedin_url' => 'https://linkedin.com/in/sarahchen',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => '<PERSON>',
                'role' => 'Business Strategy Mentor',
                'bio' => 'Serial entrepreneur and business consultant who has built multiple 7-figure companies. Specializes in scaling and growth strategies.',
                'email' => '<EMAIL>',
                'linkedin_url' => 'https://linkedin.com/in/marcusjohnson',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Dr. Emily Watson',
                'role' => 'Mindset & Psychology Expert',
                'bio' => 'Licensed psychologist and mindset coach specializing in breakthrough psychology and limiting belief transformation.',
                'email' => '<EMAIL>',
                'linkedin_url' => 'https://linkedin.com/in/emilywatson',
                'is_active' => true,
                'sort_order' => 4,
            ],
        ];

        foreach ($teamMembers as $memberData) {
            TeamMember::firstOrCreate(
                ['email' => $memberData['email']],
                $memberData
            );
        }

        $this->command->info('Team members seeded successfully!');
    }
}