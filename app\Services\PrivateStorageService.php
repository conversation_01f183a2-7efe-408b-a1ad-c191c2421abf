<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class PrivateStorageService
{
    /**
     * Material type directories mapping
     */
    const MATERIAL_TYPES = [
        'learning-materials' => 'learning-materials',
        'ebooks' => 'ebooks',
        'videos' => 'videos',
        'resources' => 'resources',
        'content-files' => 'content-files',
    ];

    /**
     * Store a file in the private storage with user and course organization
     */
    public function storeFile(
        UploadedFile $file, 
        string $userId, 
        string $courseId, 
        string $materialType
    ): array {
        // Validate material type
        if (!array_key_exists($materialType, self::MATERIAL_TYPES)) {
            throw new \InvalidArgumentException("Invalid material type: {$materialType}");
        }

        // Create the directory path
        $directory = $this->getPrivateDirectory($userId, $courseId, $materialType);
        
        // Generate unique filename to prevent conflicts
        $filename = $this->generateUniqueFilename($file);
        
        // Store the file in private storage
        $path = $file->storeAs($directory, $filename, 'private');
        
        return [
            'path' => $path,
            'filename' => $filename,
            'original_name' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
        ];
    }

    /**
     * Get the private directory path for a user's course materials
     */
    public function getPrivateDirectory(string $userId, string $courseId, string $materialType): string
    {
        $materialDir = self::MATERIAL_TYPES[$materialType];
        return "private/{$userId}/{$courseId}/materials/{$materialDir}";
    }

    /**
     * Generate a unique filename to prevent conflicts
     */
    private function generateUniqueFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $baseName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        
        // Sanitize the base name
        $baseName = Str::slug($baseName);
        
        // Add timestamp and unique ID
        $uniqueId = time() . '_' . uniqid();
        
        return "{$baseName}_{$uniqueId}.{$extension}";
    }

    /**
     * Create directory structure if it doesn't exist
     */
    public function ensureDirectoryExists(string $userId, string $courseId, string $materialType): void
    {
        $directory = $this->getPrivateDirectory($userId, $courseId, $materialType);
        
        if (!Storage::disk('private')->exists($directory)) {
            Storage::disk('private')->makeDirectory($directory);
        }
    }

    /**
     * Delete a file from private storage
     */
    public function deleteFile(string $filePath): bool
    {
        if (Storage::disk('private')->exists($filePath)) {
            return Storage::disk('private')->delete($filePath);
        }
        
        return false;
    }

    /**
     * Check if a file exists in private storage
     */
    public function fileExists(string $filePath): bool
    {
        return Storage::disk('private')->exists($filePath);
    }

    /**
     * Get file contents from private storage
     */
    public function getFileContents(string $filePath): string
    {
        return Storage::disk('private')->get($filePath);
    }

    /**
     * Get file size from private storage
     */
    public function getFileSize(string $filePath): int
    {
        return Storage::disk('private')->size($filePath);
    }

    /**
     * Move file from public to private storage
     */
    public function moveToPrivateStorage(
        string $publicPath, 
        string $userId, 
        string $courseId, 
        string $materialType,
        string $originalName
    ): array {
        // Get the file contents from public storage
        $contents = Storage::disk('public')->get($publicPath);
        
        // Create the private directory path
        $directory = $this->getPrivateDirectory($userId, $courseId, $materialType);
        
        // Generate unique filename
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $baseName = pathinfo($originalName, PATHINFO_FILENAME);
        $baseName = Str::slug($baseName);
        $uniqueId = time() . '_' . uniqid();
        $filename = "{$baseName}_{$uniqueId}.{$extension}";
        
        // Store in private storage
        $privatePath = "{$directory}/{$filename}";
        Storage::disk('private')->put($privatePath, $contents);
        
        // Delete from public storage
        Storage::disk('public')->delete($publicPath);
        
        return [
            'path' => $privatePath,
            'filename' => $filename,
            'original_name' => $originalName,
            'size' => strlen($contents),
        ];
    }

    /**
     * Verify user has access to a file
     */
    public function verifyFileAccess(string $filePath, string $userId): bool
    {
        // Check if the file path starts with the user's private directory
        return Str::startsWith($filePath, "private/{$userId}/");
    }

    /**
     * Get all material types
     */
    public static function getMaterialTypes(): array
    {
        return self::MATERIAL_TYPES;
    }

    /**
     * Create all necessary directories for a new course
     */
    public function createCourseDirectories(string $userId, string $courseId): void
    {
        foreach (self::MATERIAL_TYPES as $materialType => $directory) {
            $this->ensureDirectoryExists($userId, $courseId, $materialType);
        }
    }
}
