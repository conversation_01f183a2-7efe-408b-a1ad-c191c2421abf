<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lectures', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('title');
            $table->string('slug');
            $table->text('description')->nullable();
            $table->enum('type', ['video', 'text', 'quiz', 'assignment', 'resource', 'live_session']);
            $table->uuid('chapter_id');
            $table->uuid('course_id'); // Denormalized for performance
            $table->uuid('instructor_id'); // Denormalized for performance
            $table->integer('sort_order')->default(0);
            $table->boolean('is_published')->default(false);
            $table->boolean('is_free_preview')->default(false);
            $table->integer('duration_minutes')->default(0);
            
            // Content fields based on type
            $table->longText('content')->nullable(); // For text content
            $table->string('video_url')->nullable(); // For video lectures
            $table->string('video_provider')->nullable(); // youtube, vimeo, self_hosted
            $table->string('video_id')->nullable(); // Provider-specific ID
            $table->string('video_thumbnail')->nullable();
            $table->json('video_metadata')->nullable(); // Duration, quality, etc.
            
            // Resource/file fields
            $table->json('resources')->nullable(); // Array of downloadable resources
            $table->json('attachments')->nullable(); // Array of file attachments
            
            // Quiz/Assignment fields
            $table->json('quiz_data')->nullable(); // Quiz questions and answers
            $table->integer('quiz_passing_score')->nullable();
            $table->boolean('quiz_allow_retakes')->default(true);
            
            // Completion tracking
            $table->boolean('is_mandatory')->default(true);
            $table->integer('estimated_completion_minutes')->nullable();
            
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('chapter_id')->references('id')->on('chapters')->onDelete('cascade');
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('cascade');
            $table->foreign('instructor_id')->references('id')->on('users')->onDelete('cascade');

            // Indexes for performance
            $table->index(['chapter_id', 'sort_order']);
            $table->index(['course_id', 'is_published']);
            $table->index(['instructor_id', 'is_published']);
            $table->index(['type', 'is_published']);
            $table->index('slug');
            
            // Unique constraint for slug within chapter
            $table->unique(['chapter_id', 'slug']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lectures');
    }
};
