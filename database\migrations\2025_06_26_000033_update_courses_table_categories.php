<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            // Add foreign key references to categories
            $table->uuid('category_id')->nullable()->after('category');
            $table->uuid('subcategory_id')->nullable()->after('subcategory');
            
            // Add foreign key constraints
            $table->foreign('category_id')->references('id')->on('course_categories')->onDelete('set null');
            $table->foreign('subcategory_id')->references('id')->on('course_categories')->onDelete('set null');
            
            // Add indexes
            $table->index(['category_id', 'status']);
            $table->index(['subcategory_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            $table->dropForeign(['category_id']);
            $table->dropForeign(['subcategory_id']);
            $table->dropIndex(['category_id', 'status']);
            $table->dropIndex(['subcategory_id', 'status']);
            $table->dropColumn(['category_id', 'subcategory_id']);
        });
    }
};
