@extends('instructor.layouts.app')

@section('title', 'Video Content - Instructor Dashboard')

@section('content')
<div class="py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">Video Content</h1>
                <p class="text-gray-400">Manage your video library and course content</p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="{{ route('instructor.videos.create') }}" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Upload New Video
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-8">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-300 mb-2">Search</label>
                    <input type="text" name="search" id="search" value="{{ request('search') }}" 
                           class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                           placeholder="Search videos...">
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                    <select name="status" id="status" 
                            class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
                        <option value="">All Statuses</option>
                        <option value="1" {{ request('status') === '1' ? 'selected' : '' }}>Published</option>
                        <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>Draft</option>
                    </select>
                </div>

                <!-- Course Filter -->
                <div>
                    <label for="course_id" class="block text-sm font-medium text-gray-300 mb-2">Course</label>
                    <select name="course_id" id="course_id" 
                            class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
                        <option value="">All Courses</option>
                        @foreach($courses as $course)
                            <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>{{ $course->title }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Filter Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Filter
                    </button>
                </div>
            </form>
        </div>

        <!-- Videos Grid -->
        @if($videos->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($videos as $video)
                    <div class="bg-gray-900 border border-gray-800 rounded-lg overflow-hidden hover:border-red-500 transition-all duration-300">
                        <div class="relative">
                            @if($video->thumbnail_url)
                                <img src="{{ $video->thumbnail_url }}" alt="{{ $video->title }}" class="w-full h-48 object-cover">
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            <!-- Play Button Overlay -->
                            <div class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity">
                                <div class="bg-red-600 rounded-full p-4">
                                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M8 5v14l11-7z"/>
                                    </svg>
                                </div>
                            </div>
                            
                            <!-- Status Badge -->
                            <div class="absolute top-4 left-4">
                                @if($video->status === 'published')
                                    <span class="bg-green-600 text-white px-2 py-1 rounded text-sm">Published</span>
                                @else
                                    <span class="bg-yellow-600 text-white px-2 py-1 rounded text-sm">Draft</span>
                                @endif
                            </div>

                            <!-- Duration Badge -->
                            @if($video->duration_seconds)
                                <div class="absolute bottom-4 right-4">
                                    <span class="bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">{{ gmdate('i:s', $video->duration_seconds) }}</span>
                                </div>
                            @endif
                        </div>

                        <div class="p-6">
                            <h3 class="text-xl font-bold text-white mb-2 line-clamp-2">{{ $video->title }}</h3>
                            @if($video->description)
                                <p class="text-gray-400 mb-4 line-clamp-3">{{ $video->description }}</p>
                            @endif

                            <!-- Video Meta -->
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <span>{{ $video->created_at->format('M d, Y') }}</span>
                                @if($video->course)
                                    <span class="text-blue-400">{{ $video->course->title }}</span>
                                @endif
                            </div>

                            <!-- Video Type -->
                            <div class="flex items-center gap-2 mb-4">
                                @if($video->youtube_url)
                                    <span class="bg-red-600 text-white px-2 py-1 rounded text-xs">YouTube Video</span>
                                @endif
                                @if($video->category)
                                    <span class="bg-purple-600 text-white px-2 py-1 rounded text-xs">{{ ucfirst($video->category) }}</span>
                                @endif
                            </div>

                            <!-- Actions -->
                            <div class="flex space-x-2">
                                <a href="{{ route('instructor.videos.show', $video) }}" 
                                   class="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg text-center transition-colors">
                                    View
                                </a>
                                <a href="{{ route('instructor.videos.edit', $video) }}" 
                                   class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                                    Edit
                                </a>
                                <form method="POST" action="{{ route('instructor.videos.toggle-status', $video) }}" class="inline">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit"
                                            class="{{ $video->status === 'published' ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-green-600 hover:bg-green-700' }} text-white py-2 px-4 rounded-lg transition-colors">
                                        {{ $video->status === 'published' ? 'Hide' : 'Publish' }}
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            @if($videos->hasPages())
                <div class="mt-8 flex justify-center">
                    {{ $videos->links() }}
                </div>
            @endif
        @else
            <!-- No Videos -->
            <div class="text-center py-16">
                <div class="text-6xl mb-6">🎥</div>
                <h3 class="text-2xl font-bold text-white mb-4">No Videos Found</h3>
                <p class="text-gray-400 mb-8 max-w-md mx-auto">
                    @if(request()->hasAny(['search', 'status', 'course_id']))
                        No videos match your current filters. Try adjusting your search criteria.
                    @else
                        You haven't uploaded any videos yet. Start building your video library to enhance your courses.
                    @endif
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('instructor.videos.create') }}" class="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                        Upload Your First Video
                    </a>
                    @if(request()->hasAny(['search', 'status', 'course_id']))
                        <a href="{{ route('instructor.videos.index') }}" class="border border-gray-600 text-gray-300 hover:bg-gray-800 px-8 py-3 rounded-lg font-semibold transition-colors">
                            Clear Filters
                        </a>
                    @endif
                </div>
            </div>
        @endif
    </div>
</div>

@push('styles')
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endpush
@endsection
