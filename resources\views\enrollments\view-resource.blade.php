@extends('layouts.app')

@section('title', $resource->title . ' - ' . $course->title)

@section('content')
<div class="min-h-screen bg-black">
    <!-- Header -->
    <div class="bg-gray-900 border-b border-gray-800">
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center space-x-4 mb-4">
                <a href="{{ route('my-courses.view', $course) }}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Course
                </a>
            </div>
            
            <div class="flex items-start space-x-6">
                <div class="flex-shrink-0">
                    <div class="w-16 h-16 bg-gray-800 rounded-lg flex items-center justify-center text-3xl">
                        @if($resource->type === 'link')
                            🔗
                        @elseif($resource->hasFile())
                            @switch(strtolower(pathinfo($resource->file_name ?? '', PATHINFO_EXTENSION)))
                                @case('pdf')
                                    📄
                                    @break
                                @case('doc')
                                @case('docx')
                                    📝
                                    @break
                                @case('xls')
                                @case('xlsx')
                                    📊
                                    @break
                                @case('ppt')
                                @case('pptx')
                                    📋
                                    @break
                                @case('zip')
                                @case('rar')
                                    📦
                                    @break
                                @case('jpg')
                                @case('jpeg')
                                @case('png')
                                @case('gif')
                                    🖼️
                                    @break
                                @case('mp4')
                                @case('avi')
                                @case('mov')
                                    🎥
                                    @break
                                @case('mp3')
                                @case('wav')
                                    🎵
                                    @break
                                @default
                                    📎
                            @endswitch
                        @else
                            📎
                        @endif
                    </div>
                </div>
                
                <div class="flex-1">
                    <h1 class="text-3xl font-bold text-white mb-2">{{ $resource->title }}</h1>
                    <p class="text-gray-400 mb-2">{{ ucfirst($resource->type) }} Resource</p>
                    @if($resource->description)
                        <p class="text-gray-300 mb-4">{{ $resource->description }}</p>
                    @endif
                    
                    <div class="flex items-center space-x-4 text-sm text-gray-400">
                        @if($resource->hasFile() && $resource->formatted_file_size)
                            <span>Size: {{ $resource->formatted_file_size }}</span>
                        @endif
                        @if($resource->hasFile() && $resource->file_name)
                            <span>File: {{ $resource->file_name }}</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    @if($resource->type === 'link' && $resource->url)
                        <!-- External Link Preview -->
                        <div class="mb-6">
                            <h3 class="text-xl font-bold text-white mb-4">External Resource</h3>
                            <div class="bg-gray-800 rounded-lg p-6 text-center">
                                <div class="text-4xl mb-4">🔗</div>
                                <h4 class="text-lg font-semibold text-white mb-2">{{ $resource->title }}</h4>
                                <p class="text-gray-400 mb-4">This resource links to an external website.</p>
                                <a href="{{ $resource->url }}" target="_blank" rel="noopener noreferrer"
                                   class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                    </svg>
                                    Visit Resource
                                </a>
                            </div>
                        </div>
                    @elseif($resource->hasFile())
                        <!-- File Preview -->
                        <div class="mb-6">
                            <h3 class="text-xl font-bold text-white mb-4">File Preview</h3>
                            <div class="bg-gray-800 rounded-lg p-4">
                                @if(str_contains($resource->mime_type ?? '', 'pdf'))
                                    <!-- PDF Viewer -->
                                    <iframe src="{{ $resource->file_url }}" 
                                            class="w-full h-96 rounded border border-gray-700"
                                            title="{{ $resource->title }}">
                                        <p class="text-gray-400">Your browser doesn't support PDF viewing. 
                                           <a href="{{ $resource->download_url }}" class="text-red-500 hover:text-red-400">Download the file</a> instead.
                                        </p>
                                    </iframe>
                                @elseif(str_contains($resource->mime_type ?? '', 'image'))
                                    <!-- Image Preview -->
                                    <div class="text-center">
                                        <img src="{{ $resource->file_url }}" alt="{{ $resource->title }}" 
                                             class="max-w-full h-auto rounded-lg shadow-lg mx-auto">
                                    </div>
                                @else
                                    <!-- Generic File Preview -->
                                    <div class="text-center py-12">
                                        <div class="text-6xl mb-4">
                                            @switch(strtolower(pathinfo($resource->file_name ?? '', PATHINFO_EXTENSION)))
                                                @case('doc')
                                                @case('docx')
                                                    📝
                                                    @break
                                                @case('xls')
                                                @case('xlsx')
                                                    📊
                                                    @break
                                                @case('ppt')
                                                @case('pptx')
                                                    📋
                                                    @break
                                                @case('zip')
                                                @case('rar')
                                                    📦
                                                    @break
                                                @case('mp4')
                                                @case('avi')
                                                @case('mov')
                                                    🎥
                                                    @break
                                                @case('mp3')
                                                @case('wav')
                                                    🎵
                                                    @break
                                                @default
                                                    📎
                                            @endswitch
                                        </div>
                                        <h4 class="text-lg font-semibold text-white mb-2">{{ $resource->file_name }}</h4>
                                        <p class="text-gray-400 mb-4">{{ $resource->formatted_file_size ?? 'File' }}</p>
                                        <p class="text-gray-500 text-sm">Preview not available for this file type</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif

                    @if($resource->description)
                        <div class="mb-6">
                            <h3 class="text-xl font-bold text-white mb-4">Description</h3>
                            <div class="prose prose-invert max-w-none">
                                <p class="text-gray-300">{{ $resource->description }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Action Options -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-6">
                    <h3 class="text-xl font-bold text-white mb-4">Actions</h3>
                    <div class="space-y-3">
                        @if($resource->type === 'link' && $resource->url)
                            <a href="{{ $resource->url }}" target="_blank" rel="noopener noreferrer"
                               class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                Visit Link
                            </a>
                        @endif
                        
                        @if($resource->hasFile())
                            <a href="{{ route('courses.resources.download', [$course, $resource]) }}" 
                               class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition-colors flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m-6 4h8"></path>
                                </svg>
                                Download File
                            </a>
                            
                            <a href="{{ $resource->file_url }}" target="_blank"
                               class="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md transition-colors flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                View File
                            </a>
                        @endif
                    </div>
                </div>

                <!-- Resource Details -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-6">
                    <h3 class="text-xl font-bold text-white mb-4">Resource Details</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Type</span>
                            <span class="text-white">{{ ucfirst($resource->type) }}</span>
                        </div>
                        @if($resource->hasFile())
                            @if($resource->file_name)
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Filename</span>
                                    <span class="text-white text-sm">{{ $resource->file_name }}</span>
                                </div>
                            @endif
                            @if($resource->formatted_file_size)
                                <div class="flex justify-between">
                                    <span class="text-gray-400">File Size</span>
                                    <span class="text-white">{{ $resource->formatted_file_size }}</span>
                                </div>
                            @endif
                            @if($resource->mime_type)
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Format</span>
                                    <span class="text-white">{{ strtoupper(pathinfo($resource->file_name ?? '', PATHINFO_EXTENSION)) ?: 'File' }}</span>
                                </div>
                            @endif
                        @endif
                    </div>
                </div>

                <!-- Course Info -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    <h3 class="text-xl font-bold text-white mb-4">Course</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Title</span>
                            <span class="text-white">{{ $course->title }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Instructor</span>
                            <span class="text-white">{{ $course->instructor->name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Category</span>
                            <span class="text-white">{{ $course->category }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
