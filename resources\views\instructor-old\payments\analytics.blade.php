@extends('instructor.layouts.app')

@section('title', 'Payment Analytics - Instructor Dashboard')

@section('content')
<div class="py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-white mb-2">Payment Analytics</h1>
            <p class="text-gray-400">Track your earnings, revenue trends, and payment performance</p>
        </div>

        <!-- Time Period Filter -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-8">
            <form method="GET" class="flex flex-wrap items-end gap-4">
                <div>
                    <label for="period" class="block text-sm font-medium text-gray-300 mb-2">Time Period</label>
                    <select name="period" id="period" 
                            class="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
                        <option value="7" {{ request('period', '30') == '7' ? 'selected' : '' }}>Last 7 days</option>
                        <option value="30" {{ request('period', '30') == '30' ? 'selected' : '' }}>Last 30 days</option>
                        <option value="90" {{ request('period', '30') == '90' ? 'selected' : '' }}>Last 90 days</option>
                        <option value="365" {{ request('period', '30') == '365' ? 'selected' : '' }}>Last year</option>
                        <option value="custom" {{ request('period', '30') == 'custom' ? 'selected' : '' }}>Custom range</option>
                    </select>
                </div>

                <div id="customDateRange" class="flex gap-4 {{ request('period') != 'custom' ? 'hidden' : '' }}">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-300 mb-2">Start Date</label>
                        <input type="date" name="start_date" id="start_date" value="{{ request('start_date') }}" 
                               class="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
                    </div>
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-300 mb-2">End Date</label>
                        <input type="date" name="end_date" id="end_date" value="{{ request('end_date') }}" 
                               class="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
                    </div>
                </div>

                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    Update Analytics
                </button>
            </form>
        </div>

        <!-- Key Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">Current Month</h3>
                    <div class="bg-green-600 p-2 rounded-lg">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                </div>
                <div class="text-3xl font-bold text-white mb-2">${{ number_format($currentMonth, 2) }}</div>
                <div class="text-sm text-gray-400">
                    @if($monthlyGrowth > 0)
                        <span class="text-green-400">+{{ number_format($monthlyGrowth, 1) }}%</span>
                    @elseif($monthlyGrowth < 0)
                        <span class="text-red-400">{{ number_format($monthlyGrowth, 1) }}%</span>
                    @else
                        <span class="text-gray-400">No change</span>
                    @endif
                    from last month
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">Previous Month</h3>
                    <div class="bg-blue-600 p-2 rounded-lg">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                        </svg>
                    </div>
                </div>
                <div class="text-3xl font-bold text-white mb-2">${{ number_format($previousMonth, 2) }}</div>
                <div class="text-sm text-gray-400">
                    Last month's revenue
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">Total Courses</h3>
                    <div class="bg-yellow-600 p-2 rounded-lg">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="text-3xl font-bold text-white mb-2">{{ $revenueByCourse->count() }}</div>
                <div class="text-sm text-gray-400">
                    Courses with sales
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">Growth Rate</h3>
                    <div class="bg-purple-600 p-2 rounded-lg">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                </div>
                <div class="text-3xl font-bold text-white mb-2">
                    @if($monthlyGrowth > 0)
                        <span class="text-green-400">+{{ number_format($monthlyGrowth, 1) }}%</span>
                    @elseif($monthlyGrowth < 0)
                        <span class="text-red-400">{{ number_format($monthlyGrowth, 1) }}%</span>
                    @else
                        <span class="text-gray-400">0%</span>
                    @endif
                </div>
                <div class="text-sm text-gray-400">
                    Month-over-month growth
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Revenue Chart -->
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <h3 class="text-xl font-bold text-white mb-6">Revenue Trend</h3>
                <div class="h-64 flex items-center justify-center">
                    <div class="text-center">
                        <div class="text-4xl mb-4">📈</div>
                        <p class="text-gray-400">Revenue chart would be displayed here</p>
                        <p class="text-gray-500 text-sm">Integration with Chart.js or similar library needed</p>
                    </div>
                </div>
            </div>

            <!-- Top Courses -->
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <h3 class="text-xl font-bold text-white mb-6">Top Performing Courses</h3>
                <div class="space-y-4">
                    @forelse($revenueByCourse->take(5) as $courseData)
                        <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                            <div class="flex-1">
                                <h4 class="text-white font-medium">{{ $courseData->course->title }}</h4>
                                <p class="text-gray-400 text-sm">{{ $courseData->sales }} sales</p>
                            </div>
                            <div class="text-right">
                                <div class="text-white font-bold">${{ number_format($courseData->revenue, 0) }}</div>
                                <div class="text-gray-400 text-sm">{{ number_format(($courseData->revenue / $revenueByCourse->sum('revenue')) * 100, 1) }}%</div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <div class="text-4xl mb-3">📊</div>
                            <p class="text-gray-400">No course sales data available for this period</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Payment Methods & Refunds -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Payment Methods -->
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <h3 class="text-xl font-bold text-white mb-6">Payment Methods</h3>
                <div class="space-y-4">
                    @forelse($paymentMethods as $method)
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                                    @if($method->payment_method === 'paypal')
                                        <span class="text-white text-xs font-bold">PP</span>
                                    @else
                                        <span class="text-white text-xs font-bold">CC</span>
                                    @endif
                                </div>
                                <span class="text-white">{{ ucfirst($method->payment_method ?? 'Unknown') }}</span>
                            </div>
                            <div class="text-right">
                                <div class="text-white">${{ number_format($method->revenue, 0) }}</div>
                                <div class="text-gray-400 text-sm">{{ $method->count }} transactions</div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <div class="text-4xl mb-3">💳</div>
                            <p class="text-gray-400">No payment method data available</p>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- Revenue Over Time -->
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <h3 class="text-xl font-bold text-white mb-6">Revenue Over Time</h3>
                <div class="space-y-4">
                    @forelse($revenueOverTime->take(10) as $data)
                        <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                            <div class="flex-1">
                                <h4 class="text-white font-medium">{{ $data->period }}</h4>
                                <p class="text-gray-400 text-sm">{{ $data->transactions }} transactions</p>
                            </div>
                            <div class="text-right">
                                <div class="text-white font-bold">${{ number_format($data->revenue, 2) }}</div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <div class="text-4xl mb-3">📈</div>
                            <p class="text-gray-400">No revenue data available for this period</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Export Options -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
            <h3 class="text-xl font-bold text-white mb-4">Export Data</h3>
            <p class="text-gray-400 mb-6">Download your payment data for accounting or tax purposes</p>
            <div class="flex flex-wrap gap-4">
                <a href="{{ route('instructor.payments.export', ['format' => 'csv', 'period' => request('period', '30')]) }}" 
                   class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export CSV
                </a>
                <a href="{{ route('instructor.payments.export', ['format' => 'pdf', 'period' => request('period', '30')]) }}" 
                   class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export PDF
                </a>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Handle period selection
    document.getElementById('period').addEventListener('change', function() {
        const customRange = document.getElementById('customDateRange');
        if (this.value === 'custom') {
            customRange.classList.remove('hidden');
        } else {
            customRange.classList.add('hidden');
        }
    });
</script>
@endpush
@endsection
