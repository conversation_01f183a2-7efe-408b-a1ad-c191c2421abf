@extends('layouts.app')

@section('title', 'Verify Email - Escape Matrix Academy')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-black via-gray-900 to-black py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-white">
                Verify Your Email
            </h2>
            <p class="mt-2 text-center text-sm text-gray-400">
                We've sent a verification link to your email address
            </p>
        </div>

        @if (session('status') == 'verification-link-sent')
            <div class="bg-green-900/50 border border-green-500 text-green-200 px-4 py-3 rounded relative" role="alert">
                A new verification link has been sent to your email address.
            </div>
        @endif

        <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <div class="flex items-center justify-center mb-4">
                <svg class="w-12 h-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
            </div>
            
            <p class="text-center text-gray-300 mb-6">
                Before continuing, please check your email for a verification link. If you didn't receive the email, we can send you another one.
            </p>

            <form method="POST" action="{{ route('verification.send') }}">
                @csrf
                <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors">
                    Resend Verification Email
                </button>
            </form>
        </div>

        <div class="text-center">
            <form method="POST" action="{{ route('logout') }}">
                @csrf
                <button type="submit" class="text-sm text-gray-400 hover:text-white transition-colors">
                    Sign out
                </button>
            </form>
        </div>
    </div>
</div>
@endsection
