<?php

namespace Tests\Feature;

use App\Models\Course;
use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class CourseImageAccessDebugTest extends TestCase
{
    use RefreshDatabase;

    protected User $instructor;
    protected User $student;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        $instructorRole = Role::create([
            'name' => 'instructor',
            'display_name' => 'Instructor',
            'description' => 'Course instructor',
            'priority' => 3,
            'is_active' => true,
        ]);

        $studentRole = Role::create([
            'name' => 'student',
            'display_name' => 'Student',
            'description' => 'Student',
            'priority' => 1,
            'is_active' => true,
        ]);

        // Create users
        $this->instructor = User::factory()->create();
        $this->instructor->roles()->attach($instructorRole);

        $this->student = User::factory()->create();
        $this->student->roles()->attach($studentRole);

        // Set up storage disks
        Storage::fake('private');
    }

    /** @test */
    public function debug_course_image_access_control()
    {
        // Create a course with private image
        $course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'published'
        ]);

        $imagePath = "private/{$this->instructor->id}/{$course->id}/course-images/test.jpg";
        $course->update(['image' => $imagePath]);

        // Create the actual file in storage
        Storage::disk('private')->put($imagePath, 'fake image content');

        // Debug: Check course and user data
        $this->assertNotNull($course);
        $this->assertEquals('published', $course->status);
        $this->assertEquals($this->instructor->id, $course->instructor_id);
        
        // Debug: Check if file exists
        $this->assertTrue(Storage::disk('private')->exists($imagePath));

        // Test 1: Published course images should be accessible to authenticated users
        $this->actingAs($this->student);
        $response = $this->get(route('secure.files.serve', ['filePath' => $imagePath]));
        
        if ($response->getStatusCode() !== 200) {
            dump('Test 1 Failed - Student accessing published course image');
            dump('Response status: ' . $response->getStatusCode());
            dump('Course status: ' . $course->status);
            dump('Student ID: ' . $this->student->id);
            dump('Instructor ID: ' . $this->instructor->id);
            dump('Image path: ' . $imagePath);
        }

        $response->assertStatus(200);

        // Test 2: Change course to draft - should deny access to student
        $course->update(['status' => 'draft']);
        $course->refresh();

        $response = $this->get(route('secure.files.serve', ['filePath' => $imagePath]));

        if ($response->getStatusCode() !== 403) {
            dump('Test 2 Failed - Student accessing draft course image');
            dump('Response status: ' . $response->getStatusCode());
            dump('Course status: ' . $course->status);
            dump('Student ID: ' . $this->student->id);
            dump('Instructor ID: ' . $this->instructor->id);
            dump('Image path: ' . $imagePath);
        }

        $response->assertStatus(403);

        // Test 3: Instructor should still have access to draft course
        $this->actingAs($this->instructor);
        $response = $this->get(route('secure.files.serve', ['filePath' => $imagePath]));

        if ($response->getStatusCode() !== 200) {
            dump('Test 3 Failed - Instructor accessing own draft course image');
            dump('Response status: ' . $response->getStatusCode());
            dump('Course status: ' . $course->status);
            dump('Instructor ID: ' . $this->instructor->id);
            dump('Image path: ' . $imagePath);
        }

        $response->assertStatus(200);
    }
}
