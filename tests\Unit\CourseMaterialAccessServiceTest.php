<?php

namespace Tests\Unit;

use App\Models\Course;
use App\Models\Enrollment;
use App\Models\LearningMaterial;
use App\Models\Payment;
use App\Models\User;
use App\Services\CourseMaterialAccessService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CourseMaterialAccessServiceTest extends TestCase
{
    use RefreshDatabase;

    protected CourseMaterialAccessService $accessService;
    protected $student;
    protected $instructor;
    protected $admin;
    protected $superadmin;
    protected $course;
    protected $material;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->accessService = new CourseMaterialAccessService();
        
        // Seed RBAC system
        $this->artisan('db:seed', ['--class' => 'CompleteRbacSeeder']);
        
        // Create test users
        $this->student = User::where('email', '<EMAIL>')->first();
        $this->instructor = User::where('email', '<EMAIL>')->first();
        $this->admin = User::where('email', '<EMAIL>')->first();
        $this->superadmin = User::where('email', '<EMAIL>')->first();
        
        // Create test course
        $this->course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'price' => 99.99,
            'status' => 'published',
        ]);

        // Create test learning material
        $this->material = LearningMaterial::factory()->create([
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'is_published' => true,
        ]);
    }

    /** @test */
    public function superadmin_has_access_to_all_courses()
    {
        $result = $this->accessService->checkAccess($this->superadmin, $this->course->id);
        
        $this->assertTrue($result['allowed']);
        $this->assertEquals('superadmin_access', $result['reason']);
    }

    /** @test */
    public function admin_has_access_to_all_courses()
    {
        $result = $this->accessService->checkAccess($this->admin, $this->course->id);
        
        $this->assertTrue($result['allowed']);
        $this->assertEquals('admin_access', $result['reason']);
    }

    /** @test */
    public function instructor_has_access_to_own_courses()
    {
        $result = $this->accessService->checkAccess($this->instructor, $this->course->id);
        
        $this->assertTrue($result['allowed']);
        $this->assertEquals('instructor_access', $result['reason']);
    }

    /** @test */
    public function instructor_cannot_access_other_instructors_courses()
    {
        $otherInstructor = User::factory()->create();
        $otherInstructor->assignRole('instructor');
        
        $result = $this->accessService->checkAccess($otherInstructor, $this->course->id);
        
        $this->assertFalse($result['allowed']);
    }

    /** @test */
    public function student_without_enrollment_cannot_access_paid_course()
    {
        $result = $this->accessService->checkAccess($this->student, $this->course->id);
        
        $this->assertFalse($result['allowed']);
        $this->assertEquals('payment_required', $result['reason']);
        $this->assertTrue($result['requires_payment']);
    }

    /** @test */
    public function student_with_enrollment_but_no_payment_cannot_access_paid_course()
    {
        Enrollment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'enrolled_at' => now(),
            'status' => 'active',
            'progress' => 0,
            'hours_completed' => 0,
            'last_activity_at' => now()
        ]);

        $result = $this->accessService->checkAccess($this->student, $this->course->id);
        
        $this->assertFalse($result['allowed']);
        $this->assertEquals('payment_required', $result['reason']);
        $this->assertTrue($result['requires_payment']);
    }

    /** @test */
    public function student_with_valid_enrollment_and_payment_can_access_course()
    {
        $this->createValidEnrollmentAndPayment();

        $result = $this->accessService->checkAccess($this->student, $this->course->id);
        
        $this->assertTrue($result['allowed']);
        $this->assertEquals('valid_enrollment', $result['reason']);
    }

    /** @test */
    public function student_with_inactive_enrollment_cannot_access_course()
    {
        Enrollment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'enrolled_at' => now(),
            'status' => 'inactive',
            'progress' => 0,
            'hours_completed' => 0,
            'last_activity_at' => now()
        ]);

        $this->createValidPayment();

        $result = $this->accessService->checkAccess($this->student, $this->course->id);
        
        $this->assertFalse($result['allowed']);
        $this->assertEquals('enrollment_inactive', $result['reason']);
    }

    /** @test */
    public function student_with_failed_payment_cannot_access_course()
    {
        Enrollment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'enrolled_at' => now(),
            'status' => 'active',
            'progress' => 0,
            'hours_completed' => 0,
            'last_activity_at' => now()
        ]);

        Payment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'payment_method' => 'paypal',
            'amount' => $this->course->price,
            'instructor_amount' => $this->course->price * 0.8,
            'platform_fee' => $this->course->price * 0.2,
            'currency' => 'USD',
            'status' => Payment::STATUS_FAILED,
            'type' => Payment::TYPE_COURSE_PURCHASE,
        ]);

        $result = $this->accessService->checkAccess($this->student, $this->course->id);
        
        $this->assertFalse($result['allowed']);
        $this->assertEquals('payment_required', $result['reason']);
    }

    /** @test */
    public function access_denied_for_unpublished_course()
    {
        $this->course->update(['status' => 'draft']);

        $result = $this->accessService->checkAccess($this->student, $this->course->id);
        
        $this->assertFalse($result['allowed']);
        $this->assertEquals('course_not_published', $result['reason']);
    }

    /** @test */
    public function access_denied_for_nonexistent_course()
    {
        $result = $this->accessService->checkAccess($this->student, 'nonexistent-id');
        
        $this->assertFalse($result['allowed']);
        $this->assertEquals('course_not_found', $result['reason']);
    }

    /** @test */
    public function access_denied_for_unpublished_material()
    {
        $this->createValidEnrollmentAndPayment();
        $this->material->update(['is_published' => false]);

        $result = $this->accessService->checkAccess($this->student, $this->course->id, $this->material->id);
        
        $this->assertFalse($result['allowed']);
        $this->assertEquals('material_not_published', $result['reason']);
    }

    /** @test */
    public function free_course_access_works_with_enrollment_only()
    {
        $freeCourse = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'price' => 0,
            'status' => 'published',
        ]);

        Enrollment::create([
            'user_id' => $this->student->id,
            'course_id' => $freeCourse->id,
            'enrolled_at' => now(),
            'status' => 'active',
            'progress' => 0,
            'hours_completed' => 0,
            'last_activity_at' => now()
        ]);

        $result = $this->accessService->checkAccess($this->student, $freeCourse->id);
        
        $this->assertTrue($result['allowed']);
        $this->assertEquals('valid_enrollment', $result['reason']);
    }

    /** @test */
    public function can_access_file_method_works_correctly()
    {
        $this->createValidEnrollmentAndPayment();
        
        $filePath = 'private/' . $this->instructor->id . '/' . $this->course->id . '/materials/test.pdf';
        
        $result = $this->accessService->canAccessFile($this->student, $filePath);
        
        $this->assertTrue($result);
    }

    /** @test */
    public function can_access_file_method_denies_unauthorized_access()
    {
        $filePath = 'private/' . $this->instructor->id . '/' . $this->course->id . '/materials/test.pdf';
        
        $result = $this->accessService->canAccessFile($this->student, $filePath);
        
        $this->assertFalse($result);
    }

    protected function createValidEnrollmentAndPayment()
    {
        Enrollment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'enrolled_at' => now(),
            'status' => 'active',
            'progress' => 0,
            'hours_completed' => 0,
            'last_activity_at' => now()
        ]);

        $this->createValidPayment();
    }

    protected function createValidPayment()
    {
        Payment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'payment_method' => 'paypal',
            'amount' => $this->course->price,
            'instructor_amount' => $this->course->price * 0.8,
            'platform_fee' => $this->course->price * 0.2,
            'currency' => 'USD',
            'status' => Payment::STATUS_COMPLETED,
            'type' => Payment::TYPE_COURSE_PURCHASE,
            'paid_at' => now(),
        ]);
    }
}
