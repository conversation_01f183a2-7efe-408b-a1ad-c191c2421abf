<?php

namespace Tests\Unit;

use App\Services\PayPalService;
use Tests\TestCase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PayPalCaptureFixTest extends TestCase
{
    /**
     * Test that PayPal capture request sends correct format according to official documentation
     */
    public function test_paypal_capture_sends_correct_request_format()
    {
        // Set up configuration
        config(['services.paypal' => [
            'mode' => 'sandbox',
            'client_id' => 'test_client_id',
            'client_secret' => 'test_client_secret',
            'currency' => 'USD',
            'platform_fee_percentage' => 20,
        ]]);

        // Mock HTTP responses
        Http::fake([
            // Mock access token request
            'api.sandbox.paypal.com/v1/oauth2/token' => Http::response([
                'access_token' => 'test_access_token',
                'token_type' => 'Bearer',
                'expires_in' => 3600
            ], 200),

            // Mock capture request - should succeed with empty JSON object
            'api.sandbox.paypal.com/v2/checkout/orders/*/capture' => Http::response([
                'id' => 'test_order_id',
                'status' => 'COMPLETED',
                'purchase_units' => [
                    [
                        'payments' => [
                            'captures' => [
                                [
                                    'id' => 'test_capture_id',
                                    'status' => 'COMPLETED',
                                    'amount' => [
                                        'currency_code' => 'USD',
                                        'value' => '100.00'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);

        $service = new PayPalService();

        // This will fail because no payment record exists, but we only care about the HTTP request format
        $result = $service->captureOrder('test_order_id');

        // The result will be false due to no payment record, but the HTTP request should be correct
        $this->assertFalse($result['success']);
        $this->assertStringContainsString('Payment not found', $result['error']);

        // Most importantly, verify that the HTTP request was made with correct format
        // According to PayPal documentation: Content-Type: application/json with empty JSON object {}
        Http::assertSent(function ($request) {
            return $request->url() === 'https://api.sandbox.paypal.com/v2/checkout/orders/test_order_id/capture'
                && $request->method() === 'POST'
                && $request->hasHeader('Content-Type', 'application/json')
                && $request->hasHeader('Authorization', 'Bearer test_access_token')
                && $request->body() === '{}'; // This is the critical assertion - empty JSON object
        });
    }

    /**
     * Test that the fix matches PayPal's official documentation example
     */
    public function test_request_matches_paypal_documentation()
    {
        // Set up configuration
        config(['services.paypal' => [
            'mode' => 'sandbox',
            'client_id' => 'test_client_id',
            'client_secret' => 'test_client_secret',
            'currency' => 'USD',
            'platform_fee_percentage' => 20,
        ]]);

        // Mock only the access token request
        Http::fake([
            'api.sandbox.paypal.com/v1/oauth2/token' => Http::response([
                'access_token' => 'test_access_token',
                'token_type' => 'Bearer',
                'expires_in' => 3600
            ], 200),
            // Let the capture request go through to see the actual format
            'api.sandbox.paypal.com/v2/checkout/orders/*/capture' => Http::response([
                'name' => 'RESOURCE_NOT_FOUND',
                'message' => 'The specified resource does not exist.',
                'debug_id' => 'test_debug_id'
            ], 404)
        ]);

        $service = new PayPalService();
        $result = $service->captureOrder('test_order_id');

        // Verify the request format matches PayPal documentation exactly
        Http::assertSent(function ($request) {
            // This matches the official PayPal cURL example:
            // curl -v -X POST "https://api-m.sandbox.paypal.com/v2/checkout/orders/ORDER-ID/capture"
            // -H 'Content-Type: application/json'
            // -H 'Authorization: Bearer ACCESS-TOKEN'
            // -d '{}'
            
            $hasCorrectUrl = $request->url() === 'https://api.sandbox.paypal.com/v2/checkout/orders/test_order_id/capture';
            $hasCorrectMethod = $request->method() === 'POST';
            $hasCorrectContentType = $request->hasHeader('Content-Type', 'application/json');
            $hasCorrectAuth = $request->hasHeader('Authorization', 'Bearer test_access_token');
            $hasCorrectBody = $request->body() === '{}';
            
            Log::info('PayPal Capture Request Validation', [
                'url_correct' => $hasCorrectUrl,
                'method_correct' => $hasCorrectMethod,
                'content_type_correct' => $hasCorrectContentType,
                'auth_correct' => $hasCorrectAuth,
                'body_correct' => $hasCorrectBody,
                'actual_body' => $request->body(),
                'actual_headers' => $request->headers()
            ]);
            
            return $hasCorrectUrl && $hasCorrectMethod && $hasCorrectContentType && $hasCorrectAuth && $hasCorrectBody;
        });
    }
}
