<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Auto-Save Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-900 text-white">
    <div class="container mx-auto p-8">
        <h1 class="text-2xl font-bold mb-6">Auto-Save Test Page</h1>
        
        <div class="bg-gray-800 p-6 rounded-lg">
            <h2 class="text-lg font-semibold mb-4">Test Form</h2>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Title</label>
                    <input type="text" id="test-title" class="w-full px-4 py-2 bg-gray-700 border border-gray-600 text-white rounded-md" placeholder="Type something...">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                    <textarea id="test-description" rows="3" class="w-full px-4 py-2 bg-gray-700 border border-gray-600 text-white rounded-md" placeholder="Type something..."></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Type</label>
                    <select id="test-type" class="w-full px-4 py-2 bg-gray-700 border border-gray-600 text-white rounded-md">
                        <option value="">Select type...</option>
                        <option value="video">Video</option>
                        <option value="text">Text</option>
                        <option value="quiz">Quiz</option>
                    </select>
                </div>
            </div>
            
            <div class="mt-6">
                <div id="save-status" class="text-sm text-gray-400">
                    <i class="fas fa-cloud mr-2"></i>Auto-save ready
                </div>
            </div>
        </div>
        
        <div class="mt-8 bg-gray-800 p-6 rounded-lg">
            <h2 class="text-lg font-semibold mb-4">Debug Log</h2>
            <div id="debug-log" class="bg-gray-900 p-4 rounded text-sm font-mono text-green-400 h-64 overflow-y-auto">
                <!-- Debug messages will appear here -->
            </div>
        </div>
    </div>

    <script>
        // Debug logging function
        function debugLog(message) {
            const logElement = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // Auto-save functionality test
        let autoSaveTimeout;
        const autoSaveDelay = 2000; // 2 seconds

        function showSaveStatus(status, message) {
            const statusElement = document.getElementById('save-status');
            
            switch (status) {
                case 'saving':
                    statusElement.className = 'text-sm text-blue-400';
                    statusElement.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>' + message;
                    break;
                case 'saved':
                    statusElement.className = 'text-sm text-green-400';
                    statusElement.innerHTML = '<i class="fas fa-check mr-2"></i>' + message;
                    break;
                case 'error':
                    statusElement.className = 'text-sm text-red-400';
                    statusElement.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>' + message;
                    break;
                default:
                    statusElement.className = 'text-sm text-gray-400';
                    statusElement.innerHTML = '<i class="fas fa-cloud mr-2"></i>' + message;
            }
        }

        function testAutoSave() {
            debugLog('Auto-save triggered');
            showSaveStatus('saving', 'Saving...');
            
            // Simulate API call
            setTimeout(() => {
                const success = Math.random() > 0.2; // 80% success rate
                if (success) {
                    debugLog('Auto-save successful');
                    showSaveStatus('saved', 'Saved');
                    setTimeout(() => {
                        showSaveStatus('ready', 'Auto-save ready');
                    }, 3000);
                } else {
                    debugLog('Auto-save failed');
                    showSaveStatus('error', 'Save failed');
                    setTimeout(() => {
                        showSaveStatus('ready', 'Auto-save ready');
                    }, 5000);
                }
            }, 1000);
        }

        // Set up auto-save listeners
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('Auto-save test initialized');
            
            const inputs = document.querySelectorAll('#test-title, #test-description, #test-type');
            
            inputs.forEach(input => {
                const eventType = input.tagName === 'SELECT' ? 'change' : 'input';
                
                input.addEventListener(eventType, function() {
                    debugLog(`Input changed: ${input.id} = "${input.value}"`);
                    
                    // Clear existing timeout
                    if (autoSaveTimeout) {
                        clearTimeout(autoSaveTimeout);
                        debugLog('Cleared previous auto-save timeout');
                    }

                    // Set new timeout
                    autoSaveTimeout = setTimeout(() => {
                        testAutoSave();
                    }, autoSaveDelay);
                    
                    debugLog(`Auto-save scheduled in ${autoSaveDelay}ms`);
                });
            });
        });
    </script>
</body>
</html>
