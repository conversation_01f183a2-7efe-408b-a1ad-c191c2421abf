<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class InstructorMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            // Store the intended URL for redirect after login
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Unauthenticated.'], 401);
            }

            session(['url.intended' => $request->fullUrl()]);
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Check if user has instructor role using RBAC system
        if (!$user->isInstructor() && !$user->isAdmin() && !$user->isSuperAdmin()) {
            abort(403, 'Access denied. Instructor privileges required.');
        }

        return $next($request);
    }
}
