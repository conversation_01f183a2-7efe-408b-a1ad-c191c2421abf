<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\User;
use App\Models\Payment;
use App\Models\Enrollment;
use App\Services\PayPalService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentTestController extends Controller
{
    private $paypalService;

    public function __construct(PayPalService $paypalService)
    {
        $this->paypalService = $paypalService;
    }

    /**
     * Show payment test dashboard
     */
    public function dashboard()
    {
        $courses = Course::where('status', 'published')->with('instructor')->take(5)->get();
        $users = User::whereHas('roles', function($q) {
            $q->where('name', 'student');
        })->take(5)->get();
        
        $stats = [
            'total_payments' => Payment::count(),
            'completed_payments' => Payment::where('status', Payment::STATUS_COMPLETED)->count(),
            'pending_payments' => Payment::where('status', Payment::STATUS_PENDING)->count(),
            'total_enrollments' => Enrollment::count(),
            'active_enrollments' => Enrollment::where('status', 'active')->count(),
        ];

        return view('test.payment-dashboard', compact('courses', 'users', 'stats'));
    }

    /**
     * Test course purchase flow
     */
    public function testPurchase(Request $request)
    {
        $request->validate([
            'course_id' => 'required|exists:courses,id',
            'user_id' => 'required|exists:users,id'
        ]);

        try {
            $course = Course::findOrFail($request->course_id);
            $user = User::findOrFail($request->user_id);

            // Check if user is already enrolled
            if ($user->enrollments()->where('course_id', $course->id)->exists()) {
                return response()->json([
                    'success' => false,
                    'error' => 'User is already enrolled in this course'
                ]);
            }

            // Create PayPal order
            $result = $this->paypalService->createOrder($course, $user);

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'error' => $result['error'] ?? 'Failed to create PayPal order'
                ]);
            }

            return response()->json([
                'success' => true,
                'payment_id' => $result['payment']->id,
                'paypal_order_id' => $result['paypal_order_id'],
                'approval_url' => $result['approval_url'],
                'message' => 'PayPal order created successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Payment test failed', [
                'error' => $e->getMessage(),
                'course_id' => $request->course_id,
                'user_id' => $request->user_id
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Payment test failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Simulate successful payment completion
     */
    public function simulateSuccess(Request $request)
    {
        $request->validate([
            'payment_id' => 'required|exists:payments,id'
        ]);

        try {
            $payment = Payment::findOrFail($request->payment_id);

            // Simulate payment completion
            DB::transaction(function () use ($payment) {
                // Update payment status
                $payment->update([
                    'status' => Payment::STATUS_COMPLETED,
                    'completed_at' => now(),
                    'metadata' => array_merge($payment->metadata ?? [], [
                        'simulated_completion' => true,
                        'completed_via' => 'test_simulation'
                    ])
                ]);

                // Create enrollment
                Enrollment::firstOrCreate([
                    'user_id' => $payment->user_id,
                    'course_id' => $payment->course_id,
                ], [
                    'instructor_id' => $payment->instructor_id,
                    'enrolled_at' => now(),
                    'status' => 'active',
                    'progress_percentage' => 0,
                    'completed_lectures' => 0,
                    'total_lectures' => $payment->course->total_lectures ?? 0,
                    'total_watch_time_minutes' => 0,
                    'last_accessed_at' => now()
                ]);
            });

            return response()->json([
                'success' => true,
                'message' => 'Payment completed and enrollment created successfully',
                'payment' => $payment->fresh(),
                'enrollment_exists' => $payment->user->enrollments()->where('course_id', $payment->course_id)->exists()
            ]);

        } catch (\Exception $e) {
            Log::error('Payment simulation failed', [
                'error' => $e->getMessage(),
                'payment_id' => $request->payment_id
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Payment simulation failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Check enrollment status
     */
    public function checkEnrollment(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'course_id' => 'required|exists:courses,id'
        ]);

        $user = User::findOrFail($request->user_id);
        $course = Course::findOrFail($request->course_id);
        
        $enrollment = $user->enrollments()->where('course_id', $course->id)->first();
        $payments = $user->payments()->where('course_id', $course->id)->get();

        return response()->json([
            'user' => $user->name,
            'course' => $course->title,
            'is_enrolled' => $enrollment ? true : false,
            'enrollment' => $enrollment,
            'payments' => $payments,
            'payment_count' => $payments->count(),
            'completed_payments' => $payments->where('status', Payment::STATUS_COMPLETED)->count()
        ]);
    }
}
