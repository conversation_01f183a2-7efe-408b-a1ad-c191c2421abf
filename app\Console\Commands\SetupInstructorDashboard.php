<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schema;

class SetupInstructorDashboard extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'instructor:setup {--seed : Seed test data}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set up the instructor dashboard with migrations and optimizations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up Instructor Dashboard...');

        // Run migrations
        $this->info('Running migrations...');
        Artisan::call('migrate', [], $this->getOutput());

        // Create storage directories
        $this->info('Creating storage directories...');
        $this->createStorageDirectories();

        // Optimize application
        $this->info('Optimizing application...');
        Artisan::call('config:cache', [], $this->getOutput());
        Artisan::call('route:cache', [], $this->getOutput());
        Artisan::call('view:cache', [], $this->getOutput());

        // Seed test data if requested
        if ($this->option('seed')) {
            $this->info('Seeding test data...');
            Artisan::call('db:seed', ['--class' => 'InstructorDashboardSeeder'], $this->getOutput());
        }

        // Display database statistics
        $this->displayDatabaseStats();

        $this->info('✅ Instructor Dashboard setup completed successfully!');
        
        if ($this->option('seed')) {
            $this->info('🔑 Test login credentials:');
            $this->info('   Instructor: <EMAIL> / password');
            $this->info('   Students: student1@test.<NAME_EMAIL> / password');
        }

        $this->info('🚀 You can now access the instructor dashboard at /instructor/dashboard');
    }

    /**
     * Create necessary storage directories.
     */
    private function createStorageDirectories(): void
    {
        $directories = [
            'storage/app/public/blog-images',
            'storage/app/public/learning-materials',
            'storage/app/public/ebooks',
            'storage/app/public/resources',
            'storage/app/htmlpurifier',
        ];

        foreach ($directories as $directory) {
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
                $this->line("Created directory: {$directory}");
            }
        }

        // Create symbolic link for public storage
        if (!file_exists(public_path('storage'))) {
            Artisan::call('storage:link', [], $this->getOutput());
        }
    }

    /**
     * Display database statistics.
     */
    private function displayDatabaseStats(): void
    {
        $this->info('📊 Database Statistics:');

        $tables = [
            'users' => 'Users',
            'courses' => 'Courses',
            'enrollments' => 'Enrollments',
            'payments' => 'Payments',
            'blog_posts' => 'Blog Posts',
            'video_contents' => 'Video Contents',
        ];

        foreach ($tables as $table => $label) {
            if (Schema::hasTable($table)) {
                $count = \DB::table($table)->count();
                $this->line("   {$label}: {$count}");
            }
        }

        // Display index information
        $this->info('🔍 Performance Indexes:');
        $this->line('   ✅ User management indexes');
        $this->line('   ✅ Payment tracking indexes');
        $this->line('   ✅ Content management indexes');
        $this->line('   ✅ Course enrollment indexes');
    }
}
