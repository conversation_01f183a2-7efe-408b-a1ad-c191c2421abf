@extends('instructor.layouts.app')

@section('title', 'Upload Content File - Instructor Dashboard')

@section('content')
<div class="py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="flex items-center mb-8">
            <a href="{{ route('instructor.content-files.index') }}" class="text-gray-400 hover:text-white mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">Upload Content File</h1>
                <p class="text-gray-400">Add a new file to your content library</p>
            </div>
        </div>

        <!-- Upload Form -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-8">
            <form method="POST" action="{{ route('instructor.content-files.store') }}" enctype="multipart/form-data" class="space-y-6">
                @csrf

                <!-- Title -->
                <div>
                    <label for="title" class="block text-sm font-medium text-white mb-2">Title *</label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           value="{{ old('title') }}"
                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                           placeholder="Enter file title"
                           required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-white mb-2">Description</label>
                    <textarea id="description" 
                              name="description" 
                              rows="4"
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                              placeholder="Enter file description">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Category -->
                <div>
                    <label for="category" class="block text-sm font-medium text-white mb-2">Category *</label>
                    <select id="category" 
                            name="category"
                            class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                            required>
                        <option value="">Select category</option>
                        <option value="document" {{ old('category') == 'document' ? 'selected' : '' }}>Document</option>
                        <option value="image" {{ old('category') == 'image' ? 'selected' : '' }}>Image</option>
                        <option value="video" {{ old('category') == 'video' ? 'selected' : '' }}>Video</option>
                        <option value="audio" {{ old('category') == 'audio' ? 'selected' : '' }}>Audio</option>
                        <option value="archive" {{ old('category') == 'archive' ? 'selected' : '' }}>Archive</option>
                        <option value="other" {{ old('category') == 'other' ? 'selected' : '' }}>Other</option>
                    </select>
                    @error('category')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Course -->
                <div>
                    <label for="course_id" class="block text-sm font-medium text-white mb-2">Course (Optional)</label>
                    <select id="course_id" 
                            name="course_id"
                            class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
                        <option value="">No course (standalone file)</option>
                        @foreach($courses as $course)
                            <option value="{{ $course->id }}" {{ (old('course_id', $selectedCourse->id ?? null) == $course->id) ? 'selected' : '' }}>
                                {{ $course->title }}
                            </option>
                        @endforeach
                    </select>
                    @error('course_id')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- File Upload -->
                <div>
                    <label for="file" class="block text-sm font-medium text-white mb-2">File *</label>
                    <div class="border-2 border-dashed border-gray-700 rounded-lg p-8 text-center hover:border-gray-600 transition-colors">
                        <input type="file" 
                               id="file" 
                               name="file"
                               class="hidden"
                               required
                               onchange="updateFileName(this)">
                        <label for="file" class="cursor-pointer">
                            <svg class="w-12 h-12 text-gray-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <p class="text-white font-medium mb-2">Click to upload file</p>
                            <p class="text-gray-400 text-sm">Maximum file size: 100MB</p>
                        </label>
                        <div id="file-name" class="mt-4 text-sm text-gray-400 hidden"></div>
                    </div>
                    @error('file')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                    <!-- File Preview Container -->
                    <div id="file-preview-container"></div>
                </div>

                <!-- Access Level -->
                <div>
                    <label for="is_public" class="block text-sm font-medium text-white mb-2">Access Level *</label>
                    <div class="relative">
                        <select name="is_public" id="is_public"
                                class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('is_public') border-red-500 @enderror appearance-none" required>
                            <option value="0" {{ old('is_public', '0') == '0' ? 'selected' : '' }}>
                                🔒 Private - Only accessible to enrolled students
                            </option>
                            <option value="1" {{ old('is_public') == '1' ? 'selected' : '' }}>
                                🌐 Public - Accessible to everyone
                            </option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    @error('is_public')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                    <p class="text-xs text-gray-500 mt-1">Control who can access this file</p>
                </div>

                <!-- Submit Buttons -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-800">
                    <a href="{{ route('instructor.content-files.index') }}" 
                       class="px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                        Upload File
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Enhanced file upload with preview
        enhanceFileUpload('file', 'file-preview-container', {
            maxSize: 100 * 1024 * 1024, // 100MB
            allowedTypes: [], // Allow all file types
            onError: function(error) {
                // Create error message element
                const errorDiv = document.createElement('div');
                errorDiv.className = 'mt-2 p-3 bg-red-900 border border-red-700 rounded-lg text-red-300 text-sm';
                errorDiv.textContent = error;

                // Remove any existing error messages
                const existingError = document.querySelector('.file-upload-error');
                if (existingError) existingError.remove();

                // Add error class and insert error message
                errorDiv.classList.add('file-upload-error');
                document.getElementById('file-preview-container').appendChild(errorDiv);

                // Remove error after 5 seconds
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.remove();
                    }
                }, 5000);
            }
        });
    });

    function updateFileName(input) {
        const fileNameDiv = document.getElementById('file-name');
        if (input.files && input.files[0]) {
            fileNameDiv.textContent = 'Selected: ' + input.files[0].name;
            fileNameDiv.classList.remove('hidden');
        } else {
            fileNameDiv.classList.add('hidden');
        }
    }
</script>
@endpush
@endsection
