<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Role extends Model
{
    use HasFactory;

    // Role constants for easy reference
    const STUDENT = 'student';
    const INSTRUCTOR = 'instructor';
    const ADMIN = 'admin';
    const SUPERADMIN = 'superadmin';

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'is_active',
        'priority',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'priority' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the users that have this role.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_roles')
            ->withPivot(['assigned_at', 'assigned_by', 'notes', 'is_active', 'expires_at'])
            ->withTimestamps();
    }

    /**
     * Get the active users that have this role.
     */
    public function activeUsers(): BelongsToMany
    {
        return $this->users()->wherePivot('is_active', true);
    }

    /**
     * Get the permissions for this role.
     */
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'role_permissions')
            ->withPivot(['granted_at', 'granted_by', 'is_active'])
            ->withTimestamps();
    }

    /**
     * Get the active permissions for this role.
     */
    public function activePermissions(): BelongsToMany
    {
        return $this->permissions()->wherePivot('is_active', true);
    }

    /**
     * Get the user role assignments for this role.
     */
    public function userRoles(): HasMany
    {
        return $this->hasMany(UserRole::class);
    }

    /**
     * Get the role permission assignments for this role.
     */
    public function rolePermissions(): HasMany
    {
        return $this->hasMany(RolePermission::class);
    }

    /**
     * Check if this role has a specific permission.
     */
    public function hasPermission(string $permissionName): bool
    {
        return $this->activePermissions()
            ->where('permissions.name', $permissionName)
            ->exists();
    }

    /**
     * Grant a permission to this role.
     */
    public function grantPermission(Permission $permission, ?User $grantedBy = null): void
    {
        $this->permissions()->syncWithoutDetaching([
            $permission->id => [
                'granted_at' => now(),
                'granted_by' => $grantedBy?->id,
                'is_active' => true,
            ]
        ]);
    }

    /**
     * Revoke a permission from this role.
     */
    public function revokePermission(Permission $permission): void
    {
        $this->permissions()->updateExistingPivot($permission->id, [
            'is_active' => false,
        ]);
    }

    /**
     * Scope to get only active roles.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by priority.
     */
    public function scopeByPriority($query, $direction = 'desc')
    {
        return $query->orderBy('priority', $direction);
    }

    /**
     * Get role by name.
     */
    public static function findByName(string $name): ?self
    {
        return static::where('name', $name)->first();
    }

    /**
     * Check if this is a student role.
     */
    public function isStudent(): bool
    {
        return $this->name === self::STUDENT;
    }

    /**
     * Check if this is an instructor role.
     */
    public function isInstructor(): bool
    {
        return $this->name === self::INSTRUCTOR;
    }

    /**
     * Check if this is an admin role.
     */
    public function isAdmin(): bool
    {
        return $this->name === self::ADMIN;
    }

    /**
     * Check if this is a superadmin role.
     */
    public function isSuperAdmin(): bool
    {
        return $this->name === self::SUPERADMIN;
    }

    /**
     * Check if this role has administrative privileges.
     */
    public function hasAdminPrivileges(): bool
    {
        return in_array($this->name, [self::ADMIN, self::SUPERADMIN]);
    }
}
