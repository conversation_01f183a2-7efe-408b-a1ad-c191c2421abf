<?php

namespace App\Http\Controllers\Instructor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class EbookController extends Controller
{
    public function index()
    {
        return view('instructor.ebooks.index');
    }

    public function create()
    {
        return view('instructor.ebooks.create');
    }

    public function store(Request $request)
    {
        return redirect()->route('instructor.ebooks.index')
            ->with('success', 'Ebook created successfully.');
    }

    public function show(string $id)
    {
        return view('instructor.ebooks.show');
    }

    public function edit(string $id)
    {
        return view('instructor.ebooks.edit');
    }

    public function update(Request $request, string $id)
    {
        return redirect()->route('instructor.ebooks.index')
            ->with('success', 'Ebook updated successfully.');
    }

    public function destroy(string $id)
    {
        return redirect()->route('instructor.ebooks.index')
            ->with('success', 'Ebook deleted successfully.');
    }
}
