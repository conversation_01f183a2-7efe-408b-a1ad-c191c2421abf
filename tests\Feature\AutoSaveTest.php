<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\Chapter;
use App\Models\Lecture;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AutoSaveTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $instructor;
    protected $course;
    protected $chapter;
    protected $lecture;

    protected function setUp(): void
    {
        parent::setUp();

        // Create instructor
        $this->instructor = User::factory()->create([
            'role' => 'instructor'
        ]);

        // Create course
        $this->course = Course::factory()->create([
            'instructor_id' => $this->instructor->id
        ]);

        // Create chapter
        $this->chapter = Chapter::factory()->create([
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id
        ]);

        // Create lecture
        $this->lecture = Lecture::factory()->create([
            'chapter_id' => $this->chapter->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'type' => 'video'
        ]);
    }

    /** @test */
    public function instructor_can_auto_save_lecture_via_ajax()
    {
        $this->actingAs($this->instructor);

        $response = $this->patchJson(
            route('instructor.courses.chapters.lectures.update', [
                'course' => $this->course,
                'chapter' => $this->chapter,
                'lecture' => $this->lecture
            ]),
            [
                'title' => 'Updated Lecture Title',
                'description' => 'Updated description',
                'type' => 'video',
                'video_url' => 'https://youtube.com/watch?v=test123',
                'duration_minutes' => 15,
                'auto_save' => '1'
            ],
            [
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        );

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Lecture updated successfully!'
                ]);

        $this->assertDatabaseHas('lectures', [
            'id' => $this->lecture->id,
            'title' => 'Updated Lecture Title',
            'description' => 'Updated description',
            'duration_minutes' => 15
        ]);
    }

    /** @test */
    public function auto_save_handles_validation_errors_gracefully()
    {
        $this->actingAs($this->instructor);

        $response = $this->patchJson(
            route('instructor.courses.chapters.lectures.update', [
                'course' => $this->course,
                'chapter' => $this->chapter,
                'lecture' => $this->lecture
            ]),
            [
                'title' => '', // Invalid - required field
                'type' => 'video',
                'auto_save' => '1'
            ],
            [
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        );

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'validation_errors' => true,
                    'message' => 'Please fix validation errors'
                ]);
    }

    /** @test */
    public function auto_save_handles_empty_duration_minutes()
    {
        $this->actingAs($this->instructor);

        // Test with empty string for duration_minutes
        $response = $this->patchJson(
            route('instructor.courses.chapters.lectures.update', [
                'course' => $this->course,
                'chapter' => $this->chapter,
                'lecture' => $this->lecture
            ]),
            [
                'title' => 'Test Lecture',
                'type' => 'video',
                'duration_minutes' => '', // Empty string should be converted to 0
                'auto_save' => '1'
            ],
            [
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        );

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true
                ]);

        $this->assertDatabaseHas('lectures', [
            'id' => $this->lecture->id,
            'duration_minutes' => 0 // Should be converted to 0, not null
        ]);

        // Test with null value for duration_minutes
        $response = $this->patchJson(
            route('instructor.courses.chapters.lectures.update', [
                'course' => $this->course,
                'chapter' => $this->chapter,
                'lecture' => $this->lecture
            ]),
            [
                'title' => 'Test Lecture',
                'type' => 'video',
                'duration_minutes' => null, // Null should be converted to 0
                'auto_save' => '1'
            ],
            [
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        );

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true
                ]);

        $this->assertDatabaseHas('lectures', [
            'id' => $this->lecture->id,
            'duration_minutes' => 0 // Should be converted to 0, not null
        ]);
    }

    /** @test */
    public function auto_save_detects_concurrent_edits()
    {
        $this->actingAs($this->instructor);

        // Simulate the lecture being updated after the form was loaded
        $this->lecture->update(['title' => 'Changed by another user']);

        $response = $this->patchJson(
            route('instructor.courses.chapters.lectures.update', [
                'course' => $this->course,
                'chapter' => $this->chapter,
                'lecture' => $this->lecture
            ]),
            [
                'title' => 'My changes',
                'type' => 'video',
                'auto_save' => '1',
                'last_updated' => now()->subMinutes(5)->toISOString() // Old timestamp
            ],
            [
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        );

        $response->assertStatus(409)
                ->assertJson([
                    'success' => false,
                    'conflict' => true,
                    'message' => 'This lecture has been modified by another user. Please refresh the page.'
                ]);
    }


}
