<?php

namespace App\Policies;

use App\Models\User;
use App\Models\BlogPost;
use App\Models\VideoContent;
use App\Models\LearningMaterial;
use App\Models\Ebook;
use App\Models\Resource;
use Illuminate\Database\Eloquent\Model;

class InstructorContentPolicy
{
    /**
     * Determine if the user can view any content.
     */
    public function viewAny(User $user): bool
    {
        return $user->isInstructor() || $user->isAdmin() || $user->isSuperAdmin();
    }

    /**
     * Determine if the user can view the content.
     */
    public function view(User $user, Model $content): bool
    {
        // Admin and super admin can view all content
        if ($user->isAdmin() || $user->isSuperAdmin()) {
            return true;
        }

        // Instructors can only view their own content
        if ($user->isInstructor()) {
            return $this->isOwner($user, $content);
        }

        return false;
    }

    /**
     * Determine if the user can create content.
     */
    public function create(User $user): bool
    {
        return $user->isInstructor() || $user->isAdmin() || $user->isSuperAdmin();
    }

    /**
     * Determine if the user can update the content.
     */
    public function update(User $user, Model $content): bool
    {
        // Admin and super admin can update all content
        if ($user->isAdmin() || $user->isSuperAdmin()) {
            return true;
        }

        // Instructors can only update their own content
        if ($user->isInstructor()) {
            return $this->isOwner($user, $content);
        }

        return false;
    }

    /**
     * Determine if the user can delete the content.
     */
    public function delete(User $user, Model $content): bool
    {
        // Admin and super admin can delete all content
        if ($user->isAdmin() || $user->isSuperAdmin()) {
            return true;
        }

        // Instructors can only delete their own content
        if ($user->isInstructor()) {
            return $this->isOwner($user, $content);
        }

        return false;
    }

    /**
     * Determine if the user can restore the content.
     */
    public function restore(User $user, Model $content): bool
    {
        return $user->isAdmin() || $user->isSuperAdmin();
    }

    /**
     * Determine if the user can permanently delete the content.
     */
    public function forceDelete(User $user, Model $content): bool
    {
        return $user->isSuperAdmin();
    }

    /**
     * Determine if the user can publish/unpublish content.
     */
    public function publish(User $user, Model $content): bool
    {
        // Admin and super admin can publish/unpublish all content
        if ($user->isAdmin() || $user->isSuperAdmin()) {
            return true;
        }

        // Instructors can only publish/unpublish their own content
        if ($user->isInstructor()) {
            return $this->isOwner($user, $content);
        }

        return false;
    }

    /**
     * Check if the user owns the content.
     */
    private function isOwner(User $user, Model $content): bool
    {
        // Check different content types
        if ($content instanceof BlogPost) {
            return $content->instructor_id === $user->id;
        }

        if ($content instanceof VideoContent) {
            return $content->instructor_id === $user->id;
        }

        if ($content instanceof LearningMaterial) {
            return $content->instructor_id === $user->id;
        }

        if ($content instanceof Ebook) {
            return $content->instructor_id === $user->id;
        }

        if ($content instanceof Resource) {
            return $content->instructor_id === $user->id;
        }

        // Check if content has instructor_id attribute
        if (isset($content->instructor_id)) {
            return $content->instructor_id === $user->id;
        }

        return false;
    }
}
