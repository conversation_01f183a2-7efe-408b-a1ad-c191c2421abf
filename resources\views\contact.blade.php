@extends('layouts.app')

@section('title', 'Contact Us - Escape Matrix Academy')

@section('content')
<!-- Hero Section -->
<section class="relative py-20 md:py-32 bg-gradient-to-br from-black via-gray-900 to-black overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fillRule=\'evenodd\'%3E%3Cg fill=\'%23ef4444\' fillOpacity=\'0.1\'%3E%3Ccircle cx=\'30\' cy=\'30\' r=\'2\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>
    </div>

    <div class="container mx-auto px-4 relative">
        <div class="max-w-4xl mx-auto text-center">
            <h1 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                Get In
                <span class="text-red-500 bg-gradient-to-r from-red-500 to-red-600 bg-clip-text text-transparent">
                    Touch
                </span>
            </h1>

            <p class="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
                Have questions about our courses? Need support? Want to discuss a custom training program? We're here to help you on your transformation journey.
            </p>
        </div>
    </div>
</section>

<!-- Contact Form Section -->
<section class="py-20 bg-black">
    <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Contact Form -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-8">
                    <h2 class="text-3xl font-bold text-white mb-6">Send Us a Message</h2>
                    
                    @if(session('success'))
                        <div class="bg-green-900 border border-green-700 text-green-300 px-4 py-3 rounded-lg mb-6">
                            {{ session('success') }}
                        </div>
                    @endif

                    <form method="POST" action="{{ route('contact.submit') }}" class="space-y-6">
                        @csrf

                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-300 mb-2">Full Name *</label>
                            <input type="text" name="name" id="name" value="{{ old('name') }}" 
                                   class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('name') border-red-500 @enderror"
                                   placeholder="Enter your full name" required>
                            @error('name')
                                <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-300 mb-2">Email Address *</label>
                            <input type="email" name="email" id="email" value="{{ old('email') }}" 
                                   class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('email') border-red-500 @enderror"
                                   placeholder="Enter your email address" required>
                            @error('email')
                                <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Subject -->
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-300 mb-2">Subject *</label>
                            <select name="subject" id="subject" 
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('subject') border-red-500 @enderror" required>
                                <option value="">Select a subject</option>
                                <option value="Course Inquiry" {{ old('subject') === 'Course Inquiry' ? 'selected' : '' }}>Course Inquiry</option>
                                <option value="Technical Support" {{ old('subject') === 'Technical Support' ? 'selected' : '' }}>Technical Support</option>
                                <option value="Billing Question" {{ old('subject') === 'Billing Question' ? 'selected' : '' }}>Billing Question</option>
                                <option value="Partnership" {{ old('subject') === 'Partnership' ? 'selected' : '' }}>Partnership Opportunity</option>
                                <option value="Custom Training" {{ old('subject') === 'Custom Training' ? 'selected' : '' }}>Custom Training Program</option>
                                <option value="Media Inquiry" {{ old('subject') === 'Media Inquiry' ? 'selected' : '' }}>Media Inquiry</option>
                                <option value="Other" {{ old('subject') === 'Other' ? 'selected' : '' }}>Other</option>
                            </select>
                            @error('subject')
                                <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Message -->
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-300 mb-2">Message *</label>
                            <textarea name="message" id="message" rows="6" 
                                      class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('message') border-red-500 @enderror"
                                      placeholder="Tell us how we can help you..." required>{{ old('message') }}</textarea>
                            @error('message')
                                <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white py-3 px-6 rounded-lg font-medium transition-colors">
                            Send Message
                        </button>
                    </form>
                </div>

                <!-- Contact Information -->
                <div class="space-y-8">
                    <div>
                        <h2 class="text-3xl font-bold text-white mb-6">Contact Information</h2>
                        <p class="text-gray-400 mb-8">
                            We're here to help you succeed. Reach out to us through any of the following channels, and we'll get back to you within 24 hours.
                        </p>
                    </div>

                    <!-- Contact Methods -->
                    <div class="space-y-6">
                        <div class="flex items-start space-x-4">
                            <div class="bg-red-600 p-3 rounded-lg">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-1">Email Support</h3>
                                <p class="text-gray-400"><EMAIL></p>
                                <p class="text-sm text-gray-500">Response within 24 hours</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-4">
                            <div class="bg-red-600 p-3 rounded-lg">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-1">Live Chat</h3>
                                <p class="text-gray-400">Available on our website</p>
                                <p class="text-sm text-gray-500">Monday - Friday, 9 AM - 6 PM EST</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-4">
                            <div class="bg-red-600 p-3 rounded-lg">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-1">Office Location</h3>
                                <p class="text-gray-400">Remote-First Company</p>
                                <p class="text-sm text-gray-500">Serving students worldwide</p>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Link -->
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <h3 class="text-xl font-bold text-white mb-3">Frequently Asked Questions</h3>
                        <p class="text-gray-400 mb-4">
                            Before reaching out, you might find your answer in our comprehensive FAQ section.
                        </p>
                        <a href="#" class="inline-flex items-center text-red-400 hover:text-red-300 transition-colors">
                            View FAQ
                            <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                            </svg>
                        </a>
                    </div>

                    <!-- Response Time -->
                    <div class="bg-gradient-to-r from-red-600 to-red-800 rounded-lg p-6 text-center">
                        <div class="text-4xl mb-3">⚡</div>
                        <h3 class="text-xl font-bold text-white mb-2">Quick Response Guarantee</h3>
                        <p class="text-red-100">
                            We respond to all inquiries within 24 hours, usually much faster!
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
