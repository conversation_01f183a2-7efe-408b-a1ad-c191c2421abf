/* ===================================
   FLOATING NAVIGATION SYSTEM
   Independent CSS for Enterprise Header
   =================================== */

/* Reset any existing navigation positioning */
.floating-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(75, 85, 99, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 64px;
  /* Ensure nav doesn't affect document flow */
  margin: 0;
  padding: 0;
}

/* Scrolled state for enhanced backdrop */
.floating-nav.scrolled {
  background: rgba(0, 0, 0, 0.98);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  border-bottom-color: rgba(75, 85, 99, 0.3);
  box-shadow: 
    0 10px 25px -5px rgba(0, 0, 0, 0.3),
    0 4px 6px -2px rgba(0, 0, 0, 0.1);
}

/* Navigation container */
.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Brand/Logo */
.nav-brand {
  font-size: 1.25rem;
  font-weight: 700;
  color: #ef4444;
  text-decoration: none;
  transition: color 0.2s ease;
}

.nav-brand:hover {
  color: #dc2626;
}

/* Desktop Navigation Links */
.nav-links {
  display: none;
  align-items: center;
  gap: 2rem;
}

@media (min-width: 768px) {
  .nav-links {
    display: flex;
  }
}

.nav-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
  position: relative;
}

.nav-link:hover {
  color: #ef4444;
}

.nav-link.active {
  color: #ef4444;
}

/* User Menu Container */
.user-menu-container {
  position: relative;
  display: inline-block;
}

/* User Menu Button */
.user-menu-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  background: none;
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.user-menu-button:hover {
  color: #ef4444;
  background: rgba(55, 65, 81, 0.3);
}

/* User Avatar */
.user-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, #ef4444, #dc2626, #b91c1c);
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.user-menu-button:hover .user-avatar {
  border-color: rgba(239, 68, 68, 0.3);
  transform: scale(1.05);
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* User Name */
.user-name {
  font-weight: 500;
  transition: color 0.2s ease;
}

@media (max-width: 640px) {
  .user-name {
    display: none;
  }
}

/* Dropdown Arrow */
.dropdown-arrow {
  width: 1rem;
  height: 1rem;
  transition: transform 0.3s ease;
}

.user-menu-button[aria-expanded="true"] .dropdown-arrow {
  transform: rotate(180deg);
}

/* FLOATING DROPDOWN MENU */
.floating-dropdown {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  width: 18rem;
  background: rgba(17, 24, 39, 0.98);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  border-radius: 1rem;
  border: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow: 
    0 32px 64px -12px rgba(0, 0, 0, 0.4),
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  
  /* Animation states */
  opacity: 0;
  visibility: hidden;
  transform: scale(0.95) translateY(-0.5rem);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Ensure it floats above everything */
  z-index: 99999;
  
  /* Mobile scrolling support */
  max-height: calc(100vh - 80px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* Show state */
.floating-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: scale(1) translateY(0);
}

/* Dropdown pointer/arrow */
.floating-dropdown::before {
  content: '';
  position: absolute;
  top: -0.5rem;
  right: 1.5rem;
  width: 1rem;
  height: 1rem;
  background: rgba(17, 24, 39, 0.98);
  border: 1px solid rgba(75, 85, 99, 0.3);
  border-bottom: none;
  border-right: none;
  transform: rotate(45deg);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
}

/* Dropdown Content */
.dropdown-content {
  padding: 0.5rem;
  /* Enable smooth scrolling within dropdown */
  scroll-behavior: smooth;
}

/* User Info Header */
.dropdown-user-info {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(75, 85, 99, 0.3);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.dropdown-user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 700;
  color: white;
  background: linear-gradient(135deg, #ef4444, #dc2626, #b91c1c);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dropdown-user-details {
  flex: 1;
  min-width: 0;
}

.dropdown-user-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-user-email {
  font-size: 0.75rem;
  color: #9ca3af;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Navigation Items */
.dropdown-nav {
  padding: 0.5rem;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  margin: 0.125rem;
  border-radius: 0.5rem;
  color: #d1d5db;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.dropdown-item:hover {
  color: white;
  background: rgba(55, 65, 81, 0.4);
  transform: translateX(2px);
}

/* Icon containers */
.dropdown-icon {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  background: rgba(55, 65, 81, 0.5);
  transition: all 0.2s ease;
}

.dropdown-item:hover .dropdown-icon {
  background: rgba(239, 68, 68, 0.2);
}

.dropdown-icon i {
  color: #9ca3af;
  transition: color 0.2s ease;
}

.dropdown-item:hover .dropdown-icon i {
  color: #ef4444;
}

/* Logout Section */
.dropdown-logout {
  border-top: 1px solid rgba(75, 85, 99, 0.3);
  padding: 0.5rem;
  margin-top: 0.5rem;
}

.logout-button {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  margin: 0.125rem;
  border-radius: 0.5rem;
  color: #d1d5db;
  background: none;
  border: none;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.logout-button:hover {
  color: white;
  background: rgba(239, 68, 68, 0.1);
}

.logout-button:hover .dropdown-icon {
  background: rgba(239, 68, 68, 0.2);
}

.logout-button:hover .dropdown-icon i {
  color: #ef4444;
}

/* Mobile Menu Button */
.mobile-menu-button {
  display: block;
  color: white;
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  transition: color 0.2s ease;
}

.mobile-menu-button:hover {
  color: #ef4444;
}

@media (min-width: 768px) {
  .mobile-menu-button {
    display: none;
  }
}

/* Mobile Navigation */
.mobile-nav {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(17, 24, 39, 0.98);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  border-bottom: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
}

.mobile-nav.show {
  display: block;
}

.mobile-nav-content {
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.mobile-nav-item {
  display: block;
  color: white;
  text-decoration: none;
  padding: 0.75rem 1rem;
  margin: 0.25rem 0;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.mobile-nav-item:hover {
  color: #ef4444;
  background: rgba(55, 65, 81, 0.3);
}

/* Body padding to prevent content overlap */
body {
  padding-top: 64px; /* Height of the floating nav */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .floating-dropdown {
    width: calc(100vw - 2rem);
    right: 1rem;
    left: 1rem;
    max-height: calc(100vh - 100px);
    /* Ensure dropdown doesn't exceed viewport */
    bottom: auto;
    /* Improve touch scrolling on mobile */
    -webkit-overflow-scrolling: touch;
    /* Add momentum scrolling for iOS */
    overscroll-behavior: contain;
  }
  
  /* Adjust dropdown position if it would overflow */
  .floating-dropdown.position-adjusted {
    top: auto;
    bottom: calc(100% + 0.5rem);
  }
  
  .nav-container {
    padding: 0 0.75rem;
  }
}

/* Tablet-specific adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  .floating-dropdown {
    max-height: calc(100vh - 120px);
    /* Ensure adequate spacing on tablets */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .floating-dropdown {
    max-height: calc(100vh - 80px);
    /* More aggressive height constraint for small screens */
  }
  
  .dropdown-user-info {
    padding: 0.75rem 1rem;
    /* Reduce padding on very small screens */
  }
  
  .dropdown-item {
    padding: 0.625rem 0.75rem;
    /* Slightly smaller touch targets but still accessible */
  }
  
  .logout-button {
    padding: 0.625rem 0.75rem;
  }
}

/* Smooth scroll offset for anchors */
html {
  scroll-padding-top: 80px;
}

/* Animation for better UX */
@media (prefers-reduced-motion: no-preference) {
  .floating-nav,
  .floating-dropdown,
  .dropdown-item,
  .user-avatar {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Custom scrollbar for dropdown */
.floating-dropdown::-webkit-scrollbar {
  width: 6px;
}

.floating-dropdown::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.5);
  border-radius: 3px;
}

.floating-dropdown::-webkit-scrollbar-thumb {
  background: rgba(239, 68, 68, 0.6);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.floating-dropdown::-webkit-scrollbar-thumb:hover {
  background: rgba(239, 68, 68, 0.8);
}

/* Firefox scrollbar styling */
.floating-dropdown {
  scrollbar-width: thin;
  scrollbar-color: rgba(239, 68, 68, 0.6) rgba(31, 41, 55, 0.5);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .floating-nav {
    border-bottom-width: 2px;
  }
  
  .floating-dropdown {
    border-width: 2px;
  }
  
  .floating-dropdown::-webkit-scrollbar-thumb {
    background: #ef4444;
  }
}