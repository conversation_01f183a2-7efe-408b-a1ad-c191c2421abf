<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\Chapter;
use App\Models\Lecture;
use App\Models\CourseCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CourseBuilderAutoSaveTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $instructor;
    protected $course;
    protected $chapter;
    protected $lecture;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an instructor user
        $this->instructor = User::factory()->create([
            'role' => 'instructor',
            'email_verified_at' => now(),
        ]);

        // Create a basic category
        CourseCategory::create([
            'name' => 'General',
            'slug' => 'general',
            'description' => 'General courses',
            'is_active' => true,
        ]);

        // Create test course, chapter, and lecture
        $this->course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
        ]);

        $this->chapter = Chapter::factory()->create([
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
        ]);

        $this->lecture = Lecture::factory()->create([
            'chapter_id' => $this->chapter->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'type' => 'video',
            'duration_minutes' => 10,
        ]);
    }

    /** @test */
    public function course_builder_auto_save_handles_empty_duration_minutes()
    {
        $this->actingAs($this->instructor);

        // Test with empty string for duration_minutes
        $response = $this->postJson(
            route('instructor.course-builder.lectures.auto-save', [
                'course' => $this->course,
                'chapter' => $this->chapter,
                'lecture' => $this->lecture
            ]),
            [
                'title' => 'Test Lecture',
                'type' => 'video',
                'duration_minutes' => '', // Empty string should be converted to 0
            ],
            [
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        );

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Lecture saved automatically'
                ]);

        $this->assertDatabaseHas('lectures', [
            'id' => $this->lecture->id,
            'duration_minutes' => 0 // Should be converted to 0, not null
        ]);
    }

    /** @test */
    public function course_builder_auto_save_handles_null_duration_minutes()
    {
        $this->actingAs($this->instructor);

        // Test with null value for duration_minutes
        $response = $this->postJson(
            route('instructor.course-builder.lectures.auto-save', [
                'course' => $this->course,
                'chapter' => $this->chapter,
                'lecture' => $this->lecture
            ]),
            [
                'title' => 'Test Lecture',
                'type' => 'video',
                'duration_minutes' => null, // Null should be converted to 0
            ],
            [
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        );

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Lecture saved automatically'
                ]);

        $this->assertDatabaseHas('lectures', [
            'id' => $this->lecture->id,
            'duration_minutes' => 0 // Should be converted to 0, not null
        ]);
    }

    /** @test */
    public function course_builder_auto_save_handles_valid_duration_minutes()
    {
        $this->actingAs($this->instructor);

        // Test with valid duration_minutes value
        $response = $this->postJson(
            route('instructor.course-builder.lectures.auto-save', [
                'course' => $this->course,
                'chapter' => $this->chapter,
                'lecture' => $this->lecture
            ]),
            [
                'title' => 'Test Lecture',
                'type' => 'video',
                'duration_minutes' => 25,
            ],
            [
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        );

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Lecture saved automatically'
                ]);

        $this->assertDatabaseHas('lectures', [
            'id' => $this->lecture->id,
            'duration_minutes' => 25
        ]);
    }

    /** @test */
    public function course_builder_auto_save_handles_other_numeric_fields()
    {
        $this->actingAs($this->instructor);

        // Test with empty quiz_passing_score and estimated_completion_minutes
        $response = $this->postJson(
            route('instructor.course-builder.lectures.auto-save', [
                'course' => $this->course,
                'chapter' => $this->chapter,
                'lecture' => $this->lecture
            ]),
            [
                'title' => 'Test Lecture',
                'type' => 'quiz',
                'quiz_passing_score' => '', // Empty string should be converted to 0
                'estimated_completion_minutes' => null, // Null should be converted to 0
            ],
            [
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        );

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Lecture saved automatically'
                ]);

        $this->assertDatabaseHas('lectures', [
            'id' => $this->lecture->id,
            'quiz_passing_score' => 0,
            'estimated_completion_minutes' => 0
        ]);
    }

    /** @test */
    public function course_builder_auto_save_validates_duration_minutes_range()
    {
        $this->actingAs($this->instructor);

        // Test with invalid duration_minutes (too high)
        $response = $this->postJson(
            route('instructor.course-builder.lectures.auto-save', [
                'course' => $this->course,
                'chapter' => $this->chapter,
                'lecture' => $this->lecture
            ]),
            [
                'title' => 'Test Lecture',
                'type' => 'video',
                'duration_minutes' => 2000, // Over max limit of 1440
            ],
            [
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        );

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Validation failed'
                ]);
    }

    /** @test */
    public function course_builder_auto_save_requires_authorization()
    {
        // Create another instructor
        $otherInstructor = User::factory()->create([
            'role' => 'instructor',
            'email_verified_at' => now(),
        ]);

        $this->actingAs($otherInstructor);

        $response = $this->postJson(
            route('instructor.course-builder.lectures.auto-save', [
                'course' => $this->course,
                'chapter' => $this->chapter,
                'lecture' => $this->lecture
            ]),
            [
                'title' => 'Test Lecture',
                'type' => 'video',
                'duration_minutes' => 15,
            ],
            [
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        );

        $response->assertStatus(403);
    }
}
