<?php

namespace App\Http\Controllers\Instructor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class VideoContentController extends Controller
{
    public function index()
    {
        return view('instructor.video-content.index');
    }

    public function create()
    {
        return view('instructor.video-content.create');
    }

    public function store(Request $request)
    {
        return redirect()->route('instructor.video-content.index')
            ->with('success', 'Video content created successfully.');
    }

    public function show(string $id)
    {
        return view('instructor.video-content.show');
    }

    public function edit(string $id)
    {
        return view('instructor.video-content.edit');
    }

    public function update(Request $request, string $id)
    {
        return redirect()->route('instructor.video-content.index')
            ->with('success', 'Video content updated successfully.');
    }

    public function destroy(string $id)
    {
        return redirect()->route('instructor.video-content.index')
            ->with('success', 'Video content deleted successfully.');
    }
}
