<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\Course;
use App\Models\User;
use App\Exceptions\PayPalException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class PayPalService
{
    private $config;
    private $baseUrl;
    private $accessToken;

    public function __construct()
    {
        $this->config = config('services.paypal');
        $this->baseUrl = $this->config['mode'] === 'live'
            ? 'https://api.paypal.com'
            : 'https://api.sandbox.paypal.com';
    }

    /**
     * Get PayPal access token
     */
    private function getAccessToken(): string
    {
        if ($this->accessToken) {
            return $this->accessToken;
        }

        $response = Http::withBasicAuth(
            $this->config['client_id'],
            $this->config['client_secret']
        )->asForm()->post($this->baseUrl . '/v1/oauth2/token', [
            'grant_type' => 'client_credentials'
        ]);

        if ($response->successful()) {
            $this->accessToken = $response->json()['access_token'];
            return $this->accessToken;
        }

        $errorData = $response->json();
        throw PayPalException::fromApiResponse($errorData, [
            'operation' => 'get_access_token',
            'status_code' => $response->status()
        ]);
    }

    /**
     * Create PayPal order for course purchase
     */
    public function createOrder(Course $course, User $user): array
    {
        try {
            // Calculate amounts
            $coursePrice = (float) $course->price;
            $platformFeePercentage = $this->config['platform_fee_percentage'] / 100;
            $platformFee = $coursePrice * $platformFeePercentage;
            $instructorAmount = $coursePrice - $platformFee;

            // Create payment record
            $payment = Payment::create([
                'user_id' => $user->id,
                'course_id' => $course->id,
                'instructor_id' => $course->instructor_id,
                'payment_method' => 'paypal',
                'amount' => $coursePrice,
                'instructor_amount' => $instructorAmount,
                'platform_fee' => $platformFee,
                'currency' => $this->config['currency'],
                'status' => Payment::STATUS_PENDING,
                'type' => Payment::TYPE_COURSE_PURCHASE,
                'metadata' => [
                    'course_title' => $course->title,
                    'user_email' => $user->email,
                    'created_via' => 'paypal_service'
                ]
            ]);

            $orderData = [
                'intent' => 'CAPTURE',
                'purchase_units' => [
                    [
                        'reference_id' => $payment->id,
                        'amount' => [
                            'currency_code' => $this->config['currency'],
                            'value' => number_format($coursePrice, 2, '.', '')
                        ],
                        'description' => "Course: {$course->title}",
                        'custom_id' => $payment->id,
                        'invoice_id' => "INV-{$payment->id}"
                    ]
                ],
                'application_context' => [
                    'brand_name' => config('app.name'),
                    'locale' => 'en-US',
                    'landing_page' => 'BILLING',
                    'shipping_preference' => 'NO_SHIPPING',
                    'user_action' => 'PAY_NOW',
                    'return_url' => route('paypal.success'),
                    'cancel_url' => route('paypal.cancel')
                ]
            ];

            $response = Http::withToken($this->getAccessToken())
                ->post($this->baseUrl . '/v2/checkout/orders', $orderData);

            if (!$response->successful()) {
                throw new Exception('PayPal API error: ' . $response->body());
            }

            $orderResult = $response->json();

            // Update payment with PayPal order ID
            $payment->update([
                'payment_id' => $orderResult['id'],
                'metadata' => array_merge($payment->metadata ?? [], [
                    'paypal_order_id' => $orderResult['id'],
                    'paypal_status' => $orderResult['status']
                ])
            ]);

            Log::info('PayPal order created', [
                'payment_id' => $payment->id,
                'paypal_order_id' => $orderResult['id'],
                'course_id' => $course->id,
                'user_id' => $user->id
            ]);

            return [
                'success' => true,
                'payment' => $payment,
                'paypal_order_id' => $orderResult['id'],
                'approval_url' => $this->getApprovalUrl($orderResult['links'])
            ];

        } catch (Exception $e) {
            Log::error('PayPal order creation failed', [
                'error' => $e->getMessage(),
                'course_id' => $course->id,
                'user_id' => $user->id
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Capture PayPal order
     */
    public function captureOrder(string $orderId): array
    {
        try {
            // Log the exact request being made for debugging
            Log::info('PayPal capture request details', [
                'order_id' => $orderId,
                'url' => $this->baseUrl . "/v2/checkout/orders/{$orderId}/capture",
                'method' => 'POST',
                'headers' => ['Authorization' => 'Bearer [REDACTED]', 'Content-Type' => 'application/json'],
                'body' => 'EMPTY_JSON_OBJECT'
            ]);

            // CRITICAL FIX: PayPal's capture API expects Content-Type: application/json with empty JSON object {}
            // Based on official PayPal documentation: https://developer.paypal.com/api/rest/integration/orders-api/api-use-cases/standard/
            $response = Http::withToken($this->getAccessToken())
                ->withHeaders(['Content-Type' => 'application/json'])
                ->post($this->baseUrl . "/v2/checkout/orders/{$orderId}/capture", (object)[]);

            // Log the response for debugging
            Log::info('PayPal capture response', [
                'order_id' => $orderId,
                'status_code' => $response->status(),
                'successful' => $response->successful(),
                'response_body' => $response->body(),
                'response_headers' => $response->headers()
            ]);

            if (!$response->successful()) {
                // Parse the error response for better debugging
                $errorData = $response->json();
                Log::error('PayPal capture API detailed error', [
                    'order_id' => $orderId,
                    'status_code' => $response->status(),
                    'error_name' => $errorData['name'] ?? 'UNKNOWN',
                    'error_message' => $errorData['message'] ?? 'Unknown error',
                    'error_details' => $errorData['details'] ?? [],
                    'debug_id' => $errorData['debug_id'] ?? null,
                    'full_response' => $response->body()
                ]);

                throw new Exception('PayPal capture API error: ' . $response->body());
            }

            $captureResult = $response->json();
            $payment = Payment::where('payment_id', $orderId)->first();

            if (!$payment) {
                throw new Exception("Payment not found for PayPal order: {$orderId}");
            }

            if ($captureResult['status'] === 'COMPLETED') {
                $payment->markAsCompleted();

                // Update metadata with capture details
                $captureDetails = $captureResult['purchase_units'][0]['payments']['captures'][0] ?? null;
                $payment->update([
                    'metadata' => array_merge($payment->metadata ?? [], [
                        'paypal_capture_id' => $captureDetails['id'] ?? null,
                        'paypal_status' => $captureResult['status'],
                        'captured_at' => now()->toISOString()
                    ])
                ]);

                Log::info('PayPal payment captured successfully', [
                    'payment_id' => $payment->id,
                    'paypal_order_id' => $orderId,
                    'capture_id' => $captureDetails['id'] ?? null
                ]);

                return [
                    'success' => true,
                    'payment' => $payment,
                    'capture_details' => $captureDetails
                ];
            } else {
                $payment->markAsFailed();

                Log::warning('PayPal payment capture failed', [
                    'payment_id' => $payment->id,
                    'paypal_order_id' => $orderId,
                    'status' => $captureResult['status']
                ]);

                return [
                    'success' => false,
                    'error' => 'Payment capture failed',
                    'status' => $captureResult['status']
                ];
            }

        } catch (Exception $e) {
            Log::error('PayPal capture failed', [
                'error' => $e->getMessage(),
                'order_id' => $orderId
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get PayPal order details
     */
    public function getOrderDetails(string $orderId): array
    {
        try {
            $response = Http::withToken($this->getAccessToken())
                ->get($this->baseUrl . "/v2/checkout/orders/{$orderId}");

            if (!$response->successful()) {
                throw new Exception('PayPal get order API error: ' . $response->body());
            }

            return [
                'success' => true,
                'order' => $response->json()
            ];

        } catch (Exception $e) {
            Log::error('Failed to get PayPal order details', [
                'error' => $e->getMessage(),
                'order_id' => $orderId
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Extract approval URL from PayPal response links
     */
    private function getApprovalUrl(array $links): ?string
    {
        foreach ($links as $link) {
            if (isset($link['rel']) && $link['rel'] === 'approve') {
                return $link['href'] ?? null;
            }
        }
        return null;
    }

    /**
     * Verify webhook signature (for webhook security)
     */
    public function verifyWebhookSignature(array $headers, string $body): bool
    {
        try {
            // Get required headers
            $authAlgo = $headers['paypal-auth-algo'][0] ?? null;
            $transmission = $headers['paypal-transmission-id'][0] ?? null;
            $certId = $headers['paypal-cert-id'][0] ?? null;
            $transmissionSig = $headers['paypal-transmission-sig'][0] ?? null;
            $transmissionTime = $headers['paypal-transmission-time'][0] ?? null;

            if (!$authAlgo || !$transmission || !$certId || !$transmissionSig || !$transmissionTime) {
                Log::warning('PayPal webhook missing required headers');
                return false;
            }

            // For production, implement proper webhook verification using PayPal's verification API
            // This is a simplified version for development
            $webhookId = $this->config['webhook_id'] ?? null;

            if (!$webhookId) {
                Log::warning('PayPal webhook ID not configured');
                return false;
            }

            // In production, you would:
            // 1. Get the PayPal certificate using the cert_id
            // 2. Verify the signature using the certificate and transmission data
            // 3. Check the transmission time to prevent replay attacks

            // For now, we'll do basic validation
            if (strlen($transmissionSig) < 10 || strlen($transmission) < 10) {
                Log::warning('PayPal webhook signature validation failed');
                return false;
            }

            return true;

        } catch (Exception $e) {
            Log::error('PayPal webhook signature verification error', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Process webhook event
     */
    public function processWebhookEvent(array $eventData): array
    {
        try {
            $eventType = $eventData['event_type'] ?? null;
            $resource = $eventData['resource'] ?? null;

            Log::info('Processing PayPal webhook', [
                'event_type' => $eventType,
                'resource_id' => $resource['id'] ?? null
            ]);

            switch ($eventType) {
                case 'PAYMENT.CAPTURE.COMPLETED':
                    return $this->handlePaymentCaptureCompleted($resource);
                
                case 'PAYMENT.CAPTURE.DENIED':
                case 'PAYMENT.CAPTURE.DECLINED':
                    return $this->handlePaymentCaptureFailed($resource);
                
                default:
                    Log::info('Unhandled PayPal webhook event', ['event_type' => $eventType]);
                    return ['success' => true, 'message' => 'Event not handled'];
            }

        } catch (Exception $e) {
            Log::error('PayPal webhook processing failed', [
                'error' => $e->getMessage(),
                'event_data' => $eventData
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Handle payment capture completed webhook
     */
    private function handlePaymentCaptureCompleted(array $resource): array
    {
        $customId = $resource['custom_id'] ?? null;
        
        if (!$customId) {
            return ['success' => false, 'error' => 'No custom_id in webhook'];
        }

        $payment = Payment::find($customId);
        
        if (!$payment) {
            return ['success' => false, 'error' => 'Payment not found'];
        }

        if ($payment->status !== Payment::STATUS_COMPLETED) {
            $payment->markAsCompleted();
            
            Log::info('Payment marked as completed via webhook', [
                'payment_id' => $payment->id
            ]);
        }

        return ['success' => true, 'payment' => $payment];
    }

    /**
     * Handle payment capture failed webhook
     */
    private function handlePaymentCaptureFailed(array $resource): array
    {
        $customId = $resource['custom_id'] ?? null;
        
        if (!$customId) {
            return ['success' => false, 'error' => 'No custom_id in webhook'];
        }

        $payment = Payment::find($customId);
        
        if (!$payment) {
            return ['success' => false, 'error' => 'Payment not found'];
        }

        $payment->markAsFailed();
        
        Log::warning('Payment marked as failed via webhook', [
            'payment_id' => $payment->id
        ]);

        return ['success' => true, 'payment' => $payment];
    }
}
