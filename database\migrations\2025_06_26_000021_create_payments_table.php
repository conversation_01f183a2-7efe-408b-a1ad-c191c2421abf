<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->uuid('course_id');
            $table->uuid('instructor_id'); // Denormalized for performance
            $table->string('payment_method')->default('paypal'); // paypal, stripe, etc.
            $table->string('payment_provider_id')->nullable(); // External payment ID
            $table->string('transaction_id')->unique()->nullable(); // Our internal transaction ID
            $table->decimal('amount', 10, 2); // Total amount paid
            $table->decimal('instructor_amount', 10, 2); // Amount after platform fee
            $table->decimal('platform_fee', 10, 2)->default(0);
            $table->decimal('processing_fee', 10, 2)->default(0);
            $table->string('currency', 3)->default('USD');
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'])->default('pending');
            $table->json('payment_details')->nullable(); // Provider-specific details
            $table->json('refund_details')->nullable(); // Refund information if applicable
            $table->timestamp('paid_at')->nullable();
            $table->timestamp('refunded_at')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('cascade');
            $table->foreign('instructor_id')->references('id')->on('users')->onDelete('cascade');

            // Indexes for performance
            $table->index(['user_id', 'status']);
            $table->index(['course_id', 'status']);
            $table->index(['instructor_id', 'status']);
            $table->index(['status', 'paid_at']);
            $table->index(['payment_method', 'status']);
            $table->index('payment_provider_id');
            $table->index('transaction_id');
            $table->index('paid_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
