@extends('instructor.layouts.app')

@section('title', 'Edit Blog Post - ' . $blogPost->title)

@section('content')
<div class="py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-4 mb-4">
                <a href="{{ route('instructor.blog-posts.show', $blogPost) }}" class="text-gray-400 hover:text-white transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <h1 class="text-3xl font-bold text-white">Edit Blog Post</h1>
            </div>
            <p class="text-gray-400">Update your blog post content and settings</p>
        </div>

        <!-- Form -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
            <form method="POST" action="{{ route('instructor.blog-posts.update', $blogPost) }}" enctype="multipart/form-data" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- Title -->
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-300 mb-2">Title *</label>
                    <input type="text" name="title" id="title" value="{{ old('title', $blogPost->title) }}" 
                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('title') border-red-500 @enderror"
                           placeholder="Enter blog post title" required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Excerpt -->
                <div>
                    <label for="excerpt" class="block text-sm font-medium text-gray-300 mb-2">Excerpt</label>
                    <textarea name="excerpt" id="excerpt" rows="3" 
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('excerpt') border-red-500 @enderror"
                              placeholder="Brief summary of the blog post (optional)">{{ old('excerpt', $blogPost->excerpt) }}</textarea>
                    @error('excerpt')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Content -->
                <div>
                    <label for="content" class="block text-sm font-medium text-gray-300 mb-2">Content *</label>
                    <textarea name="content" id="content" rows="15" 
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('content') border-red-500 @enderror"
                              placeholder="Write your blog post content here..." required>{{ old('content', $blogPost->content) }}</textarea>
                    @error('content')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-xs text-gray-500">You can use Markdown formatting for rich text content.</p>
                </div>

                <!-- Category and Course Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Category -->
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-300 mb-2">Category</label>
                        <select name="category" id="category" 
                                class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('category') border-red-500 @enderror">
                            <option value="">Select a category</option>
                            <option value="tutorial" {{ old('category', $blogPost->category) === 'tutorial' ? 'selected' : '' }}>Tutorial</option>
                            <option value="tips" {{ old('category', $blogPost->category) === 'tips' ? 'selected' : '' }}>Tips & Tricks</option>
                            <option value="news" {{ old('category', $blogPost->category) === 'news' ? 'selected' : '' }}>News & Updates</option>
                            <option value="case-study" {{ old('category', $blogPost->category) === 'case-study' ? 'selected' : '' }}>Case Study</option>
                            <option value="opinion" {{ old('category', $blogPost->category) === 'opinion' ? 'selected' : '' }}>Opinion</option>
                            <option value="review" {{ old('category', $blogPost->category) === 'review' ? 'selected' : '' }}>Review</option>
                            <option value="other" {{ old('category', $blogPost->category) === 'other' ? 'selected' : '' }}>Other</option>
                        </select>
                        @error('category')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Course -->
                    <div>
                        <label for="course_id" class="block text-sm font-medium text-gray-300 mb-2">Related Course (Optional)</label>
                        <select name="course_id" id="course_id" 
                                class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('course_id') border-red-500 @enderror">
                            <option value="">No course association</option>
                            @if(isset($courses))
                                @foreach($courses as $course)
                                    <option value="{{ $course->id }}" {{ old('course_id', $blogPost->course_id) === $course->id ? 'selected' : '' }}>
                                        {{ $course->title }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                        @error('course_id')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Tags -->
                <div>
                    <label for="tags" class="block text-sm font-medium text-gray-300 mb-2">Tags</label>
                    <input type="text" name="tags" id="tags" value="{{ old('tags', isset($blogPost) ? implode(', ', $blogPost->tags ?? []) : '') }}" 
                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('tags') border-red-500 @enderror"
                           placeholder="Enter tags separated by commas (e.g., laravel, php, tutorial)">
                    @error('tags')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-xs text-gray-500">Separate multiple tags with commas.</p>
                </div>

                <!-- Featured Image Upload -->
                <div>
                    <label for="featured_image" class="block text-sm font-medium text-gray-300 mb-2">Featured Image (Optional)</label>
                    
                    @if($blogPost->featured_image_url)
                        <div class="mb-4">
                            <img src="{{ $blogPost->featured_image_url }}" alt="Current featured image" class="w-48 h-32 object-cover rounded-lg">
                            <p class="text-gray-400 text-sm mt-1">Current featured image</p>
                        </div>
                    @endif
                    
                    <input type="file" name="featured_image" id="featured_image" accept="image/*" 
                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-600 file:text-white hover:file:bg-blue-700 @error('featured_image') border-red-500 @enderror">
                    @error('featured_image')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Status and Publication Date -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Publication Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-300 mb-2">Publication Status *</label>
                        <div class="relative">
                            <select name="status" id="status"
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('status') border-red-500 @enderror appearance-none" required>
                                <option value="draft" {{ old('status', $blogPost->status) == 'draft' ? 'selected' : '' }}>
                                    📝 Draft - Not visible to readers
                                </option>
                                <option value="published" {{ old('status', $blogPost->status) == 'published' ? 'selected' : '' }}>
                                    🌟 Published - Live and available to readers
                                </option>
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        @error('status')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">Choose the visibility status for this blog post</p>
                    </div>

                    <!-- Publication Date -->
                    <div>
                        <label for="published_at" class="block text-sm font-medium text-gray-300 mb-2">Publication Date</label>
                        <input type="datetime-local" name="published_at" id="published_at" 
                               value="{{ old('published_at', isset($blogPost) && $blogPost->published_at ? $blogPost->published_at->format('Y-m-d\TH:i') : '') }}" 
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('published_at') border-red-500 @enderror">
                        @error('published_at')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Leave empty to publish immediately when status is set to published.</p>
                    </div>
                </div>

                <!-- SEO Meta Description -->
                <div>
                    <label for="meta_description" class="block text-sm font-medium text-gray-300 mb-2">Meta Description (SEO)</label>
                    <textarea name="meta_description" id="meta_description" rows="2" 
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('meta_description') border-red-500 @enderror"
                              placeholder="Brief description for search engines (optional)">{{ old('meta_description', $blogPost->meta_description) }}</textarea>
                    @error('meta_description')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-xs text-gray-500">Recommended length: 150-160 characters.</p>
                </div>

                <!-- Submit Buttons -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-800">
                    <a href="{{ route('instructor.blog-posts.show', $blogPost) }}" class="px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" name="action" value="save_draft" class="px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors">
                        Save as Draft
                    </button>
                    <button type="submit" name="action" value="publish" class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                        Update Post
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle form submission based on button clicked
        document.querySelectorAll('button[name="action"]').forEach(button => {
            button.addEventListener('click', function() {
                const statusSelect = document.querySelector('select[name="status"]');
                if (this.value === 'save_draft') {
                    statusSelect.value = 'draft';
                } else if (this.value === 'publish') {
                    statusSelect.value = 'published';
                }
            });
        });

        // Character counter for meta description
        const metaDescInput = document.getElementById('meta_description');
        if (metaDescInput) {
            const counterDiv = document.createElement('div');
            counterDiv.className = 'text-xs text-gray-500 mt-1';
            counterDiv.id = 'meta-desc-counter';
            metaDescInput.parentNode.appendChild(counterDiv);

            function updateCounter() {
                const length = metaDescInput.value.length;
                const color = length > 160 ? 'text-red-400' : length > 150 ? 'text-yellow-400' : 'text-gray-500';
                counterDiv.className = `text-xs mt-1 ${color}`;
                counterDiv.textContent = `${length}/160 characters`;
            }

            metaDescInput.addEventListener('input', updateCounter);
            updateCounter(); // Initial count
        }
    });
</script>
@endpush
@endsection
