<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chapters', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('title');
            $table->string('slug');
            $table->text('description')->nullable();
            $table->text('learning_objectives')->nullable(); // JSON array of objectives
            $table->uuid('course_id');
            $table->uuid('instructor_id');
            $table->integer('sort_order')->default(0);
            $table->boolean('is_published')->default(false);
            $table->boolean('is_free_preview')->default(false); // Allow free preview of this chapter
            $table->integer('total_duration_minutes')->default(0); // Auto-calculated
            $table->integer('total_lectures')->default(0); // Auto-calculated
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('cascade');
            $table->foreign('instructor_id')->references('id')->on('users')->onDelete('cascade');

            // Indexes for performance
            $table->index(['course_id', 'sort_order']);
            $table->index(['instructor_id', 'is_published']);
            $table->index(['course_id', 'is_published']);
            $table->index('slug');
            
            // Unique constraint for slug within course
            $table->unique(['course_id', 'slug']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chapters');
    }
};
