<?php

namespace App\Providers;

use App\Models\BlogPost;
use App\Models\VideoContent;
use App\Models\LearningMaterial;
use App\Models\Ebook;
use App\Models\Resource;
use App\Models\Payment;
use App\Models\User;
use App\Models\Course;
use App\Models\Chapter;
use App\Models\Lecture;
use App\Policies\InstructorContentPolicy;
use App\Policies\CoursePolicy;
use App\Policies\ChapterPolicy;
use App\Policies\LecturePolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        BlogPost::class => InstructorContentPolicy::class,
        VideoContent::class => InstructorContentPolicy::class,
        LearningMaterial::class => InstructorContentPolicy::class,
        Ebook::class => InstructorContentPolicy::class,
        Resource::class => InstructorContentPolicy::class,
        Course::class => CoursePolicy::class,
        Chapter::class => ChapterPolicy::class,
        Lecture::class => LecturePolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Define custom gates for instructor access
        Gate::define('access-instructor-dashboard', function (User $user) {
            return $user->isInstructor() || $user->isAdmin() || $user->isSuperAdmin();
        });

        Gate::define('manage-students', function (User $user) {
            return $user->isInstructor() || $user->isAdmin() || $user->isSuperAdmin();
        });

        Gate::define('view-payments', function (User $user) {
            return $user->isInstructor() || $user->isAdmin() || $user->isSuperAdmin();
        });

        Gate::define('manage-content', function (User $user) {
            return $user->isInstructor() || $user->isAdmin() || $user->isSuperAdmin();
        });

        // Define gate for viewing specific user data
        Gate::define('view-user-data', function (User $user, User $targetUser) {
            // Super admin can view all users
            if ($user->isSuperAdmin()) {
                return true;
            }

            // Admin can view all users except super admins
            if ($user->isAdmin()) {
                return !$targetUser->isSuperAdmin();
            }

            // Instructors can only view students enrolled in their courses
            if ($user->isInstructor()) {
                $instructorCourseIds = $user->courses()->pluck('id');
                return $targetUser->enrollments()
                    ->whereIn('course_id', $instructorCourseIds)
                    ->exists();
            }

            // Users can view their own data
            return $user->id === $targetUser->id;
        });

        // Define gate for viewing payment data
        Gate::define('view-payment-data', function (User $user, Payment $payment) {
            // Super admin can view all payments
            if ($user->isSuperAdmin()) {
                return true;
            }

            // Admin can view all payments
            if ($user->isAdmin()) {
                return true;
            }

            // Instructors can only view payments for their courses
            if ($user->isInstructor()) {
                return $payment->instructor_id === $user->id;
            }

            // Users can view their own payments
            return $payment->user_id === $user->id;
        });

        // Define gate for course ownership
        Gate::define('owns-course', function (User $user, $courseId) {
            return $user->courses()->where('id', $courseId)->exists();
        });

        // Define gate for content ownership
        Gate::define('owns-content', function (User $user, $content) {
            if (method_exists($content, 'instructor_id')) {
                return $content->instructor_id === $user->id;
            }
            return false;
        });

        // Rate limiting gates
        Gate::define('can-create-content', function (User $user) {
            // Limit content creation based on user role
            if ($user->isSuperAdmin() || $user->isAdmin()) {
                return true;
            }

            if ($user->isInstructor()) {
                // Check if instructor hasn't exceeded daily content creation limit
                $today = now()->startOfDay();
                $contentCount = collect([
                    $user->blogPosts()->whereDate('created_at', $today)->count(),
                    $user->videoContents()->whereDate('created_at', $today)->count(),
                    $user->learningMaterials()->whereDate('created_at', $today)->count(),
                ])->sum();

                return $contentCount < 50; // Max 50 content items per day
            }

            return false;
        });
    }
}
