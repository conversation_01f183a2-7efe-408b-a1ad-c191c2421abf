<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\Chapter;
use App\Models\Lecture;
use App\Models\Enrollment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class EnrolledCourseViewingTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $student;
    protected $instructor;
    protected $course;
    protected $enrollment;
    protected $chapters;
    protected $lectures;

    protected function setUp(): void
    {
        parent::setUp();

        // Create instructor
        $this->instructor = User::factory()->create([
            'role' => 'instructor',
            'email' => '<EMAIL>'
        ]);

        // Create student
        $this->student = User::factory()->create([
            'role' => 'student',
            'email' => '<EMAIL>'
        ]);

        // Create course
        $this->course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'title' => 'Advanced JavaScript and ES6',
            'slug' => 'advanced-javascript-and-es6',
            'status' => 'published',
            'price' => 0 // Free course for testing
        ]);

        // Create chapters and lectures
        $this->createCourseStructure();

        // Create enrollment
        $this->enrollment = Enrollment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'enrolled_at' => now(),
            'status' => 'active',
            'progress_percentage' => 0,
            'completed_lectures' => 0,
            'total_lectures' => $this->lectures->count(),
            'completed_lecture_ids' => [],
        ]);
    }

    protected function createCourseStructure()
    {
        $this->chapters = collect();
        $this->lectures = collect();

        // Chapter 1: Introduction
        $chapter1 = Chapter::factory()->create([
            'course_id' => $this->course->id,
            'title' => 'Introduction to ES6',
            'sort_order' => 1,
            'is_published' => true
        ]);
        $this->chapters->push($chapter1);

        // Lectures for Chapter 1
        $lecture1 = Lecture::factory()->create([
            'course_id' => $this->course->id,
            'chapter_id' => $chapter1->id,
            'title' => 'Welcome to the Course',
            'type' => 'video',
            'sort_order' => 1,
            'is_published' => true,
            'duration_minutes' => 5
        ]);
        $this->lectures->push($lecture1);

        $lecture2 = Lecture::factory()->create([
            'course_id' => $this->course->id,
            'chapter_id' => $chapter1->id,
            'title' => 'Course Overview',
            'type' => 'text',
            'sort_order' => 2,
            'is_published' => true,
            'duration_minutes' => 3
        ]);
        $this->lectures->push($lecture2);

        // Chapter 2: Advanced Topics
        $chapter2 = Chapter::factory()->create([
            'course_id' => $this->course->id,
            'title' => 'Advanced ES6 Features',
            'sort_order' => 2,
            'is_published' => true
        ]);
        $this->chapters->push($chapter2);

        // Lectures for Chapter 2
        $lecture3 = Lecture::factory()->create([
            'course_id' => $this->course->id,
            'chapter_id' => $chapter2->id,
            'title' => 'Arrow Functions',
            'type' => 'video',
            'sort_order' => 1,
            'is_published' => true,
            'duration_minutes' => 10
        ]);
        $this->lectures->push($lecture3);

        $lecture4 = Lecture::factory()->create([
            'course_id' => $this->course->id,
            'chapter_id' => $chapter2->id,
            'title' => 'Destructuring Assignment',
            'type' => 'quiz',
            'sort_order' => 2,
            'is_published' => true,
            'duration_minutes' => 8
        ]);
        $this->lectures->push($lecture4);
    }

    /** @test */
    public function enrolled_student_can_view_course()
    {
        $this->actingAs($this->student);

        $response = $this->get(route('my-courses.view', $this->course));

        $response->assertStatus(200);
        $response->assertSee($this->course->title);
        $response->assertSee('Progress');
        $response->assertSee('0.0%'); // Initial progress
        $response->assertSee('0/4 lessons'); // Total lectures
    }

    /** @test */
    public function enrolled_course_view_contains_responsive_elements()
    {
        $this->actingAs($this->student);

        $response = $this->get(route('my-courses.view', $this->course));

        $response->assertStatus(200);
        
        // Check for mobile sidebar toggle
        $response->assertSee('id="mobile-sidebar-toggle"', false);
        
        // Check for mobile sidebar overlay
        $response->assertSee('id="mobile-sidebar-overlay"', false);
        
        // Check for mobile sidebar close button
        $response->assertSee('id="mobile-sidebar-close"', false);
        
        // Check for responsive sidebar classes
        $response->assertSee('lg:relative lg:translate-x-0', false);
        $response->assertSee('-translate-x-full', false);
    }

    /** @test */
    public function enrolled_course_view_contains_progress_tracking_elements()
    {
        $this->actingAs($this->student);

        $response = $this->get(route('my-courses.view', $this->course));

        $response->assertStatus(200);
        
        // Check for progress tracking IDs
        $response->assertSee('id="progress-bar"', false);
        $response->assertSee('id="progress-percentage"', false);
        $response->assertSee('id="lecture-count"', false);
    }

    /** @test */
    public function student_can_view_specific_lecture()
    {
        $this->actingAs($this->student);
        $lecture = $this->lectures->first();

        $response = $this->get(route('my-courses.lecture', [$this->course, $lecture]));

        $response->assertStatus(200);
        $response->assertSee($lecture->title);
        $response->assertSee('Mark as Complete');
    }

    /** @test */
    public function student_can_mark_lecture_as_complete()
    {
        $this->actingAs($this->student);
        $lecture = $this->lectures->first();

        $response = $this->postJson(route('my-courses.lecture.complete', [$this->course, $lecture]));

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'progress' => 25.0, // 1 out of 4 lectures
            'completed_lectures' => 1,
            'total_lectures' => 4
        ]);

        // Verify database update
        $this->enrollment->refresh();
        $this->assertEquals(25.0, $this->enrollment->progress_percentage);
        $this->assertEquals(1, $this->enrollment->completed_lectures);
        $this->assertContains($lecture->id, $this->enrollment->completed_lecture_ids);
    }

    /** @test */
    public function progress_updates_correctly_with_multiple_completions()
    {
        $this->actingAs($this->student);

        // Complete first lecture
        $lecture1 = $this->lectures->get(0);
        $this->postJson(route('my-courses.lecture.complete', [$this->course, $lecture1]));

        // Complete second lecture
        $lecture2 = $this->lectures->get(1);
        $response = $this->postJson(route('my-courses.lecture.complete', [$this->course, $lecture2]));

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'progress' => 50.0, // 2 out of 4 lectures
            'completed_lectures' => 2,
            'total_lectures' => 4
        ]);

        // Verify database update
        $this->enrollment->refresh();
        $this->assertEquals(50.0, $this->enrollment->progress_percentage);
        $this->assertEquals(2, $this->enrollment->completed_lectures);
    }

    /** @test */
    public function course_marked_as_completed_when_all_lectures_finished()
    {
        $this->actingAs($this->student);

        // Complete all lectures
        foreach ($this->lectures as $lecture) {
            $this->postJson(route('my-courses.lecture.complete', [$this->course, $lecture]));
        }

        // Verify course completion
        $this->enrollment->refresh();
        $this->assertEquals(100.0, $this->enrollment->progress_percentage);
        $this->assertEquals('completed', $this->enrollment->status);
        $this->assertNotNull($this->enrollment->completed_at);
    }

    /** @test */
    public function non_enrolled_student_cannot_access_course()
    {
        $otherStudent = User::factory()->create(['role' => 'student']);
        $this->actingAs($otherStudent);

        $response = $this->get(route('my-courses.view', $this->course));

        $response->assertStatus(404);
    }

    /** @test */
    public function unauthenticated_user_cannot_access_enrolled_course()
    {
        $response = $this->get(route('my-courses.view', $this->course));

        $response->assertRedirect(route('login'));
    }

    /** @test */
    public function enrolled_course_view_contains_mobile_css()
    {
        $this->actingAs($this->student);

        $response = $this->get(route('my-courses.view', $this->course));

        $response->assertStatus(200);
        
        // Check for mobile-specific CSS
        $response->assertSee('@media (max-width: 1023px)', false);
        $response->assertSee('min-height: 48px', false);
        $response->assertSee('min-height: 44px', false);
        $response->assertSee('touch-feedback', false);
    }

    /** @test */
    public function enrolled_course_view_contains_mobile_javascript()
    {
        $this->actingAs($this->student);

        $response = $this->get(route('my-courses.view', $this->course));

        $response->assertStatus(200);
        
        // Check for mobile JavaScript functions
        $response->assertSee('initializeMobileNavigation', false);
        $response->assertSee('handleLectureClick', false);
        $response->assertSee('initializeSwipeNavigation', false);
    }
}
