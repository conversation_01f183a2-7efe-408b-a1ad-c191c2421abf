<?php

namespace App\Http\Controllers\Instructor;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Chapter;
use App\Models\Lecture;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class ChapterController extends Controller
{
    /**
     * Display a listing of chapters for a course.
     */
    public function index(Course $course)
    {
        $this->authorize('view', $course);

        $chapters = $course->chapters()
            ->with(['lectures'])
            ->withCount(['lectures'])
            ->orderBy('sort_order')
            ->get();

        return view('instructor.chapters.index', compact('course', 'chapters'));
    }

    /**
     * Add chapter from integrated course management page.
     */
    public function addChapter(Request $request, Course $course)
    {
        $this->authorize('update', $course);

        // Validate chapter data
        $request->validate([
            'chapters' => 'required|array|min:1',
            'chapters.*.title' => 'required|string|max:255',
            'chapters.*.description' => 'nullable|string|max:2000',
            'chapters.*.learning_objectives' => 'nullable|string|max:2000',
            'chapters.*.is_free_preview' => 'boolean',

            // Lecture validation
            'chapters.*.lectures' => 'required|array|min:1',
            'chapters.*.lectures.*.title' => 'required|string|max:255',
            'chapters.*.lectures.*.description' => 'nullable|string|max:1000',
            'chapters.*.lectures.*.type' => 'required|in:video,text,quiz,assignment,resource',
            'chapters.*.lectures.*.is_free_preview' => 'boolean',
            'chapters.*.lectures.*.is_mandatory' => 'boolean',

            // Type-specific validation - simplified for nested arrays
            'chapters.*.lectures.*.video_url' => 'nullable|url',
            'chapters.*.lectures.*.duration_minutes' => 'nullable|integer|min:1|max:300',
            'chapters.*.lectures.*.content' => 'nullable|string',
            'chapters.*.lectures.*.estimated_completion_minutes' => 'nullable|integer|min:1|max:1440',
            'chapters.*.lectures.*.allow_comments' => 'boolean',
        ]);

        // Custom validation for lecture type-specific fields
        foreach ($request->chapters as $chapterIndex => $chapterData) {
            if (isset($chapterData['lectures']) && is_array($chapterData['lectures'])) {
                foreach ($chapterData['lectures'] as $lectureIndex => $lecture) {
                    if (isset($lecture['type'])) {
                        if ($lecture['type'] === 'video' && empty($lecture['video_url'])) {
                            throw \Illuminate\Validation\ValidationException::withMessages([
                                "chapters.{$chapterIndex}.lectures.{$lectureIndex}.video_url" => ['Video URL is required for video lectures.']
                            ]);
                        }
                        if (in_array($lecture['type'], ['text', 'quiz', 'assignment', 'resource']) && empty($lecture['content'])) {
                            throw \Illuminate\Validation\ValidationException::withMessages([
                                "chapters.{$chapterIndex}.lectures.{$lectureIndex}.content" => ['Content is required for this lecture type.']
                            ]);
                        }
                    }
                }
            }
        }

        try {
            DB::beginTransaction();

            $createdChapters = [];

            foreach ($request->chapters as $index => $chapterData) {
                // Process learning objectives
                $learningObjectives = null;
                if (!empty($chapterData['learning_objectives'])) {
                    $learningObjectives = array_filter(
                        array_map('trim', explode("\n", $chapterData['learning_objectives']))
                    );
                }

                // Create chapter
                $chapter = Chapter::create([
                    'title' => $chapterData['title'],
                    'description' => $chapterData['description'] ?? null,
                    'learning_objectives' => $learningObjectives,
                    'course_id' => $course->id,
                    'instructor_id' => $course->instructor_id,
                    'slug' => $this->generateUniqueSlug($chapterData['title'], $course->id),
                    'sort_order' => $course->chapters()->count() + $index + 1,
                    'is_published' => false,
                    'is_free_preview' => $chapterData['is_free_preview'] ?? false,
                ]);

                // Create lectures for this chapter
                if (isset($chapterData['lectures'])) {
                    $this->createLectures($chapterData['lectures'], $chapter, $course);
                }

                $createdChapters[] = $chapter;
            }

            DB::commit();

            $chapterCount = count($createdChapters);
            $lectureCount = array_sum(array_map(function($chapterData) {
                return count($chapterData['lectures'] ?? []);
            }, $request->chapters));

            return redirect()->route('instructor.courses.show', $course)
                ->with('success', "Successfully created {$chapterCount} chapter(s) with {$lectureCount} lecture(s)!");

        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create chapters: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created chapter in storage.
     */
    public function store(Request $request, Course $course)
    {
        try {
            \Log::info('Chapter store started', [
                'course_id' => $course->id,
                'request_data' => $request->all(),
                'is_ajax' => $request->ajax()
            ]);

            $this->authorize('update', $course);

            // Handle JSON lectures data from AJAX requests
            $lecturesData = $request->lectures;
            if (is_string($lecturesData)) {
                $lecturesData = json_decode($lecturesData, true);
            }

            \Log::info('Processed lectures data', [
                'lectures_count' => is_array($lecturesData) ? count($lecturesData) : 0,
                'lectures_data' => $lecturesData
            ]);

        // Validate chapter data
        $validationRules = [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:2000',
            'learning_objectives' => 'nullable|string|max:2000',
            'is_free_preview' => 'nullable|boolean',
        ];

        // Add comprehensive lecture validation if lectures are provided
        if ($lecturesData && is_array($lecturesData) && count($lecturesData) > 0) {
            // Validate lectures array structure
            $validationRules['lectures'] = 'array|min:1';

            // Create a temporary request with lectures data for validation
            $lectureValidationData = [];
            foreach ($lecturesData as $index => $lectureData) {
                $lectureValidationData["lectures.{$index}.title"] = $lectureData['title'] ?? '';
                $lectureValidationData["lectures.{$index}.description"] = $lectureData['description'] ?? null;
                $lectureValidationData["lectures.{$index}.type"] = $lectureData['type'] ?? '';
                $lectureValidationData["lectures.{$index}.is_free_preview"] = $lectureData['is_free_preview'] ?? false;
                $lectureValidationData["lectures.{$index}.is_mandatory"] = $lectureData['is_mandatory'] ?? true;

                // Add type-specific data
                if (isset($lectureData['video_url'])) {
                    $lectureValidationData["lectures.{$index}.video_url"] = $lectureData['video_url'];
                }
                if (isset($lectureData['duration_minutes'])) {
                    $lectureValidationData["lectures.{$index}.duration_minutes"] = $lectureData['duration_minutes'];
                }
                if (isset($lectureData['content'])) {
                    $lectureValidationData["lectures.{$index}.content"] = $lectureData['content'];
                }
                if (isset($lectureData['estimated_completion_minutes'])) {
                    $lectureValidationData["lectures.{$index}.estimated_completion_minutes"] = $lectureData['estimated_completion_minutes'];
                }
            }

            // Add lecture validation rules
            $validationRules = array_merge($validationRules, [
                'lectures.*.title' => 'required|string|max:255',
                'lectures.*.description' => 'nullable|string|max:1000',
                'lectures.*.type' => 'required|in:video,text,quiz,assignment,resource',
                'lectures.*.is_free_preview' => 'nullable|boolean',
                'lectures.*.is_mandatory' => 'nullable|boolean',
                'lectures.*.video_url' => 'nullable|url',
                'lectures.*.duration_minutes' => 'nullable|integer|min:0|max:1440',
                'lectures.*.content' => 'nullable|string',
                'lectures.*.estimated_completion_minutes' => 'nullable|integer|min:0|max:1440',
            ]);

            // Merge lecture data into request for validation
            $request->merge(['lectures' => $lecturesData]);
        }

        DB::beginTransaction();

        $request->validate($validationRules);

        // Custom validation for lecture type-specific fields
        if ($lecturesData && is_array($lecturesData)) {
            foreach ($lecturesData as $index => $lecture) {
                if (isset($lecture['type'])) {
                    if ($lecture['type'] === 'video' && empty($lecture['video_url'])) {
                        throw \Illuminate\Validation\ValidationException::withMessages([
                            "lectures.{$index}.video_url" => ['Video URL is required for video lectures.']
                        ]);
                    }
                    if (in_array($lecture['type'], ['text', 'quiz', 'assignment', 'resource']) && empty($lecture['content'])) {
                        throw \Illuminate\Validation\ValidationException::withMessages([
                            "lectures.{$index}.content" => ['Content is required for this lecture type.']
                        ]);
                    }
                }
            }
        }

            // Process learning objectives
            $learningObjectives = null;
            if ($request->learning_objectives) {
                $learningObjectives = array_filter(
                    array_map('trim', explode("\n", $request->learning_objectives))
                );
            }

            // Create chapter
            $chapterData = [
                'title' => $request->title,
                'description' => $request->description,
                'learning_objectives' => $learningObjectives,
                'course_id' => $course->id,
                'instructor_id' => $course->instructor_id,
                'slug' => $this->generateUniqueSlug($request->title, $course->id),
                'sort_order' => $course->chapters()->count() + 1,
                'is_published' => false,
                'is_free_preview' => $this->parseBooleanValue($request->is_free_preview),
            ];

            $chapter = Chapter::create($chapterData);

            // Create lectures if provided
            if ($lecturesData && is_array($lecturesData)) {
                $this->createLecturesFromArray($lecturesData, $chapter, $course);
            }

            DB::commit();

            \Log::info('Chapter created successfully', [
                'chapter_id' => $chapter->id,
                'lectures_count' => count($lecturesData ?? [])
            ]);

            // Return JSON response for AJAX requests
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Chapter created successfully with ' . count($lecturesData ?? []) . ' lectures!',
                    'chapter' => $chapter->load('lectures')
                ], 200, [
                    'Content-Type' => 'application/json'
                ]);
            }

            return redirect()->route('instructor.courses.chapters.show', [$course, $chapter])
                ->with('success', 'Chapter created successfully with ' . count($lecturesData ?? []) . ' lectures!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            DB::rollBack();

            \Log::error('Chapter validation failed', [
                'errors' => $e->errors(),
                'course_id' => $course->id
            ]);

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $e->errors()
                ], 422, [
                    'Content-Type' => 'application/json'
                ]);
            }
            throw $e;
        } catch (\Exception $e) {
            DB::rollBack();

            \Log::error('Chapter creation failed', [
                'error' => $e->getMessage(),
                'course_id' => $course->id,
                'user_id' => auth()->id()
            ]);

            // Return JSON response for AJAX requests
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create chapter: ' . $e->getMessage()
                ], 500, [
                    'Content-Type' => 'application/json'
                ]);
            }

            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create chapter: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified chapter.
     */
    public function show(Course $course, Chapter $chapter)
    {
        $this->authorize('view', $course);
        $this->ensureChapterBelongsToCourse($chapter, $course);

        $chapter->load(['lectures' => function ($query) {
            $query->orderBy('sort_order');
        }]);

        // Return JSON for AJAX requests
        if (request()->wantsJson() || request()->ajax()) {
            return response()->json([
                'success' => true,
                'data' => $chapter
            ]);
        }

        return view('instructor.chapters.show', compact('course', 'chapter'));
    }

    /**
     * Show the form for editing the specified chapter.
     */
    public function edit(Course $course, Chapter $chapter)
    {
        $this->authorize('update', $course);
        $this->ensureChapterBelongsToCourse($chapter, $course);

        return view('instructor.chapters.edit', compact('course', 'chapter'));
    }

    /**
     * Update the specified chapter in storage.
     */
    public function update(Request $request, Course $course, Chapter $chapter)
    {
        try {
            $this->authorize('update', $course);
            $this->ensureChapterBelongsToCourse($chapter, $course);

            $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'nullable|string|max:2000',
                'learning_objectives' => 'nullable|array',
                'learning_objectives.*' => 'string|max:255',
                'is_free_preview' => 'boolean',
            ]);

            $data = $request->all();

            // Update slug if title changed
            if ($request->title !== $chapter->title) {
                $data['slug'] = $this->generateUniqueSlug($request->title, $course->id, $chapter->id);
            }

            $chapter->update($data);

            // Check if this is an AJAX request
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Chapter updated successfully!',
                    'chapter' => $chapter
                ]);
            }

            return redirect()->route('instructor.courses.show', $course)
                ->with('success', 'Chapter updated successfully!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $e->errors()
                ], 422);
            }
            throw $e;
        } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not authorized to perform this action'
                ], 403);
            }
            throw $e;
        } catch (\Exception $e) {
            \Log::error('Chapter update failed: ' . $e->getMessage(), [
                'chapter_id' => $chapter->id,
                'course_id' => $course->id,
                'user_id' => auth()->id()
            ]);

            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An unexpected error occurred. Please try again.'
                ], 500);
            }

            return redirect()->route('instructor.courses.show', $course)
                ->with('error', 'An unexpected error occurred. Please try again.');
        }
    }

    /**
     * Remove the specified chapter from storage.
     */
    public function destroy(Request $request, Course $course, Chapter $chapter)
    {
        $this->authorize('update', $course);
        $this->ensureChapterBelongsToCourse($chapter, $course);

        // Check if chapter has lectures
        if ($chapter->lectures()->count() > 0) {
            $errorMessage = 'Cannot delete chapter that contains lectures. Please delete all lectures first.';

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $errorMessage
                ], 422);
            }

            return redirect()->route('instructor.courses.show', $course)
                ->with('error', $errorMessage);
        }

        $chapter->delete();

        $successMessage = 'Chapter deleted successfully.';

        if ($request->ajax() || $request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => $successMessage
            ]);
        }

        return redirect()->route('instructor.courses.show', $course)
            ->with('success', $successMessage);
    }

    /**
     * Toggle chapter published status.
     */
    public function toggleStatus(Course $course, Chapter $chapter)
    {
        $this->authorize('update', $course);
        $this->ensureChapterBelongsToCourse($chapter, $course);

        $chapter->update(['is_published' => !$chapter->is_published]);

        $message = $chapter->is_published ? 'Chapter published successfully!' : 'Chapter unpublished successfully.';

        return redirect()->back()->with('success', $message);
    }

    /**
     * Move chapter up in sort order.
     */
    public function moveUp(Course $course, Chapter $chapter)
    {
        $this->authorize('update', $course);
        $this->ensureChapterBelongsToCourse($chapter, $course);

        if ($chapter->moveUp()) {
            return redirect()->back()->with('success', 'Chapter moved up successfully.');
        }

        return redirect()->back()->with('error', 'Cannot move chapter up. It\'s already at the top.');
    }

    /**
     * Move chapter down in sort order.
     */
    public function moveDown(Course $course, Chapter $chapter)
    {
        $this->authorize('update', $course);
        $this->ensureChapterBelongsToCourse($chapter, $course);

        if ($chapter->moveDown()) {
            return redirect()->back()->with('success', 'Chapter moved down successfully.');
        }

        return redirect()->back()->with('error', 'Cannot move chapter down. It\'s already at the bottom.');
    }

    /**
     * Duplicate a chapter.
     */
    public function duplicate(Course $course, Chapter $chapter)
    {
        $this->authorize('update', $course);
        $this->ensureChapterBelongsToCourse($chapter, $course);

        $newChapter = $chapter->replicate();
        $newChapter->title = $chapter->title . ' (Copy)';
        $newChapter->slug = $this->generateUniqueSlug($newChapter->title, $course->id);
        $newChapter->is_published = false;
        $newChapter->sort_order = Chapter::where('course_id', $course->id)->max('sort_order') + 1;
        $newChapter->save();

        // Duplicate lectures
        foreach ($chapter->lectures as $lecture) {
            $newLecture = $lecture->replicate();
            $newLecture->chapter_id = $newChapter->id;
            $newLecture->title = $lecture->title . ' (Copy)';
            $newLecture->slug = $this->generateUniqueLectureSlug($newLecture->title, $newChapter->id);
            $newLecture->is_published = false;
            $newLecture->save();
        }

        return redirect()->route('instructor.courses.chapters.show', [$course, $newChapter])
            ->with('success', 'Chapter duplicated successfully! You can now edit the copy.');
    }

    /**
     * Bulk update chapter order.
     */
    public function updateOrder(Request $request, Course $course)
    {
        $this->authorize('update', $course);

        $request->validate([
            'chapters' => 'required|array',
            'chapters.*' => 'exists:chapters,id',
        ]);

        foreach ($request->chapters as $index => $chapterId) {
            Chapter::where('id', $chapterId)
                ->where('course_id', $course->id)
                ->update(['sort_order' => $index + 1]);
        }

        return response()->json(['success' => true, 'message' => 'Chapter order updated successfully.']);
    }

    /**
     * Generate a unique slug for the chapter within the course.
     */
    private function generateUniqueSlug(string $title, string $courseId, ?string $excludeId = null): string
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $counter = 1;

        while (true) {
            $query = Chapter::where('course_id', $courseId)->where('slug', $slug);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }
            
            if (!$query->exists()) {
                break;
            }
            
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Generate a unique slug for a lecture within the chapter.
     */
    private function generateUniqueLectureSlug(string $title, string $chapterId): string
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $counter = 1;

        while (true) {
            $query = \App\Models\Lecture::where('chapter_id', $chapterId)->where('slug', $slug);
            
            if (!$query->exists()) {
                break;
            }
            
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Create lectures for a chapter.
     */
    private function createLectures(array $lecturesData, Chapter $chapter, Course $course): void
    {
        foreach ($lecturesData as $index => $lectureData) {
            $lecture = $this->createSingleLecture($lectureData, $chapter, $course, $index + 1);

            // Handle file uploads based on lecture type
            if ($lectureData['type'] === 'assignment' && isset($lectureData['assignment_files'])) {
                $this->handleAssignmentFiles($lecture, $lectureData['assignment_files'], $course);
            }

            if ($lectureData['type'] === 'resource' && isset($lectureData['resource_files'])) {
                $this->handleResourceFiles($lecture, $lectureData['resource_files'], $course);
            }
        }
    }

    /**
     * Create lectures from array data (for AJAX requests).
     */
    private function createLecturesFromArray(array $lecturesData, Chapter $chapter, Course $course): void
    {
        foreach ($lecturesData as $index => $lectureData) {
            $data = [
                'title' => $lectureData['title'],
                'description' => $lectureData['description'] ?? null,
                'type' => $lectureData['type'],
                'chapter_id' => $chapter->id,
                'course_id' => $course->id,
                'instructor_id' => $course->instructor_id,
                'sort_order' => $index + 1,
                'is_published' => false,
                'is_free_preview' => $this->parseBooleanValue($lectureData['is_free_preview'] ?? false),
                'is_mandatory' => $this->parseBooleanValue($lectureData['is_mandatory'] ?? true),
                'slug' => $this->generateUniqueLectureSlug($lectureData['title'], $chapter->id),
            ];

            // Type-specific data
            switch ($lectureData['type']) {
                case 'video':
                    $data['video_url'] = $lectureData['video_url'] ?? null;
                    $data['duration_minutes'] = $lectureData['duration_minutes'] ?? null;
                    break;

                case 'text':
                    $data['content'] = $lectureData['content'] ?? null;
                    $data['estimated_completion_minutes'] = $lectureData['estimated_completion_minutes'] ?? null;
                    break;

                case 'quiz':
                case 'assignment':
                case 'resource':
                    $data['content'] = $lectureData['content'] ?? null;
                    break;
            }

            // Handle numeric fields that cannot be null in database
            $numericFields = ['duration_minutes', 'quiz_passing_score', 'estimated_completion_minutes'];
            foreach ($numericFields as $field) {
                if (array_key_exists($field, $data)) {
                    // Convert null/empty values to 0 for database compatibility
                    if (is_null($data[$field]) || $data[$field] === '') {
                        $data[$field] = 0;
                    }
                }
            }

            \App\Models\Lecture::create($data);
        }
    }

    /**
     * Create a single lecture.
     */
    private function createSingleLecture(array $lectureData, Chapter $chapter, Course $course, int $sortOrder): Lecture
    {
        $data = [
            'title' => $lectureData['title'],
            'description' => $lectureData['description'] ?? null,
            'type' => $lectureData['type'],
            'chapter_id' => $chapter->id,
            'course_id' => $course->id,
            'instructor_id' => $course->instructor_id,
            'sort_order' => $sortOrder,
            'is_published' => false,
            'is_free_preview' => $lectureData['is_free_preview'] ?? false,
            'is_mandatory' => $lectureData['is_mandatory'] ?? true,
            'slug' => $this->generateUniqueLectureSlug($lectureData['title'], $chapter->id),
        ];

        // Type-specific data
        switch ($lectureData['type']) {
            case 'video':
                $data['video_url'] = $lectureData['video_url'] ?? null;
                $data['duration_minutes'] = $lectureData['duration_minutes'] ?? null;
                if ($data['video_url']) {
                    $data['video_metadata'] = $this->extractVideoMetadata($data['video_url']);
                }
                break;

            case 'text':
                $data['content'] = $lectureData['content'] ?? null;
                $data['estimated_completion_minutes'] = $lectureData['estimated_completion_minutes'] ?? null;
                break;

            case 'quiz':
                $data['content'] = $lectureData['content'] ?? null;
                $data['quiz_passing_score'] = $lectureData['quiz_passing_score'] ?? 80;
                $data['quiz_allow_retakes'] = $lectureData['quiz_allow_retakes'] ?? true;

                if (isset($lectureData['quiz_questions'])) {
                    $data['quiz_data'] = $this->processQuizQuestions($lectureData['quiz_questions']);
                }
                break;

            case 'assignment':
                $data['content'] = $lectureData['content'] ?? null;
                $data['estimated_completion_minutes'] = $lectureData['estimated_completion_minutes'] ?? null;
                break;

            case 'resource':
                $data['content'] = $lectureData['content'] ?? null;
                break;
        }

        // Handle numeric fields that cannot be null in database
        $numericFields = ['duration_minutes', 'quiz_passing_score', 'estimated_completion_minutes'];
        foreach ($numericFields as $field) {
            if (array_key_exists($field, $data)) {
                // Convert null/empty values to 0 for database compatibility
                if (is_null($data[$field]) || $data[$field] === '') {
                    $data[$field] = 0;
                }
            }
        }

        return Lecture::create($data);
    }

    /**
     * Process quiz questions data.
     */
    private function processQuizQuestions(array $questions): array
    {
        $processedQuestions = [];

        foreach ($questions as $question) {
            $processedQuestion = [
                'question' => $question['question'],
                'type' => $question['type'],
                'points' => $question['points'] ?? 1,
            ];

            switch ($question['type']) {
                case 'multiple_choice':
                    $processedQuestion['options'] = $question['options'] ?? [];
                    $processedQuestion['correct_answer'] = $question['correct_answer'] ?? 0;
                    break;

                case 'true_false':
                    $processedQuestion['correct_answer'] = $question['correct_answer'] ?? 'true';
                    break;

                case 'short_answer':
                    $processedQuestion['sample_answer'] = $question['sample_answer'] ?? '';
                    break;
            }

            $processedQuestions[] = $processedQuestion;
        }

        return $processedQuestions;
    }

    /**
     * Handle assignment file uploads.
     */
    private function handleAssignmentFiles(Lecture $lecture, array $files, Course $course): void
    {
        $attachments = [];

        foreach ($files as $file) {
            if ($file && $file->isValid()) {
                $path = $file->store("private/{$course->instructor_id}/{$course->id}/assignments", 'local');

                $attachments[] = [
                    'name' => $file->getClientOriginalName(),
                    'path' => $path,
                    'size' => $file->getSize(),
                    'type' => $file->getMimeType(),
                ];
            }
        }

        if (!empty($attachments)) {
            $lecture->update(['attachments' => $attachments]);
        }
    }

    /**
     * Handle resource file uploads.
     */
    private function handleResourceFiles(Lecture $lecture, array $files, Course $course): void
    {
        $resources = [];

        foreach ($files as $file) {
            if ($file && $file->isValid()) {
                $path = $file->store("private/{$course->instructor_id}/{$course->id}/resources", 'local');

                $resources[] = [
                    'name' => $file->getClientOriginalName(),
                    'path' => $path,
                    'size' => $file->getSize(),
                    'type' => $file->getMimeType(),
                ];
            }
        }

        if (!empty($resources)) {
            $lecture->update(['resources' => $resources]);
        }
    }

    /**
     * Extract video metadata from URL.
     */
    private function extractVideoMetadata(string $url): array
    {
        $metadata = [
            'url' => $url,
            'provider' => 'unknown',
            'video_id' => null,
            'thumbnail' => null,
        ];

        // YouTube
        if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $url, $matches)) {
            $metadata['provider'] = 'youtube';
            $metadata['video_id'] = $matches[1];
            $metadata['thumbnail'] = "https://img.youtube.com/vi/{$matches[1]}/maxresdefault.jpg";
        }
        // Vimeo
        elseif (preg_match('/vimeo\.com\/(\d+)/', $url, $matches)) {
            $metadata['provider'] = 'vimeo';
            $metadata['video_id'] = $matches[1];
        }

        return $metadata;
    }



    /**
     * Parse boolean value from various input formats.
     */
    private function parseBooleanValue($value): bool
    {
        if (is_bool($value)) {
            return $value;
        }

        if (is_string($value)) {
            return in_array(strtolower($value), ['1', 'true', 'on', 'yes']);
        }

        return (bool) $value;
    }

    /**
     * Ensure chapter belongs to the course.
     */
    private function ensureChapterBelongsToCourse(Chapter $chapter, Course $course): void
    {
        if ($chapter->course_id !== $course->id) {
            abort(404, 'Chapter not found in this course.');
        }
    }
}
