<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Role;
use App\Services\GoogleOAuthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Laravel\Socialite\Facades\Socialite;
use Exception;

class SocialAuthController extends Controller
{
    protected $googleOAuthService;

    public function __construct(GoogleOAuthService $googleOAuthService)
    {
        $this->googleOAuthService = $googleOAuthService;
    }

    /**
     * Redirect to Google OAuth provider
     */
    public function redirectToGoogle()
    {
        try {
            return Socialite::driver('google')->redirect();
        } catch (Exception $e) {
            Log::error('Google OAuth redirect failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()->route('login')->with('error', 'Unable to connect to Google. Please try again or use email/password login.');
        }
    }

    /**
     * Handle Google OAuth callback
     */
    public function handleGoogleCallback(Request $request)
    {
        try {
            // Check for OAuth errors
            if ($request->has('error')) {
                Log::warning('Google OAuth error', [
                    'error' => $request->get('error'),
                    'error_description' => $request->get('error_description')
                ]);
                
                return redirect()->route('login')->with('error', 'Google authentication was cancelled or failed. Please try again.');
            }

            // Get user from Google
            $googleUser = Socialite::driver('google')->user();

            if (!$googleUser || !$googleUser->getEmail()) {
                Log::error('Google OAuth: No user data or email received');
                return redirect()->route('login')->with('error', 'Unable to retrieve your Google account information. Please try again.');
            }

            // Additional security validation
            if (!$this->googleOAuthService->validateGoogleUser($googleUser)) {
                Log::error('Google OAuth: User validation failed', [
                    'google_id' => $googleUser->getId(),
                    'email' => $googleUser->getEmail()
                ]);
                return redirect()->route('login')->with('error', 'Invalid Google account data. Please try again.');
            }

            // Process the Google user through our service
            $user = $this->googleOAuthService->handleGoogleUser($googleUser);
            
            if (!$user) {
                return redirect()->route('login')->with('error', 'Unable to create or link your account. Please contact support.');
            }

            // Log the user in
            Auth::login($user, true);
            
            Log::info('Google OAuth login successful', [
                'user_id' => $user->id,
                'email' => $user->email,
                'google_id' => $user->google_id
            ]);

            // Redirect based on user role
            return $this->redirectAfterLogin($user);

        } catch (Exception $e) {
            Log::error('Google OAuth callback failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            
            return redirect()->route('login')->with('error', 'Authentication failed. Please try again or contact support if the problem persists.');
        }
    }

    /**
     * Redirect user after successful login based on their role
     */
    protected function redirectAfterLogin(User $user)
    {
        // Check for intended URL first
        if (session()->has('url.intended')) {
            $intended = session()->pull('url.intended');
            // Validate the intended URL is safe
            if (filter_var($intended, FILTER_VALIDATE_URL) && str_starts_with($intended, config('app.url'))) {
                return redirect($intended);
            }
        }

        // Role-based redirection
        if ($user->isSuperAdmin() || $user->isAdmin()) {
            return redirect()->route('admin.dashboard');
        }
        
        if ($user->isInstructor()) {
            return redirect()->route('instructor.dashboard');
        }
        
        // Default to student dashboard
        return redirect()->route('dashboard');
    }

    /**
     * Handle linking Google account to existing user
     */
    public function linkGoogleAccount(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        try {
            $googleUser = Socialite::driver('google')->user();
            $user = Auth::user();
            
            // Check if Google account is already linked to another user
            $existingUser = User::where('google_id', $googleUser->getId())
                ->where('id', '!=', $user->id)
                ->first();
                
            if ($existingUser) {
                return redirect()->route('profile.show')->with('error', 'This Google account is already linked to another user.');
            }
            
            // Link the Google account
            $user->update([
                'google_id' => $googleUser->getId()
            ]);
            
            Log::info('Google account linked', [
                'user_id' => $user->id,
                'google_id' => $googleUser->getId()
            ]);
            
            return redirect()->route('profile.show')->with('success', 'Google account linked successfully!');
            
        } catch (Exception $e) {
            Log::error('Google account linking failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);
            
            return redirect()->route('profile.show')->with('error', 'Failed to link Google account. Please try again.');
        }
    }

    /**
     * Unlink Google account from user
     */
    public function unlinkGoogleAccount()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        try {
            $user = Auth::user();
            
            if (!$user->google_id) {
                return redirect()->route('profile.show')->with('info', 'No Google account is currently linked.');
            }
            
            $user->update(['google_id' => null]);
            
            Log::info('Google account unlinked', [
                'user_id' => $user->id
            ]);
            
            return redirect()->route('profile.show')->with('success', 'Google account unlinked successfully!');
            
        } catch (Exception $e) {
            Log::error('Google account unlinking failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);
            
            return redirect()->route('profile.show')->with('error', 'Failed to unlink Google account. Please try again.');
        }
    }
}
