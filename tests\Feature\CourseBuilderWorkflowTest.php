<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\CourseCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CourseBuilderWorkflowTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $instructor;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an instructor user
        $this->instructor = User::factory()->create([
            'role' => 'instructor',
            'email_verified_at' => now(),
        ]);

        // Create a basic category
        CourseCategory::create([
            'name' => 'General',
            'slug' => 'general',
            'description' => 'General courses',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function instructor_can_create_course_and_redirect_to_builder()
    {
        $this->actingAs($this->instructor);

        $courseTitle = 'Test Course Title';

        $response = $this->post(route('instructor.courses.create-and-build'), [
            'title' => $courseTitle,
        ]);

        // Check that course was created
        $this->assertDatabaseHas('courses', [
            'title' => $courseTitle,
            'instructor_id' => $this->instructor->id,
            'status' => 'draft',
        ]);

        // Get the created course
        $course = Course::where('title', $courseTitle)->first();

        // Check redirect to course builder
        $response->assertRedirect(route('instructor.course-builder.show', $course));
        $response->assertSessionHas('success');
    }

    /** @test */
    public function course_creation_requires_title()
    {
        $this->actingAs($this->instructor);

        $response = $this->post(route('instructor.courses.create-and-build'), [
            'title' => '',
        ]);

        $response->assertSessionHasErrors(['title']);
    }

    /** @test */
    public function course_builder_loads_for_new_course()
    {
        $this->actingAs($this->instructor);

        // Create a course
        $course = Course::create([
            'title' => 'Test Course',
            'subtitle' => '',
            'description' => 'Test description',
            'instructor_id' => $this->instructor->id,
            'slug' => 'test-course',
            'status' => 'draft',
            'level' => 'beginner',
            'language' => 'English',
            'price' => 0,
            'category' => 'General',
            'what_you_will_learn' => [],
            'requirements' => [],
            'target_audience' => [],
        ]);

        $response = $this->get(route('instructor.course-builder.show', $course));

        $response->assertStatus(200);
        $response->assertViewIs('instructor.course-builder.show');
        $response->assertViewHas('course', $course);
    }

    /** @test */
    public function instructor_cannot_access_other_instructor_course_builder()
    {
        $otherInstructor = User::factory()->create([
            'role' => 'instructor',
            'email_verified_at' => now(),
        ]);

        $course = Course::create([
            'title' => 'Other Instructor Course',
            'subtitle' => '',
            'description' => 'Test description',
            'instructor_id' => $otherInstructor->id,
            'slug' => 'other-instructor-course',
            'status' => 'draft',
            'level' => 'beginner',
            'language' => 'English',
            'price' => 0,
            'category' => 'General',
            'what_you_will_learn' => [],
            'requirements' => [],
            'target_audience' => [],
        ]);

        $this->actingAs($this->instructor);

        $response = $this->get(route('instructor.course-builder.show', $course));

        $response->assertStatus(403);
    }

    /** @test */
    public function course_wizard_routes_no_longer_exist()
    {
        $this->actingAs($this->instructor);

        // Test that old course wizard routes return 404
        $wizardRoutes = [
            '/instructor/course-wizard/start',
            '/instructor/course-wizard/step1',
            '/instructor/course-wizard/step2',
            '/instructor/course-wizard/step3',
            '/instructor/course-wizard/review',
        ];

        foreach ($wizardRoutes as $route) {
            $response = $this->get($route);
            $response->assertStatus(404);
        }
    }
}
