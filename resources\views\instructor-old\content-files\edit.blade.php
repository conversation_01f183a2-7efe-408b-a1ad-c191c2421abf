@extends('instructor.layouts.app')

@section('title', 'Edit Content File - ' . $contentFile->title)

@section('content')
<div class="py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-4 mb-4">
                <a href="{{ route('instructor.content-files.index') }}" class="text-gray-400 hover:text-white transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <h1 class="text-3xl font-bold text-white">Edit Content File</h1>
            </div>
            <p class="text-gray-400">Update your content file information and settings</p>
        </div>

        <!-- Current File Info -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-6">
            <h3 class="text-lg font-semibold text-white mb-4">Current File</h3>
            <div class="flex items-start space-x-4">
                <div class="w-16 h-16 bg-gray-600 rounded-lg flex items-center justify-center">
                    @if(str_starts_with($contentFile->mime_type, 'image/'))
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                        </svg>
                    @elseif($contentFile->mime_type === 'application/pdf')
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                        </svg>
                    @elseif(str_starts_with($contentFile->mime_type, 'video/'))
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path>
                        </svg>
                    @else
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                        </svg>
                    @endif
                </div>
                <div class="flex-1">
                    <p class="text-white font-medium">{{ $contentFile->file_name }}</p>
                    <p class="text-gray-400 text-sm">{{ $contentFile->formatted_file_size ?? 'Unknown size' }}</p>
                    <p class="text-gray-400 text-sm">{{ $contentFile->mime_type }}</p>
                    <p class="text-gray-400 text-sm">Uploaded {{ $contentFile->created_at->diffForHumans() }}</p>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
            <form method="POST" action="{{ route('instructor.content-files.update', $contentFile) }}" enctype="multipart/form-data" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- Title -->
                <div>
                    <label for="title" class="block text-sm font-medium text-white mb-2">Title *</label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           value="{{ old('title', $contentFile->title) }}"
                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('title') border-red-500 @enderror"
                           placeholder="Enter file title"
                           required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-white mb-2">Description</label>
                    <textarea id="description" 
                              name="description" 
                              rows="4"
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('description') border-red-500 @enderror"
                              placeholder="Enter file description">{{ old('description', $contentFile->description) }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Category and Course Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Category -->
                    <div>
                        <label for="category" class="block text-sm font-medium text-white mb-2">Category *</label>
                        <div class="relative">
                            <select id="category" 
                                    name="category"
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('category') border-red-500 @enderror appearance-none" required>
                                <option value="document" {{ old('category', $contentFile->category) === 'document' ? 'selected' : '' }}>📄 Document</option>
                                <option value="image" {{ old('category', $contentFile->category) === 'image' ? 'selected' : '' }}>🖼️ Image</option>
                                <option value="video" {{ old('category', $contentFile->category) === 'video' ? 'selected' : '' }}>🎥 Video</option>
                                <option value="audio" {{ old('category', $contentFile->category) === 'audio' ? 'selected' : '' }}>🎵 Audio</option>
                                <option value="archive" {{ old('category', $contentFile->category) === 'archive' ? 'selected' : '' }}>📦 Archive</option>
                                <option value="other" {{ old('category', $contentFile->category) === 'other' ? 'selected' : '' }}>📁 Other</option>
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        @error('category')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Course -->
                    <div>
                        <label for="course_id" class="block text-sm font-medium text-white mb-2">Course (Optional)</label>
                        <div class="relative">
                            <select id="course_id" 
                                    name="course_id"
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('course_id') border-red-500 @enderror appearance-none">
                                <option value="">No course (standalone file)</option>
                                @foreach(auth()->user()->courses as $course)
                                    <option value="{{ $course->id }}" {{ old('course_id', $contentFile->course_id) == $course->id ? 'selected' : '' }}>
                                        {{ $course->title }}
                                    </option>
                                @endforeach
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        @error('course_id')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Replace File -->
                <div>
                    <label for="file" class="block text-sm font-medium text-white mb-2">Replace File (Optional)</label>
                    <div class="border-2 border-dashed border-gray-700 rounded-lg p-8 text-center hover:border-gray-600 transition-colors">
                        <input type="file" 
                               id="file" 
                               name="file"
                               class="hidden"
                               onchange="updateFileName(this)">
                        <label for="file" class="cursor-pointer">
                            <svg class="w-12 h-12 text-gray-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <p class="text-white font-medium mb-2">Click to replace file</p>
                            <p class="text-gray-400 text-sm">Maximum file size: 100MB</p>
                        </label>
                        <div id="file-name" class="mt-4 text-sm text-gray-400 hidden"></div>
                    </div>
                    @error('file')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                    <!-- File Preview Container -->
                    <div id="file-preview-container"></div>
                </div>

                <!-- Public/Private Status -->
                <div>
                    <label for="is_public" class="block text-sm font-medium text-white mb-2">Access Level *</label>
                    <div class="relative">
                        <select name="is_public" id="is_public"
                                class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('is_public') border-red-500 @enderror appearance-none" required>
                            <option value="0" {{ old('is_public', $contentFile->is_public) == '0' ? 'selected' : '' }}>
                                🔒 Private - Only accessible to enrolled students
                            </option>
                            <option value="1" {{ old('is_public', $contentFile->is_public) == '1' ? 'selected' : '' }}>
                                🌐 Public - Accessible to everyone
                            </option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    @error('is_public')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                    <p class="text-xs text-gray-500 mt-1">Control who can access this file</p>
                </div>

                <!-- Submit Buttons -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-800">
                    <a href="{{ route('instructor.content-files.index') }}" 
                       class="px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                        Update File
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Enhanced file upload with preview
        enhanceFileUpload('file', 'file-preview-container', {
            maxSize: 100 * 1024 * 1024, // 100MB
            allowedTypes: [], // Allow all file types
            onError: function(error) {
                // Create error message element
                const errorDiv = document.createElement('div');
                errorDiv.className = 'mt-2 p-3 bg-red-900 border border-red-700 rounded-lg text-red-300 text-sm';
                errorDiv.textContent = error;
                
                // Remove any existing error messages
                const existingError = document.querySelector('.file-upload-error');
                if (existingError) existingError.remove();
                
                // Add error class and insert error message
                errorDiv.classList.add('file-upload-error');
                document.getElementById('file-preview-container').appendChild(errorDiv);
                
                // Remove error after 5 seconds
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.remove();
                    }
                }, 5000);
            }
        });
    });

    function updateFileName(input) {
        const fileNameDiv = document.getElementById('file-name');
        if (input.files && input.files[0]) {
            fileNameDiv.textContent = 'Selected: ' + input.files[0].name;
            fileNameDiv.classList.remove('hidden');
        } else {
            fileNameDiv.classList.add('hidden');
        }
    }
</script>
@endpush
@endsection
