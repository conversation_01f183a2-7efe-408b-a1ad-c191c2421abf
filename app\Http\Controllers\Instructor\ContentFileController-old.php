<?php

namespace App\Http\Controllers\Instructor;

use App\Http\Controllers\Controller;
use App\Models\ContentFile;
use App\Models\Course;
use App\Services\PrivateStorageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ContentFileController extends Controller
{
    protected PrivateStorageService $storageService;

    public function __construct(PrivateStorageService $storageService)
    {
        $this->storageService = $storageService;
    }

    /**
     * Display a listing of the content files.
     */
    public function index()
    {
        $instructor = auth()->user();
        $contentFiles = $instructor->contentFiles()
            ->with('course')
            ->orderBy('created_at', 'desc')
            ->paginate(15);
            
        return view('instructor.content-files.index', compact('contentFiles'));
    }

    /**
     * Show the form for creating a new content file.
     */
    public function create(Request $request)
    {
        $instructor = auth()->user();
        $courses = $instructor->courses()->get();

        // Pre-select course if provided in URL
        $selectedCourseId = $request->get('course_id');
        $selectedCourse = null;

        if ($selectedCourseId) {
            $selectedCourse = $courses->firstWhere('id', $selectedCourseId);
        }

        return view('instructor.content-files.create', compact('courses', 'selectedCourse'));
    }

    /**
     * Store a newly created content file in storage.
     */
    public function store(Request $request)
    {
        $instructor = auth()->user();
        
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category' => 'required|in:document,image,video,audio,archive,other',
            'course_id' => 'nullable|exists:courses,id',
            'file' => 'required|file|max:100000', // 100MB max
            'is_public' => 'boolean'
        ]);
        
        // Verify the instructor owns the course if provided
        if ($request->course_id) {
            Course::where('id', $request->course_id)
                ->where('instructor_id', $instructor->id)
                ->firstOrFail();
        }
        
        $data = $request->only([
            'title', 'description', 'category', 'course_id', 'is_public'
        ]);
        
        $data['instructor_id'] = $instructor->id;
        
        // Handle file upload using private storage
        $file = $request->file('file');
        $courseId = $data['course_id'] ?? 'general';

        // Store file in private storage
        $fileData = $this->storageService->storeFile(
            $file,
            (string) $instructor->id,
            (string) $courseId,
            'content-files'
        );

        $data['file_path'] = $fileData['path'];
        $data['file_name'] = $fileData['original_name'];
        $data['file_size'] = $fileData['size'];
        $data['mime_type'] = $fileData['mime_type'];
        
        $contentFile = ContentFile::create($data);
        
        return redirect()->route('instructor.content-files.show', $contentFile)
            ->with('success', 'Content file uploaded successfully.');
    }

    /**
     * Display the specified content file.
     */
    public function show(ContentFile $contentFile)
    {
        // Check if the instructor owns this content file
        if ($contentFile->instructor_id !== auth()->id()) {
            abort(403);
        }
        
        return view('instructor.content-files.show', compact('contentFile'));
    }

    /**
     * Show the form for editing the specified content file.
     */
    public function edit(ContentFile $contentFile)
    {
        // Check if the instructor owns this content file
        if ($contentFile->instructor_id !== auth()->id()) {
            abort(403);
        }
        
        $instructor = auth()->user();
        $courses = $instructor->courses()->get();
        
        return view('instructor.content-files.edit', compact('contentFile', 'courses'));
    }

    /**
     * Update the specified content file in storage.
     */
    public function update(Request $request, ContentFile $contentFile)
    {
        // Check if the instructor owns this content file
        if ($contentFile->instructor_id !== auth()->id()) {
            abort(403);
        }
        
        $instructor = auth()->user();
        
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category' => 'required|in:document,image,video,audio,archive,other',
            'course_id' => 'nullable|exists:courses,id',
            'file' => 'nullable|file|max:100000', // 100MB max
            'is_public' => 'boolean'
        ]);
        
        // Verify the instructor owns the course if provided
        if ($request->course_id) {
            Course::where('id', $request->course_id)
                ->where('instructor_id', $instructor->id)
                ->firstOrFail();
        }
        
        $data = $request->only([
            'title', 'description', 'category', 'course_id', 'is_public'
        ]);
        
        // Handle file upload using private storage
        if ($request->hasFile('file')) {
            // Delete old file
            if ($contentFile->file_path) {
                $this->storageService->deleteFile($contentFile->file_path);
            }

            $file = $request->file('file');
            $courseId = $data['course_id'] ?? $contentFile->course_id ?? 'general';

            // Store file in private storage
            $fileData = $this->storageService->storeFile(
                $file,
                (string) $instructor->id,
                (string) $courseId,
                'content-files'
            );

            $data['file_path'] = $fileData['path'];
            $data['file_name'] = $fileData['original_name'];
            $data['file_size'] = $fileData['size'];
            $data['mime_type'] = $fileData['mime_type'];
        }
        
        $contentFile->update($data);
        
        return redirect()->route('instructor.content-files.show', $contentFile)
            ->with('success', 'Content file updated successfully.');
    }

    /**
     * Remove the specified content file from storage.
     */
    public function destroy(ContentFile $contentFile)
    {
        // Check if the instructor owns this content file
        if ($contentFile->instructor_id !== auth()->id()) {
            abort(403);
        }
        
        // Delete associated file
        if ($contentFile->file_path) {
            $this->storageService->deleteFile($contentFile->file_path);
        }
        
        $contentFile->delete();
        
        return redirect()->route('instructor.content-files.index')
            ->with('success', 'Content file deleted successfully.');
    }

    /**
     * Download the specified content file.
     */
    public function download(ContentFile $contentFile)
    {
        // Check if the instructor owns this content file or if it's public
        if ($contentFile->instructor_id !== auth()->id() && !$contentFile->is_public) {
            abort(403);
        }

        if (!$this->storageService->fileExists($contentFile->file_path)) {
            abort(404, 'File not found.');
        }

        // Increment download count
        $contentFile->incrementDownloadCount();

        // Redirect to secure download route
        return redirect()->route('secure.files.download', ['filePath' => $contentFile->file_path]);
    }
}
