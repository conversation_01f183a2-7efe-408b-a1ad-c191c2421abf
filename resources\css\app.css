@import "tailwindcss";

/* Custom Styles */
body {
  font-family: "Inter", sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #ef4444;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #dc2626;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Form focus styles */
.form-input:focus {
  outline: none;
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Button hover effects */
.btn-primary {
  background-color: #dc2626;
  color: white;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #b91c1c;
}

.btn-secondary {
  background-color: #4b5563;
  color: white;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: #374151;
}

.btn-outline {
  border: 1px solid #4b5563;
  color: #d1d5db;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s;
}

.btn-outline:hover {
  background-color: #1f2937;
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s;
}

.card-hover:hover {
  box-shadow: 0 10px 15px -3px rgba(239, 68, 68, 0.1), 0 4px 6px -2px rgba(239, 68, 68, 0.05);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(to right, #ef4444, #dc2626);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modern dropdown styles */
.dropdown-modern {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, rgba(17, 24, 39, 0.95), rgba(31, 41, 55, 0.95));
}

.dropdown-modern::before {
  content: '';
  position: absolute;
  top: -6px;
  right: 16px;
  width: 12px;
  height: 12px;
  background: linear-gradient(135deg, rgba(17, 24, 39, 0.95), rgba(31, 41, 55, 0.95));
  border: 1px solid rgba(75, 85, 99, 0.5);
  border-bottom: none;
  border-right: none;
  transform: rotate(45deg);
  z-index: -1;
}

/* Enterprise Dropdown Styling */
.enterprise-dropdown {
  backdrop-filter: blur(24px);
  box-shadow: 
    0 32px 64px -12px rgba(0, 0, 0, 0.4),
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, rgba(17, 24, 39, 0.98) 0%, rgba(31, 41, 55, 0.98) 100%);
}

.shadow-enterprise {
  box-shadow: 
    0 32px 64px -12px rgba(0, 0, 0, 0.4),
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.enterprise-dropdown::before {
  content: '';
  position: absolute;
  top: -8px;
  right: 24px;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, rgba(17, 24, 39, 0.98) 0%, rgba(31, 41, 55, 0.98) 100%);
  border: 1px solid rgba(75, 85, 99, 0.3);
  border-bottom: none;
  border-right: none;
  transform: rotate(45deg);
  backdrop-filter: blur(24px);
  box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.1);
}

.enterprise-dropdown-item {
  position: relative;
  overflow: hidden;
}

.enterprise-dropdown-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(220, 38, 38, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: 0.5rem;
}

.enterprise-dropdown-item:hover::before {
  opacity: 1;
}

/* Dropdown menu item hover effects */
.dropdown-item {
  position: relative;
  overflow: hidden;
}

.dropdown-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(239, 68, 68, 0.1), transparent);
  transition: left 0.5s ease;
}

.dropdown-item:hover::before {
  left: 100%;
}

/* User avatar gradient animation */
.user-avatar {
  background: linear-gradient(135deg, #ef4444, #dc2626, #b91c1c);
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Smooth scale transitions */
.scale-95 {
  transform: scale(0.95);
}

.scale-100 {
  transform: scale(1);
}

/* Focus ring for accessibility */
.focus-ring {
  transition: box-shadow 0.15s ease-in-out;
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.3);
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading spinner */
.spinner {
  border: 2px solid #374151;
  border-top: 2px solid #ef4444;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
  display: inline-block;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Form loading state */
.form-loading {
  pointer-events: none;
  opacity: 0.8;
}

.form-loading input,
.form-loading textarea,
.form-loading select {
  background-color: #374151 !important;
  cursor: not-allowed;
}

/* Progress bar */
.progress-bar {
  width: 100%;
  background-color: #374151;
  border-radius: 9999px;
  height: 0.5rem;
}

.progress-fill {
  background-color: #dc2626;
  height: 0.5rem;
  border-radius: 9999px;
  transition: all 0.3s;
}

/* Alert styles */
.alert {
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

.alert-success {
  background-color: #059669;
  color: white;
}

.alert-error {
  background-color: #dc2626;
  color: white;
}

.alert-warning {
  background-color: #d97706;
  color: white;
}

.alert-info {
  background-color: #2563eb;
  color: white;
}

/* Form validation */
.form-error {
  border-color: #ef4444;
  background-color: #fef2f2;
  color: #7f1d1d;
}

.error-message {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Mobile responsive utilities */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }

  .mobile-full {
    width: 100%;
  }
}

/* Dark mode enhancements */
.dark-card {
  background-color: #111827;
  border: 1px solid #1f2937;
}

.dark-input {
  background-color: #1f2937;
  border-color: #374151;
  color: white;
}

.dark-input::placeholder {
  color: #9ca3af;
}

.dark-button {
  background-color: #1f2937;
  color: white;
  transition: background-color 0.2s;
}

.dark-button:hover {
  background-color: #374151;
}

/* Typography */
.heading-primary {
  font-size: 2.25rem;
  font-weight: 700;
  color: white;
}

@media (min-width: 768px) {
  .heading-primary {
    font-size: 3.75rem;
  }
}

.heading-secondary {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

@media (min-width: 768px) {
  .heading-secondary {
    font-size: 1.875rem;
  }
}

.text-muted {
  color: #9ca3af;
}

/* Layout utilities */
.container-custom {
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-custom {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-custom {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.section-padding {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

@media (min-width: 768px) {
  .section-padding {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

/* Interactive elements */
.interactive-hover {
  transition: all 0.2s;
}

.interactive-hover:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.glow-effect {
  box-shadow: 0 10px 15px -3px rgba(239, 68, 68, 0.2), 0 4px 6px -2px rgba(239, 68, 68, 0.1);
}

/* Course card specific styles */
.course-card {
  background-color: #1f2937;
  border: 1px solid #374151;
  border-radius: 0.5rem;
  overflow: hidden;
  transition: all 0.3s;
}

.course-card:hover {
  border-color: #ef4444;
}

.course-image {
  width: 100%;
  height: 12rem;
  object-fit: cover;
  transition: transform 0.3s;
}

.group:hover .course-image {
  transform: scale(1.05);
}

.course-badge {
  background-color: #dc2626;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.course-price {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

/* Navigation enhancements */
.nav-link {
  color: white;
  transition: color 0.2s;
}

.nav-link:hover {
  color: #ef4444;
}

.nav-link.active {
  color: #ef4444;
}

/* Footer styles */
.footer-link {
  color: #9ca3af;
  transition: color 0.2s;
}

.footer-link:hover {
  color: white;
}
