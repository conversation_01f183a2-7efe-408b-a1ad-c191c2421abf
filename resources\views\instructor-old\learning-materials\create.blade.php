@extends('instructor.layouts.app')

@section('title', isset($learningMaterial) ? 'Edit Learning Material - Instructor Dashboard' : 'Create Learning Material - Instructor Dashboard')

@section('content')
<div class="py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-4 mb-4">
                <a href="{{ route('instructor.learning-materials.index') }}" class="text-gray-400 hover:text-white transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <h1 class="text-3xl font-bold text-white">{{ isset($learningMaterial) ? 'Edit' : 'Create' }} Learning Material</h1>
            </div>
            <p class="text-gray-400">{{ isset($learningMaterial) ? 'Update your educational content' : 'Add new educational content for your courses' }}</p>
        </div>

        <!-- Form -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
            <form method="POST" action="{{ isset($learningMaterial) ? route('instructor.learning-materials.update', $learningMaterial) : route('instructor.learning-materials.store') }}" enctype="multipart/form-data" class="space-y-6">
                @csrf
                @if(isset($learningMaterial))
                    @method('PUT')
                @endif

                <!-- Title -->
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-300 mb-2">Title *</label>
                    <input type="text" name="title" id="title" value="{{ old('title', $learningMaterial->title ?? '') }}"
                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('title') border-red-500 @enderror"
                           placeholder="Enter material title" required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                    <textarea name="description" id="description" rows="3"
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('description') border-red-500 @enderror"
                              placeholder="Brief description of the material">{{ old('description', $learningMaterial->description ?? '') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Content -->
                <div>
                    <label for="content" class="block text-sm font-medium text-gray-300 mb-2">Content *</label>
                    <textarea name="content" id="content" rows="10"
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('content') border-red-500 @enderror"
                              placeholder="Enter the main content of your material" required>{{ old('content', $learningMaterial->content ?? '') }}</textarea>
                    @error('content')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Type and Course Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Type -->
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-300 mb-2">Type *</label>
                        <select name="type" id="type"
                                class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('type') border-red-500 @enderror" required>
                            <option value="">Select type</option>
                            <option value="text" {{ old('type', $learningMaterial->type ?? '') === 'text' ? 'selected' : '' }}>Text</option>
                            <option value="video" {{ old('type', $learningMaterial->type ?? '') === 'video' ? 'selected' : '' }}>Video</option>
                            <option value="audio" {{ old('type', $learningMaterial->type ?? '') === 'audio' ? 'selected' : '' }}>Audio</option>
                            <option value="document" {{ old('type', $learningMaterial->type ?? '') === 'document' ? 'selected' : '' }}>Document</option>
                            <option value="presentation" {{ old('type', $learningMaterial->type ?? '') === 'presentation' ? 'selected' : '' }}>Presentation</option>
                        </select>
                        @error('type')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Course -->
                    <div>
                        <label for="course_id" class="block text-sm font-medium text-gray-300 mb-2">Course *</label>
                        <select name="course_id" id="course_id"
                                class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('course_id') border-red-500 @enderror" required>
                            <option value="">Select course</option>
                            @foreach($courses as $course)
                                <option value="{{ $course->id }}" {{ old('course_id', $learningMaterial->course_id ?? ($selectedCourse ? $selectedCourse->id : '')) === $course->id ? 'selected' : '' }}>
                                    {{ $course->title }}
                                </option>
                            @endforeach
                        </select>
                        @error('course_id')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- File Upload -->
                <div>
                    <label for="file" class="block text-sm font-medium text-gray-300 mb-2">File (Optional)</label>
                    <div class="flex items-center justify-center w-full">
                        <label for="file" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-700 border-dashed rounded-lg cursor-pointer bg-gray-800 hover:bg-gray-750 transition-colors">
                            <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                <svg class="w-8 h-8 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <p class="mb-2 text-sm text-gray-400">
                                    <span class="font-semibold">Click to upload</span> or drag and drop
                                </p>
                                <p class="text-xs text-gray-500">PDF, DOC, PPT, MP4, MP3, WAV files. Max size: 50MB</p>
                            </div>
                            <input id="file" name="file" type="file" class="hidden" accept=".pdf,.doc,.docx,.ppt,.pptx,.mp4,.mp3,.wav">
                        </label>
                    </div>
                    @error('file')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                    <!-- File Preview Container -->
                    <div id="file-preview-container"></div>
                </div>

                <!-- Sort Order and Status Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Sort Order -->
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-300 mb-2">Sort Order</label>
                        <input type="number" name="sort_order" id="sort_order" value="{{ old('sort_order', $learningMaterial->sort_order ?? 0) }}" min="0"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('sort_order') border-red-500 @enderror"
                               placeholder="0">
                        @error('sort_order')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Publication Status -->
                    <div>
                        <label for="is_published" class="block text-sm font-medium text-gray-300 mb-2">Publication Status *</label>
                        <div class="relative">
                            <select name="is_published" id="is_published"
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('is_published') border-red-500 @enderror appearance-none" required>
                                <option value="0" {{ old('is_published', $learningMaterial->is_published ?? '0') == '0' ? 'selected' : '' }}>
                                    📝 Draft - Not visible to students
                                </option>
                                <option value="1" {{ old('is_published', $learningMaterial->is_published ?? '0') == '1' ? 'selected' : '' }}>
                                    🌟 Published - Live and available to students
                                </option>
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        @error('is_published')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">Choose the visibility status for this learning material</p>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-800">
                    <a href="{{ route('instructor.learning-materials.index') }}" class="px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                        {{ isset($learningMaterial) ? 'Update Material' : 'Create Material' }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Enhanced file upload with preview
        enhanceFileUpload('file', 'file-preview-container', {
            maxSize: 50 * 1024 * 1024, // 50MB
            allowedTypes: [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'video/mp4',
                'audio/mpeg',
                'audio/wav'
            ],
            onError: function(error) {
                // Create error message element
                const errorDiv = document.createElement('div');
                errorDiv.className = 'mt-2 p-3 bg-red-900 border border-red-700 rounded-lg text-red-300 text-sm';
                errorDiv.textContent = error;

                // Remove any existing error messages
                const existingError = document.querySelector('.file-upload-error');
                if (existingError) existingError.remove();

                // Add error class and insert error message
                errorDiv.classList.add('file-upload-error');
                document.getElementById('file-preview-container').appendChild(errorDiv);

                // Remove error after 5 seconds
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.remove();
                    }
                }, 5000);
            }
        });
    });
</script>
@endpush
@endsection
