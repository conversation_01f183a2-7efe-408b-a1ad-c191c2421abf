<?php

namespace App\Services;

use App\Models\Course;
use App\Models\Enrollment;
use App\Models\LearningMaterial;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class CourseMaterialAccessService
{
    /**
     * Check if a user has access to course materials
     */
    public function checkAccess(User $user, string $courseId, ?string $materialId = null): array
    {
        $course = Course::find($courseId);
        
        if (!$course) {
            return [
                'allowed' => false,
                'reason' => 'course_not_found',
                'message' => 'Course not found.',
            ];
        }

        // Check RBAC permissions first
        $rbacResult = $this->checkRbacAccess($user, $course);
        if ($rbacResult['allowed']) {
            return $rbacResult;
        }

        // Check course publication status
        if (!$this->isCourseAccessible($course)) {
            return [
                'allowed' => false,
                'reason' => 'course_not_published',
                'message' => 'This course is not currently available.',
            ];
        }

        // Check if material is published (if specific material requested)
        if ($materialId) {
            $materialResult = $this->checkMaterialAccess($materialId);
            if (!$materialResult['allowed']) {
                return $materialResult;
            }
        }

        // Check enrollment and payment status
        return $this->checkEnrollmentAndPayment($user, $course);
    }

    /**
     * Check RBAC-based access (admins, superadmins, instructors)
     */
    protected function checkRbacAccess(User $user, Course $course): array
    {
        // Superadmins have full access
        if ($user->isSuperAdmin()) {
            Log::info('Course material access granted: SuperAdmin', [
                'user_id' => $user->id,
                'course_id' => $course->id,
                'reason' => 'superadmin_access'
            ]);
            
            return [
                'allowed' => true,
                'reason' => 'superadmin_access',
                'message' => 'Access granted as SuperAdmin.',
            ];
        }

        // Admins have full access
        if ($user->isAdmin()) {
            Log::info('Course material access granted: Admin', [
                'user_id' => $user->id,
                'course_id' => $course->id,
                'reason' => 'admin_access'
            ]);
            
            return [
                'allowed' => true,
                'reason' => 'admin_access',
                'message' => 'Access granted as Admin.',
            ];
        }

        // Instructors can access their own course materials
        if ($user->isInstructor() && $course->instructor_id === $user->id) {
            Log::info('Course material access granted: Course Instructor', [
                'user_id' => $user->id,
                'course_id' => $course->id,
                'reason' => 'instructor_access'
            ]);
            
            return [
                'allowed' => true,
                'reason' => 'instructor_access',
                'message' => 'Access granted as course instructor.',
            ];
        }

        return [
            'allowed' => false,
            'reason' => 'rbac_check_failed',
            'message' => 'RBAC access check failed.',
        ];
    }

    /**
     * Check if course is accessible based on its status
     */
    protected function isCourseAccessible(Course $course): bool
    {
        return in_array($course->status, ['published', 'archived']);
    }

    /**
     * Check if specific material is accessible
     */
    protected function checkMaterialAccess(string $materialId): array
    {
        $material = LearningMaterial::find($materialId);
        
        if (!$material) {
            return [
                'allowed' => false,
                'reason' => 'material_not_found',
                'message' => 'Learning material not found.',
            ];
        }

        if (!$material->is_published) {
            return [
                'allowed' => false,
                'reason' => 'material_not_published',
                'message' => 'This learning material is not currently available.',
            ];
        }

        return [
            'allowed' => true,
            'reason' => 'material_accessible',
            'message' => 'Material is accessible.',
        ];
    }

    /**
     * Check enrollment and payment status for regular users
     */
    protected function checkEnrollmentAndPayment(User $user, Course $course): array
    {
        // Check if user is enrolled
        $enrollment = $user->enrollments()->where('course_id', $course->id)->first();
        
        if (!$enrollment) {
            return $this->handleNoEnrollment($user, $course);
        }

        // Check enrollment status
        if ($enrollment->status !== 'active') {
            return [
                'allowed' => false,
                'reason' => 'enrollment_inactive',
                'message' => 'Your enrollment in this course is not active.',
            ];
        }

        // For paid courses, verify payment
        if ($course->price > 0) {
            $paymentResult = $this->verifyPayment($user, $course);
            if (!$paymentResult['allowed']) {
                return $paymentResult;
            }
        }

        Log::info('Course material access granted: Valid enrollment', [
            'user_id' => $user->id,
            'course_id' => $course->id,
            'enrollment_id' => $enrollment->id,
            'reason' => 'valid_enrollment'
        ]);

        return [
            'allowed' => true,
            'reason' => 'valid_enrollment',
            'message' => 'Access granted through valid enrollment.',
        ];
    }

    /**
     * Handle cases where user is not enrolled
     */
    protected function handleNoEnrollment(User $user, Course $course): array
    {
        if ($course->price > 0) {
            return [
                'allowed' => false,
                'reason' => 'payment_required',
                'message' => 'You must purchase this course to access its materials.',
                'requires_payment' => true,
            ];
        }

        return [
            'allowed' => false,
            'reason' => 'enrollment_required',
            'message' => 'You must enroll in this course to access its materials.',
        ];
    }

    /**
     * Verify payment for paid courses
     */
    protected function verifyPayment(User $user, Course $course): array
    {
        $payment = Payment::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->where('status', Payment::STATUS_COMPLETED)
            ->where('type', Payment::TYPE_COURSE_PURCHASE)
            ->first();

        if (!$payment) {
            Log::warning('Course material access denied: No valid payment', [
                'user_id' => $user->id,
                'course_id' => $course->id,
                'reason' => 'no_valid_payment'
            ]);

            return [
                'allowed' => false,
                'reason' => 'payment_required',
                'message' => 'Valid payment required to access this course.',
                'requires_payment' => true,
            ];
        }

        return [
            'allowed' => true,
            'reason' => 'valid_payment',
            'message' => 'Access granted through valid payment.',
        ];
    }

    /**
     * Check if user can access a specific file path
     */
    public function canAccessFile(User $user, string $filePath): bool
    {
        // Extract course ID from file path
        if (str_contains($filePath, 'private/')) {
            $pathParts = explode('/', $filePath);
            if (count($pathParts) >= 3 && $pathParts[0] === 'private') {
                $courseId = $pathParts[2];
                $result = $this->checkAccess($user, $courseId);
                return $result['allowed'];
            }
        }

        return false;
    }
}
