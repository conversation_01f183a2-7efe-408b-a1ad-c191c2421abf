@extends('instructor.layouts.app')

@section('title', 'Edit Course - ' . $course->title)

@section('content')
<div class="py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-4 mb-4">
                <a href="{{ route('instructor.courses.show', $course) }}" class="text-gray-400 hover:text-white transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <h1 class="text-3xl font-bold text-white">Edit Course</h1>
            </div>
            <p class="text-gray-400">Update your course information and settings</p>
        </div>

        <!-- Form -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
            <form method="POST" action="{{ route('instructor.courses.update', $course) }}" enctype="multipart/form-data" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- Title -->
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-300 mb-2">Course Title *</label>
                    <input type="text" name="title" id="title" value="{{ old('title', $course->title) }}" 
                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('title') border-red-500 @enderror"
                           placeholder="Enter course title" required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Course Description *</label>
                    <textarea name="description" id="description" rows="6" 
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('description') border-red-500 @enderror"
                              placeholder="Describe what students will learn in this course..." required>{{ old('description', $course->description) }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Category and Level Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Category -->
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-300 mb-2">Category *</label>
                        <select name="category" id="category" 
                                class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('category') border-red-500 @enderror" required>
                            <option value="">Select a category</option>
                            <option value="marketing" {{ old('category', $course->category) === 'marketing' ? 'selected' : '' }}>Marketing</option>
                            <option value="business" {{ old('category', $course->category) === 'business' ? 'selected' : '' }}>Business</option>
                            <option value="finance" {{ old('category', $course->category) === 'finance' ? 'selected' : '' }}>Finance</option>
                            <option value="technology" {{ old('category', $course->category) === 'technology' ? 'selected' : '' }}>Technology</option>
                            <option value="design" {{ old('category', $course->category) === 'design' ? 'selected' : '' }}>Design</option>
                            <option value="personal-development" {{ old('category', $course->category) === 'personal-development' ? 'selected' : '' }}>Personal Development</option>
                            <option value="health-fitness" {{ old('category', $course->category) === 'health-fitness' ? 'selected' : '' }}>Health & Fitness</option>
                            <option value="lifestyle" {{ old('category', $course->category) === 'lifestyle' ? 'selected' : '' }}>Lifestyle</option>
                        </select>
                        @error('category')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Level -->
                    <div>
                        <label for="level" class="block text-sm font-medium text-gray-300 mb-2">Difficulty Level *</label>
                        <select name="level" id="level" 
                                class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('level') border-red-500 @enderror" required>
                            <option value="">Select difficulty level</option>
                            <option value="beginner" {{ old('level', $course->level) === 'beginner' ? 'selected' : '' }}>Beginner</option>
                            <option value="intermediate" {{ old('level', $course->level) === 'intermediate' ? 'selected' : '' }}>Intermediate</option>
                            <option value="advanced" {{ old('level', $course->level) === 'advanced' ? 'selected' : '' }}>Advanced</option>
                        </select>
                        @error('level')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Price and Duration Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Price -->
                    <div>
                        <label for="price" class="block text-sm font-medium text-gray-300 mb-2">Price (USD) *</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-400">$</span>
                            </div>
                            <input type="number" name="price" id="price" value="{{ old('price', $course->price) }}" step="0.01" min="0" max="9999.99"
                                   class="w-full pl-8 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('price') border-red-500 @enderror"
                                   placeholder="0.00" required>
                        </div>
                        @error('price')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Duration -->
                    <div>
                        <label for="duration" class="block text-sm font-medium text-gray-300 mb-2">Duration *</label>
                        <input type="text" name="duration" id="duration" value="{{ old('duration', $course->duration) }}" 
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('duration') border-red-500 @enderror"
                               placeholder="e.g., 8 weeks, 40 hours, Self-paced" required>
                        @error('duration')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Course Image -->
                <div>
                    <label for="image" class="block text-sm font-medium text-gray-300 mb-2">Course Image</label>

                    <div class="flex items-center justify-center w-full">
                        <label for="image" id="image-upload-area" class="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-700 border-dashed rounded-lg cursor-pointer bg-gray-800 hover:bg-gray-750 transition-colors">
                            <div id="upload-placeholder" class="flex flex-col items-center justify-center pt-5 pb-6 {{ $course->image ? 'hidden' : '' }}">
                                <svg class="w-8 h-8 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <p class="mb-2 text-sm text-gray-400">
                                    <span class="font-semibold">Click to upload</span> or drag and drop
                                </p>
                                <p class="text-xs text-gray-500">PNG, JPG, GIF, WEBP up to 5MB (Recommended: 1200x630px)</p>
                            </div>
                            <div id="image-preview" class="w-full h-full relative {{ $course->image ? '' : 'hidden' }}">
                                <img id="preview-img" src="{{ $course->image_url ?: '' }}" alt="Course preview" class="w-full h-full object-cover rounded-lg">
                                <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                                    <p class="text-white text-sm">Click to change image</p>
                                </div>
                            </div>
                            <input id="image" name="image" type="file" class="hidden" accept="image/*">
                        </label>
                    </div>
                    @error('image')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                    <!-- Enhanced Image Preview Container -->
                    <div id="image-preview-container"></div>
                </div>

                <!-- Status and Featured Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-300 mb-2">Publication Status *</label>
                        <div class="relative">
                            <select name="status" id="status"
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('status') border-red-500 @enderror appearance-none" required>
                                <option value="draft" {{ old('status', $course->status) === 'draft' ? 'selected' : '' }}>
                                    📝 Draft - Not visible to students
                                </option>
                                <option value="published" {{ old('status', $course->status) === 'published' ? 'selected' : '' }}>
                                    🌟 Published - Live and available to students
                                </option>
                                <option value="archived" {{ old('status', $course->status) === 'archived' ? 'selected' : '' }}>
                                    📦 Archived - Hidden from public but accessible to enrolled students
                                </option>
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        @error('status')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">Choose how you want this course to be available to students</p>
                    </div>

                    <!-- Featured -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Featured Course</label>
                        <div class="flex items-center pt-3">
                            <label class="flex items-center">
                                <input type="checkbox" name="featured" value="1" {{ old('featured', $course->featured) ? 'checked' : '' }} class="sr-only">
                                <div class="w-4 h-4 border-2 border-gray-600 rounded mr-2 flex items-center justify-center">
                                    <svg class="w-3 h-3 text-red-500 hidden" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <span class="text-gray-300">Mark as featured course</span>
                            </label>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Featured courses appear on the homepage and get more visibility</p>
                    </div>
                </div>

                <!-- Danger Zone -->
                @if($course->enrollments->count() === 0)
                <div class="border border-red-800 rounded-lg p-6 bg-red-900/20">
                    <h3 class="text-lg font-bold text-red-400 mb-2">Danger Zone</h3>
                    <p class="text-gray-300 mb-4">This course has no enrollments. You can delete it permanently if needed.</p>
                    <button type="button" onclick="confirmDelete()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors">
                        Delete Course
                    </button>
                </div>
                @endif

                <!-- Submit Buttons -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-800">
                    <a href="{{ route('instructor.courses.show', $course) }}" class="px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                        Update Course
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
@if($course->enrollments->count() === 0)
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md mx-4">
        <h3 class="text-lg font-bold text-white mb-4">Confirm Deletion</h3>
        <p class="text-gray-300 mb-6">Are you sure you want to delete this course? This action cannot be undone.</p>
        <div class="flex space-x-4">
            <button onclick="closeDeleteModal()" class="flex-1 px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors">
                Cancel
            </button>
            <form method="POST" action="{{ route('instructor.courses.destroy', $course) }}" class="flex-1">
                @csrf
                @method('DELETE')
                <button type="submit" class="w-full px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                    Delete
                </button>
            </form>
        </div>
    </div>
</div>
@endif

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Enhanced image upload with preview
        enhanceFileUpload('image', 'image-preview-container', {
            maxSize: 5 * 1024 * 1024, // 5MB
            allowedTypes: [
                'image/jpeg',
                'image/png',
                'image/gif',
                'image/webp'
            ],
            onFileSelect: function(file) {
                // Also update the existing preview system
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('upload-placeholder').classList.add('hidden');
                    document.getElementById('image-preview').classList.remove('hidden');
                    document.getElementById('preview-img').src = e.target.result;
                };
                reader.readAsDataURL(file);
            },
            onError: function(error) {
                // Create error message element
                const errorDiv = document.createElement('div');
                errorDiv.className = 'mt-2 p-3 bg-red-900 border border-red-700 rounded-lg text-red-300 text-sm';
                errorDiv.textContent = error;

                // Remove any existing error messages
                const existingError = document.querySelector('.image-upload-error');
                if (existingError) existingError.remove();

                // Add error class and insert error message
                errorDiv.classList.add('image-upload-error');
                document.getElementById('image-preview-container').appendChild(errorDiv);

                // Remove error after 5 seconds
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.remove();
                    }
                }, 5000);
            }
        });

        // Initialize common form components
        initInstructorFormComponents();
    });

    // Delete modal functions
    function confirmDelete() {
        document.getElementById('deleteModal').classList.remove('hidden');
        document.getElementById('deleteModal').classList.add('flex');
    }

    function closeDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden');
        document.getElementById('deleteModal').classList.remove('flex');
    }
</script>
@endpush
@endsection
