<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\PayPalService;
use App\Services\PayPalDebugService;
use Illuminate\Support\Facades\Log;

class TestPayPalCapture extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'paypal:test-capture {order_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test PayPal capture functionality with the fixed implementation';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 Testing PayPal Capture Fix');
        $this->info('============================');

        // Check PayPal configuration
        $config = config('services.paypal');
        if (!$config || !$config['client_id'] || !$config['client_secret']) {
            $this->error('❌ PayPal configuration is missing. Please check config/services.php');
            return 1;
        }

        $this->info('✅ PayPal configuration found');
        $this->info("   Mode: {$config['mode']}");
        $this->info("   Currency: {$config['currency']}");

        $orderId = $this->argument('order_id');

        if (!$orderId) {
            $this->info('📝 No order ID provided. Creating a test order first...');
            $orderId = $this->createTestOrder();
            
            if (!$orderId) {
                $this->error('❌ Failed to create test order');
                return 1;
            }
        }

        $this->info("🎯 Testing capture for order: {$orderId}");

        // Test the fixed implementation
        $this->testFixedImplementation($orderId);

        // Test different approaches for comparison
        $this->testDifferentApproaches($orderId);

        return 0;
    }

    /**
     * Create a test order for testing capture
     */
    private function createTestOrder(): ?string
    {
        try {
            $debugService = new PayPalDebugService();
            $result = $debugService->createTestOrder();

            if ($result['success']) {
                $this->info("✅ Test order created: {$result['order_id']}");
                $this->info("   Status: {$result['status']}");
                return $result['order_id'];
            } else {
                $this->error("❌ Failed to create test order: {$result['error']}");
                return null;
            }
        } catch (\Exception $e) {
            $this->error("❌ Exception creating test order: {$e->getMessage()}");
            return null;
        }
    }

    /**
     * Test the fixed PayPal service implementation
     */
    private function testFixedImplementation(string $orderId): void
    {
        $this->info('');
        $this->info('🔧 Testing Fixed Implementation');
        $this->info('------------------------------');

        try {
            $paypalService = new PayPalService();
            $result = $paypalService->captureOrder($orderId);

            if ($result['success']) {
                $this->info('✅ Capture succeeded with fixed implementation!');
                $this->info("   Payment ID: {$result['payment']->id}");
                $this->info("   Status: {$result['payment']->status}");
            } else {
                $this->warn("⚠️  Capture failed (expected for test orders): {$result['error']}");
                
                // Check if it's the expected "Payment not found" error vs MALFORMED_REQUEST_JSON
                if (strpos($result['error'], 'MALFORMED_REQUEST_JSON') !== false) {
                    $this->error('❌ MALFORMED_REQUEST_JSON error still occurring!');
                } elseif (strpos($result['error'], 'Payment not found') !== false) {
                    $this->info('✅ HTTP request format is correct (payment record issue is expected)');
                } else {
                    $this->info("ℹ️  Other error: {$result['error']}");
                }
            }
        } catch (\Exception $e) {
            $this->error("❌ Exception in fixed implementation: {$e->getMessage()}");
        }
    }

    /**
     * Test different approaches for comparison
     */
    private function testDifferentApproaches(string $orderId): void
    {
        $this->info('');
        $this->info('🧪 Testing Different Approaches');
        $this->info('-------------------------------');

        try {
            $debugService = new PayPalDebugService();
            $results = $debugService->debugCaptureRequest($orderId);

            foreach ($results as $testName => $result) {
                if ($testName === 'access_token') {
                    continue;
                }

                $status = $result['successful'] ? '✅' : '❌';
                $this->info("{$status} {$result['test_name']}");

                if (!$result['successful'] && isset($result['error_name'])) {
                    $this->info("   Error: {$result['error_name']} - {$result['error_message']}");
                }
            }
        } catch (\Exception $e) {
            $this->error("❌ Exception in debug tests: {$e->getMessage()}");
        }
    }
}
