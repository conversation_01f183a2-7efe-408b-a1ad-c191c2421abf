<?php

namespace App\Http\Controllers\Instructor;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PaymentController extends Controller
{
    /**
     * Display a listing of payments for instructor's courses.
     */
    public function index(Request $request)
    {
        $instructor = Auth::user();
        
        $payments = Payment::whereHas('course', function ($query) use ($instructor) {
            $query->where('instructor_id', $instructor->id);
        })
        ->with(['user', 'course'])
        ->when($request->search, function ($query, $search) {
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        })
        ->when($request->course_id, function ($query, $courseId) {
            $query->where('course_id', $courseId);
        })
        ->when($request->status, function ($query, $status) {
            $query->where('status', $status);
        })
        ->orderBy('created_at', 'desc')
        ->paginate(20);

        // Get instructor's courses for filter dropdown
        $courses = $instructor->courses()->select('id', 'title')->get();

        // Calculate summary statistics
        $totalRevenue = Payment::whereHas('course', function ($query) use ($instructor) {
            $query->where('instructor_id', $instructor->id);
        })->where('status', 'completed')->sum('amount');

        $monthlyRevenue = Payment::whereHas('course', function ($query) use ($instructor) {
            $query->where('instructor_id', $instructor->id);
        })
        ->where('status', 'completed')
        ->whereMonth('created_at', now()->month)
        ->whereYear('created_at', now()->year)
        ->sum('amount');

        return view('instructor.payments.index', compact('payments', 'courses', 'totalRevenue', 'monthlyRevenue'));
    }

    /**
     * Display the specified payment.
     */
    public function show(Payment $payment)
    {
        $instructor = Auth::user();
        
        // Check if payment is for instructor's course
        if ($payment->course->instructor_id !== $instructor->id) {
            abort(403, 'You can only view payments for your own courses.');
        }

        $payment->load(['user', 'course']);

        return view('instructor.payments.show', compact('payment'));
    }

    /**
     * Export payments data.
     */
    public function export(Request $request)
    {
        $instructor = Auth::user();
        
        $payments = Payment::whereHas('course', function ($query) use ($instructor) {
            $query->where('instructor_id', $instructor->id);
        })
        ->with(['user', 'course'])
        ->when($request->course_id, function ($query, $courseId) {
            $query->where('course_id', $courseId);
        })
        ->when($request->status, function ($query, $status) {
            $query->where('status', $status);
        })
        ->get();

        $csvData = [];
        $csvData[] = ['Date', 'Student', 'Course', 'Amount', 'Status', 'Payment Method', 'Transaction ID'];

        foreach ($payments as $payment) {
            $csvData[] = [
                $payment->created_at->format('Y-m-d'),
                $payment->user->name,
                $payment->course->title,
                '$' . number_format($payment->amount, 2),
                ucfirst($payment->status),
                $payment->payment_method,
                $payment->transaction_id
            ];
        }

        $filename = 'payments_' . date('Y-m-d') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
