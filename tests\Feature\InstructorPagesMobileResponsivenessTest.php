<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\CourseCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class InstructorPagesMobileResponsivenessTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $instructor;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an instructor user
        $this->instructor = User::factory()->create([
            'role' => 'instructor',
            'email_verified_at' => now(),
        ]);

        // Create a basic category
        CourseCategory::create([
            'name' => 'General',
            'slug' => 'general',
            'description' => 'General courses',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function instructor_dashboard_header_is_mobile_responsive()
    {
        $this->actingAs($this->instructor);

        $response = $this->get(route('instructor.dashboard'));

        $response->assertStatus(200);

        // Check for mobile-responsive header layout
        $response->assertSee('flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0', false);
        
        // Check for responsive text sizing
        $response->assertSee('text-2xl md:text-3xl lg:text-4xl', false);
        $response->assertSee('text-base md:text-lg', false);
        
        // Check for responsive button layout
        $response->assertSee('flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3', false);
        
        // Check for mobile-specific button text
        $response->assertSee('hidden sm:inline', false);
        $response->assertSee('sm:hidden', false);
        
        // Check for responsive button sizing
        $response->assertSee('px-4 py-3 md:px-6 md:py-3', false);
        $response->assertSee('text-sm md:text-base', false);
    }

    /** @test */
    public function instructor_courses_index_header_is_mobile_responsive()
    {
        $this->actingAs($this->instructor);

        $response = $this->get(route('instructor.courses.index'));

        $response->assertStatus(200);

        // Check for mobile-responsive header layout
        $response->assertSee('flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0', false);
        
        // Check for responsive text sizing
        $response->assertSee('text-2xl md:text-3xl lg:text-4xl', false);
        $response->assertSee('text-base md:text-lg', false);
        
        // Check for responsive button layout
        $response->assertSee('flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3', false);
        
        // Check for mobile-specific button text
        $response->assertSee('hidden sm:inline', false);
        $response->assertSee('sm:hidden', false);
        
        // Check for responsive button sizing
        $response->assertSee('px-4 py-3 md:px-6 md:py-3', false);
        $response->assertSee('text-sm md:text-base', false);
    }

    /** @test */
    public function instructor_course_builder_header_is_mobile_responsive()
    {
        $this->actingAs($this->instructor);

        // Create a test course
        $course = Course::create([
            'title' => 'Test Mobile Course',
            'subtitle' => 'Testing mobile responsiveness',
            'description' => 'Test description for mobile responsiveness',
            'instructor_id' => $this->instructor->id,
            'slug' => 'test-mobile-course',
            'status' => 'draft',
            'level' => 'beginner',
            'language' => 'English',
            'price' => 0,
            'category' => 'General',
            'what_you_will_learn' => [],
            'requirements' => [],
            'target_audience' => [],
        ]);

        $response = $this->get(route('instructor.course-builder.show', $course));

        $response->assertStatus(200);

        // Check for mobile-responsive header layout
        $response->assertSee('flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0', false);
        
        // Check for mobile sidebar toggle
        $response->assertSee('id="mobile-sidebar-toggle"', false);
        $response->assertSee('md:hidden', false);
        
        // Check for responsive text sizing
        $response->assertSee('text-lg md:text-xl', false);
        $response->assertSee('text-sm md:text-base', false);
        
        // Check for mobile-specific button text
        $response->assertSee('hidden sm:inline', false);
        $response->assertSee('sm:hidden', false);
    }

    /** @test */
    public function mobile_responsive_classes_are_consistently_applied()
    {
        $this->actingAs($this->instructor);

        $pages = [
            route('instructor.dashboard'),
            route('instructor.courses.index'),
        ];

        foreach ($pages as $page) {
            $response = $this->get($page);
            $response->assertStatus(200);

            // Check for consistent responsive patterns
            $response->assertSee('flex flex-col space-y-4', false);
            $response->assertSee('lg:flex-row lg:items-center lg:justify-between', false);
            $response->assertSee('sm:flex-row sm:space-y-0 sm:space-x-3', false);
            $response->assertSee('justify-center', false);
        }
    }

    /** @test */
    public function buttons_have_proper_mobile_sizing()
    {
        $this->actingAs($this->instructor);

        $response = $this->get(route('instructor.dashboard'));

        $response->assertStatus(200);

        // Check for touch-friendly button sizing
        $response->assertSee('px-4 py-3', false);
        $response->assertSee('md:px-6 md:py-3', false);
        
        // Check for responsive icon sizing
        $response->assertSee('w-4 h-4 md:w-5 md:h-5', false);
        
        // Check for responsive text sizing
        $response->assertSee('text-sm md:text-base', false);
    }
}
