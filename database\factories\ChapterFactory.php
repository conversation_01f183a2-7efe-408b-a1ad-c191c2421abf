<?php

namespace Database\Factories;

use App\Models\Chapter;
use App\Models\Course;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Chapter>
 */
class ChapterFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Chapter::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(3),
            'slug' => $this->faker->slug(),
            'description' => $this->faker->paragraph(),
            'learning_objectives' => $this->faker->paragraph(),
            'course_id' => Course::factory(),
            'instructor_id' => User::factory(),
            'sort_order' => $this->faker->numberBetween(1, 10),
            'is_published' => $this->faker->boolean(80), // 80% chance of being published
            'is_free_preview' => $this->faker->boolean(20), // 20% chance of being free preview
            'total_duration_minutes' => $this->faker->numberBetween(30, 180),
            'total_lectures' => $this->faker->numberBetween(3, 15),
        ];
    }

    /**
     * Indicate that the chapter is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_published' => true,
        ]);
    }

    /**
     * Indicate that the chapter is a draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_published' => false,
        ]);
    }

    /**
     * Indicate that the chapter is a free preview.
     */
    public function freePreview(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_free_preview' => true,
        ]);
    }
}
