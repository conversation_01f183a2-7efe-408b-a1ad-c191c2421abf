<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Enrollment extends Model
{
    use HasFactory;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'user_id',
        'course_id',
        'instructor_id',
        'enrolled_at',
        'completed_at',
        'last_accessed_at',
        'status',
        'progress_percentage',
        'completed_lectures',
        'total_lectures',
        'total_watch_time_minutes',
        'current_lecture_id',
        'completed_lecture_ids',
        'quiz_scores',
        'final_score',
        'certificate_issued',
        'certificate_issued_at',
        'certificate_url',
    ];

    protected $casts = [
        'enrolled_at' => 'datetime',
        'completed_at' => 'datetime',
        'last_accessed_at' => 'datetime',
        'progress_percentage' => 'integer',
        'completed_lectures' => 'integer',
        'total_lectures' => 'integer',
        'total_watch_time_minutes' => 'integer',
        'completed_lecture_ids' => 'array',
        'quiz_scores' => 'array',
        'final_score' => 'decimal:2',
        'certificate_issued' => 'boolean',
        'certificate_issued_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
            if (empty($model->enrolled_at)) {
                $model->enrolled_at = now();
            }
            // Set total lectures from course at enrollment time
            if (empty($model->total_lectures)) {
                $course = Course::find($model->course_id);
                $model->total_lectures = $course ? $course->total_lectures : 0;
            }
        });

        static::saved(function ($model) {
            // Update course statistics when enrollment is saved
            if ($model->course) {
                $model->course->updateStatistics();
            }
        });

        static::deleted(function ($model) {
            // Update course statistics when enrollment is deleted
            if ($model->course) {
                $model->course->updateStatistics();
            }
        });
    }

    /**
     * Get the user that owns the enrollment.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the course for the enrollment.
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get the instructor for the enrollment.
     */
    public function instructor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'instructor_id');
    }

    /**
     * Get the current lecture for the enrollment.
     */
    public function currentLecture(): BelongsTo
    {
        return $this->belongsTo(Lecture::class, 'current_lecture_id');
    }

    /**
     * Get the lecture progress records for this enrollment.
     */
    public function lectureProgress(): HasMany
    {
        return $this->hasMany(LectureProgress::class, 'user_id', 'user_id')
            ->where('course_id', $this->course_id);
    }

    /**
     * Scope to filter active enrollments.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to filter completed enrollments.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by course.
     */
    public function scopeByCourse($query, $courseId)
    {
        return $query->where('course_id', $courseId);
    }

    /**
     * Scope to filter by instructor.
     */
    public function scopeByInstructor($query, $instructorId)
    {
        return $query->where('instructor_id', $instructorId);
    }

    /**
     * Check if the enrollment is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if the enrollment is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if a specific lecture is completed.
     */
    public function isLectureCompleted(string $lectureId): bool
    {
        return in_array($lectureId, $this->completed_lecture_ids ?? []);
    }

    /**
     * Mark a lecture as completed.
     */
    public function markLectureCompleted(string $lectureId): void
    {
        $completedIds = $this->completed_lecture_ids ?? [];
        if (!in_array($lectureId, $completedIds)) {
            $completedIds[] = $lectureId;
            $this->update([
                'completed_lecture_ids' => $completedIds,
                'completed_lectures' => count($completedIds),
                'progress_percentage' => $this->calculateProgressPercentage(),
                'last_accessed_at' => now(),
            ]);
        }
    }

    /**
     * Update current lecture.
     */
    public function updateCurrentLecture(string $lectureId): void
    {
        $this->update([
            'current_lecture_id' => $lectureId,
            'last_accessed_at' => now(),
        ]);
    }

    /**
     * Calculate progress percentage.
     */
    public function calculateProgressPercentage(): int
    {
        if ($this->total_lectures === 0) {
            return 0;
        }
        return min(100, round(($this->completed_lectures / $this->total_lectures) * 100));
    }

    /**
     * Update progress percentage.
     */
    public function updateProgress(): void
    {
        $this->update([
            'progress_percentage' => $this->calculateProgressPercentage(),
        ]);
    }

    /**
     * Mark enrollment as completed.
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'progress_percentage' => 100,
        ]);
    }

    /**
     * Get formatted watch time.
     */
    public function getFormattedWatchTime(): string
    {
        if ($this->total_watch_time_minutes < 60) {
            return $this->total_watch_time_minutes . ' minutes';
        }
        
        $hours = floor($this->total_watch_time_minutes / 60);
        $minutes = $this->total_watch_time_minutes % 60;
        
        if ($minutes === 0) {
            return $hours . ' hour' . ($hours > 1 ? 's' : '');
        }
        
        return $hours . 'h ' . $minutes . 'm';
    }

    /**
     * Issue certificate for completed course.
     */
    public function issueCertificate(): void
    {
        if ($this->isCompleted() && !$this->certificate_issued) {
            $this->update([
                'certificate_issued' => true,
                'certificate_issued_at' => now(),
                'certificate_url' => $this->generateCertificateUrl(),
            ]);
        }
    }

    /**
     * Generate certificate URL.
     */
    private function generateCertificateUrl(): string
    {
        return route('certificates.show', [
            'enrollment' => $this->id,
            'token' => hash('sha256', $this->id . $this->user_id . $this->course_id)
        ]);
    }
}
