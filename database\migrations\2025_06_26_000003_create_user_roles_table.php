<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_roles', function (Blueprint $table) {
            $table->uuid('id')->primary()->default(\Illuminate\Support\Facades\DB::raw('(UUID())'));
            $table->uuid('user_id');
            $table->uuid('role_id');
            $table->timestamp('assigned_at')->useCurrent();
            $table->uuid('assigned_by')->nullable(); // Who assigned this role
            $table->text('notes')->nullable(); // Optional notes about the assignment
            $table->boolean('is_active')->default(true);
            $table->timestamp('expires_at')->nullable(); // Optional expiration
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('role_id')->references('id')->on('roles')->onDelete('cascade');
            $table->foreign('assigned_by')->references('id')->on('users')->onDelete('set null');

            // Indexes for performance
            $table->index(['user_id', 'is_active']);
            $table->index(['role_id', 'is_active']);
            $table->index('assigned_at');
            $table->index('expires_at');
            
            // Unique constraint to prevent duplicate active role assignments
            $table->unique(['user_id', 'role_id', 'is_active'], 'unique_active_user_role');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_roles');
    }
};
