@extends('layouts.app')

@section('title', $chapter->title . ' - ' . $course->title)

@section('content')
<div class="min-h-screen bg-black">
    <!-- Header -->
    <div class="bg-gradient-to-br from-black via-gray-900 to-black border-b border-gray-800">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-4">
                        <a href="{{ route('instructor.courses.chapters.index', $course) }}" 
                           class="text-gray-400 hover:text-red-500 transition-colors">
                            <i class="fas fa-arrow-left"></i>
                        </a>
                        <div>
                            <h1 class="text-3xl font-bold text-white">Chapter <span class="text-red-500">Management</span></h1>
                            <p class="text-gray-400 mt-1">{{ $course->title }}</p>
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{{ route('instructor.courses.chapters.edit', [$course, $chapter]) }}" 
                           class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-edit mr-2"></i>Edit Chapter
                        </a>
                        <button type="button" onclick="toggleChapterStatus('{{ $chapter->id }}')"
                                class="bg-{{ $chapter->is_published ? 'yellow' : 'green' }}-600 hover:bg-{{ $chapter->is_published ? 'yellow' : 'green' }}-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-{{ $chapter->is_published ? 'eye-slash' : 'eye' }} mr-2"></i>
                            {{ $chapter->is_published ? 'Unpublish' : 'Publish' }}
                        </button>
                    </div>
                </div>

                <!-- Breadcrumb -->
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <div class="flex items-center">
                                <a href="{{ route('instructor.courses.show', $course) }}" class="text-sm font-medium text-gray-400 hover:text-red-500">
                                    Course Details
                                </a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-gray-600 mr-4"></i>
                                <a href="{{ route('instructor.courses.chapters.index', $course) }}" class="text-sm font-medium text-gray-400 hover:text-red-500">
                                    Chapters
                                </a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-gray-600 mr-4"></i>
                                <span class="text-sm font-medium text-red-500">{{ $chapter->title }}</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Chapter Overview -->
        <div class="bg-gray-800 border border-gray-700 rounded-lg shadow p-6 mb-6">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-12 h-12 bg-red-600 text-white rounded-full flex items-center justify-center text-lg font-bold">
                            {{ $chapter->sort_order }}
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-white">{{ $chapter->title }}</h2>
                            <div class="flex items-center space-x-4 mt-1">
                                <span class="px-3 py-1 text-sm rounded-full {{ $chapter->is_published ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                    {{ $chapter->is_published ? 'Published' : 'Draft' }}
                                </span>
                                @if($chapter->is_free_preview)
                                    <span class="px-3 py-1 text-sm rounded-full bg-blue-100 text-blue-800">Free Preview</span>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    @if($chapter->description)
                        <p class="text-gray-300 mb-4">{{ $chapter->description }}</p>
                    @endif

                    @if($chapter->learning_objectives && count($chapter->learning_objectives) > 0)
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-400 mb-2">Learning Objectives:</h4>
                            <ul class="list-disc list-inside text-sm text-gray-300 space-y-1">
                                @foreach($chapter->learning_objectives as $objective)
                                    <li>{{ $objective }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                </div>

                <!-- Chapter Statistics -->
                <div class="ml-6 bg-gray-900 rounded-lg p-4 min-w-[200px]">
                    <h4 class="text-sm font-medium text-gray-400 mb-3">Chapter Statistics</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Total Lectures:</span>
                            <span class="text-white font-medium">{{ $chapter->lectures->count() }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Published:</span>
                            <span class="text-white font-medium">{{ $chapter->lectures->where('is_published', true)->count() }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Duration:</span>
                            <span class="text-white font-medium">{{ $chapter->getFormattedDuration() }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Free Previews:</span>
                            <span class="text-white font-medium">{{ $chapter->lectures->where('is_free_preview', true)->count() }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-gray-800 border border-gray-700 rounded-lg shadow p-6 mb-6">
            <h3 class="text-lg font-medium text-white mb-4">Quick Actions</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="{{ route('instructor.courses.chapters.lectures.create', [$course, $chapter]) }}" 
                   class="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg text-center transition-colors">
                    <i class="fas fa-plus text-2xl mb-2"></i>
                    <div class="font-medium">Add Lecture</div>
                    <div class="text-sm opacity-90">Create new content</div>
                </a>
                <button type="button" onclick="reorderLectures()" 
                        class="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg text-center transition-colors">
                    <i class="fas fa-sort text-2xl mb-2"></i>
                    <div class="font-medium">Reorder Lectures</div>
                    <div class="text-sm opacity-90">Drag & drop to reorder</div>
                </button>
                <button type="button" onclick="bulkActions()" 
                        class="bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg text-center transition-colors">
                    <i class="fas fa-tasks text-2xl mb-2"></i>
                    <div class="font-medium">Bulk Actions</div>
                    <div class="text-sm opacity-90">Publish/unpublish multiple</div>
                </button>
            </div>

            <div class="p-6">
                @if($chapter->lectures->count() > 0)
                    <div id="lectures-list" class="space-y-4">
                        @foreach($chapter->lectures as $lecture)
                            <div class="lecture-item bg-gray-900 border border-gray-600 rounded-lg p-4 hover:border-gray-500 transition-colors"
                                 data-lecture-id="{{ $lecture->id }}">
                                <div class="flex items-center space-x-4">
                                    <!-- Bulk Select -->
                                    <input type="checkbox" class="bulk-select rounded border-gray-600 text-red-600 focus:ring-red-500 focus:ring-offset-gray-800"
                                           value="{{ $lecture->id }}">

                                    <!-- Drag Handle -->
                                    <div class="drag-handle cursor-move text-gray-400 hover:text-gray-300">
                                        <i class="fas fa-grip-vertical"></i>
                                    </div>

                                    <!-- Lecture Number -->
                                    <div class="w-8 h-8 bg-gray-700 text-gray-300 rounded-full flex items-center justify-center text-sm font-medium">
                                        {{ $lecture->sort_order }}
                                    </div>

                                    <!-- Lecture Info -->
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3 mb-2">
                                            <h4 class="font-semibold text-white">{{ $lecture->title }}</h4>
                                            <span class="px-2 py-1 text-xs rounded-full
                                                @if($lecture->type === 'video') bg-red-100 text-red-800
                                                @elseif($lecture->type === 'text') bg-blue-100 text-blue-800
                                                @elseif($lecture->type === 'quiz') bg-green-100 text-green-800
                                                @elseif($lecture->type === 'assignment') bg-purple-100 text-purple-800
                                                @else bg-gray-100 text-gray-800 @endif">
                                                {{ ucfirst($lecture->type) }}
                                            </span>
                                        </div>

                                        @if($lecture->description)
                                            <p class="text-sm text-gray-400 mb-2">{{ Str::limit($lecture->description, 100) }}</p>
                                        @endif

                                        <div class="flex items-center space-x-4 text-xs text-gray-500">
                                            @if($lecture->duration_minutes)
                                                <span><i class="fas fa-clock mr-1"></i>{{ $lecture->duration_minutes }} min</span>
                                            @endif
                                            <span class="px-2 py-1 rounded-full {{ $lecture->is_published ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                                {{ $lecture->is_published ? 'Published' : 'Draft' }}
                                            </span>
                                            @if($lecture->is_free_preview)
                                                <span class="px-2 py-1 rounded-full bg-blue-100 text-blue-800">Free Preview</span>
                                            @endif
                                            @if($lecture->is_mandatory)
                                                <span class="px-2 py-1 rounded-full bg-orange-100 text-orange-800">Mandatory</span>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Actions -->
                                    <div class="flex items-center space-x-2">
                                        <a href="{{ route('instructor.courses.chapters.lectures.show', [$course, $chapter, $lecture]) }}"
                                           class="text-blue-400 hover:text-blue-300 text-sm font-medium">
                                            <i class="fas fa-eye mr-1"></i>View
                                        </a>
                                        <a href="{{ route('instructor.courses.chapters.lectures.edit', [$course, $chapter, $lecture]) }}"
                                           class="text-gray-400 hover:text-gray-300 text-sm font-medium">
                                            <i class="fas fa-edit mr-1"></i>Edit
                                        </a>

                                        <!-- Dropdown Menu -->
                                        <div class="relative">
                                            <button type="button"
                                                    class="text-gray-400 hover:text-gray-300 dropdown-toggle"
                                                    data-lecture-id="{{ $lecture->id }}">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <div class="dropdown-menu absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden">
                                                <div class="py-1">
                                                    <button type="button"
                                                            onclick="toggleLectureStatus('{{ $lecture->id }}')"
                                                            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                        <i class="fas fa-{{ $lecture->is_published ? 'eye-slash' : 'eye' }} mr-2"></i>
                                                        {{ $lecture->is_published ? 'Unpublish' : 'Publish' }}
                                                    </button>
                                                    <button type="button"
                                                            onclick="duplicateLecture('{{ $lecture->id }}')"
                                                            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                        <i class="fas fa-copy mr-2"></i>Duplicate
                                                    </button>
                                                    <button type="button"
                                                            onclick="moveLecture('{{ $lecture->id }}', 'up')"
                                                            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                        <i class="fas fa-arrow-up mr-2"></i>Move Up
                                                    </button>
                                                    <button type="button"
                                                            onclick="moveLecture('{{ $lecture->id }}', 'down')"
                                                            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                        <i class="fas fa-arrow-down mr-2"></i>Move Down
                                                    </button>
                                                    <div class="border-t border-gray-100"></div>
                                                    <button type="button"
                                                            onclick="deleteLecture('{{ $lecture->id }}')"
                                                            class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                                        <i class="fas fa-trash mr-2"></i>Delete
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <!-- Empty State -->
                    <div class="text-center py-12">
                        <i class="fas fa-video text-6xl text-gray-600 mb-6"></i>
                        <h3 class="text-xl font-medium text-gray-300 mb-4">No lectures yet</h3>
                        <p class="text-gray-500 mb-6">Start building your chapter by adding your first lecture.</p>
                        <a href="{{ route('instructor.courses.chapters.lectures.create', [$course, $chapter]) }}"
                           class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-plus mr-2"></i>Add First Lecture
                        </a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Bulk Actions Panel -->
        <div id="bulk-actions-panel" class="fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-gray-800 border border-gray-700 rounded-lg shadow-lg p-4 hidden">
            <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-300">
                    <span id="selected-count">0</span> lectures selected
                </span>
                <button type="button" onclick="bulkPublish()" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                    <i class="fas fa-eye mr-1"></i>Publish
                </button>
                <button type="button" onclick="bulkUnpublish()" class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm">
                    <i class="fas fa-eye-slash mr-1"></i>Unpublish
                </button>
                <button type="button" onclick="bulkDelete()" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
                    <i class="fas fa-trash mr-1"></i>Delete
                </button>
                <button type="button" onclick="clearSelection()" class="text-gray-400 hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">Delete Lecture</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    Are you sure you want to delete this lecture? This action cannot be undone.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="confirm-delete"
                        class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-700">
                    Delete
                </button>
                <button id="cancel-delete"
                        class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md w-24 hover:bg-gray-400">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Delete Confirmation Modal -->
<div id="bulk-delete-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">Delete Multiple Lectures</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    Are you sure you want to delete <span id="bulk-delete-count">0</span> lectures? This action cannot be undone.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="confirm-bulk-delete"
                        class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-700">
                    Delete
                </button>
                <button id="cancel-bulk-delete"
                        class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md w-24 hover:bg-gray-400">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize sortable
    const lecturesList = document.getElementById('lectures-list');
    if (lecturesList) {
        new Sortable(lecturesList, {
            handle: '.drag-handle',
            animation: 150,
            onEnd: function(evt) {
                updateLectureOrder();
            }
        });
    }

    // Dropdown toggles
    document.querySelectorAll('.dropdown-toggle').forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            const dropdown = this.nextElementSibling;

            // Close all other dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu !== dropdown) {
                    menu.classList.add('hidden');
                }
            });

            dropdown.classList.toggle('hidden');
        });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function() {
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.classList.add('hidden');
        });
    });

    // Bulk selection
    const bulkSelectAll = document.getElementById('bulk-select-all');
    const bulkSelects = document.querySelectorAll('.bulk-select');
    const bulkActionsPanel = document.getElementById('bulk-actions-panel');
    const selectedCount = document.getElementById('selected-count');

    bulkSelectAll.addEventListener('change', function() {
        bulkSelects.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActionsPanel();
    });

    bulkSelects.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionsPanel);
    });

    function updateBulkActionsPanel() {
        const selectedLectures = document.querySelectorAll('.bulk-select:checked');
        const count = selectedLectures.length;

        selectedCount.textContent = count;

        if (count > 0) {
            bulkActionsPanel.classList.remove('hidden');
        } else {
            bulkActionsPanel.classList.add('hidden');
        }

        // Update select all checkbox
        if (count === 0) {
            bulkSelectAll.indeterminate = false;
            bulkSelectAll.checked = false;
        } else if (count === bulkSelects.length) {
            bulkSelectAll.indeterminate = false;
            bulkSelectAll.checked = true;
        } else {
            bulkSelectAll.indeterminate = true;
        }
    }

    // Delete modal
    const deleteModal = document.getElementById('delete-modal');
    const confirmDelete = document.getElementById('confirm-delete');
    const cancelDelete = document.getElementById('cancel-delete');
    let lectureToDelete = null;

    window.deleteLecture = function(lectureId) {
        lectureToDelete = lectureId;
        deleteModal.classList.remove('hidden');
    };

    confirmDelete.addEventListener('click', function() {
        if (lectureToDelete) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/instructor/courses/{{ $course->id }}/chapters/{{ $chapter->id }}/lectures/${lectureToDelete}`;
            form.innerHTML = `
                @csrf
                @method('DELETE')
            `;
            document.body.appendChild(form);
            form.submit();
        }
    });

    cancelDelete.addEventListener('click', function() {
        deleteModal.classList.add('hidden');
        lectureToDelete = null;
    });

    // Bulk delete modal
    const bulkDeleteModal = document.getElementById('bulk-delete-modal');
    const confirmBulkDelete = document.getElementById('confirm-bulk-delete');
    const cancelBulkDelete = document.getElementById('cancel-bulk-delete');
    const bulkDeleteCount = document.getElementById('bulk-delete-count');

    window.bulkDelete = function() {
        const selectedLectures = document.querySelectorAll('.bulk-select:checked');
        if (selectedLectures.length === 0) {
            alert('Please select lectures to delete.');
            return;
        }

        bulkDeleteCount.textContent = selectedLectures.length;
        bulkDeleteModal.classList.remove('hidden');
    };

    confirmBulkDelete.addEventListener('click', function() {
        const selectedLectures = document.querySelectorAll('.bulk-select:checked');
        const lectureIds = Array.from(selectedLectures).map(cb => cb.value);

        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("instructor.courses.chapters.lectures.bulk-delete", [$course, $chapter]) }}';
        form.innerHTML = `
            @csrf
            @method('DELETE')
            <input type="hidden" name="lecture_ids" value="${lectureIds.join(',')}">
        `;
        document.body.appendChild(form);
        form.submit();
    });

    cancelBulkDelete.addEventListener('click', function() {
        bulkDeleteModal.classList.add('hidden');
    });

    // Update lecture order
    function updateLectureOrder() {
        const lectureIds = Array.from(lecturesList.children).map(item =>
            item.getAttribute('data-lecture-id')
        );

        fetch(`{{ route('instructor.courses.chapters.lectures.update-order', [$course, $chapter]) }}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                lectures: lectureIds
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update lecture numbers
                lecturesList.querySelectorAll('.lecture-item').forEach((item, index) => {
                    const numberElement = item.querySelector('.w-8.h-8');
                    if (numberElement) {
                        numberElement.textContent = index + 1;
                    }
                });
            }
        })
        .catch(error => {
            console.error('Error updating lecture order:', error);
            // Reload page on error
            location.reload();
        });
    }

    // Toggle lecture status
    window.toggleLectureStatus = function(lectureId) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/instructor/courses/{{ $course->id }}/chapters/{{ $chapter->id }}/lectures/${lectureId}/toggle-status`;
        form.innerHTML = `
            @csrf
            @method('PATCH')
        `;
        document.body.appendChild(form);
        form.submit();
    };

    // Move lecture
    window.moveLecture = function(lectureId, direction) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/instructor/courses/{{ $course->id }}/chapters/{{ $chapter->id }}/lectures/${lectureId}/move-${direction}`;
        form.innerHTML = `
            @csrf
        `;
        document.body.appendChild(form);
        form.submit();
    };

    // Duplicate lecture
    window.duplicateLecture = function(lectureId) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/instructor/courses/{{ $course->id }}/chapters/{{ $chapter->id }}/lectures/${lectureId}/duplicate`;
        form.innerHTML = `
            @csrf
        `;
        document.body.appendChild(form);
        form.submit();
    };

    // Toggle chapter status
    window.toggleChapterStatus = function(chapterId) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/instructor/courses/{{ $course->id }}/chapters/${chapterId}/toggle-status`;
        form.innerHTML = `
            @csrf
            @method('PATCH')
        `;
        document.body.appendChild(form);
        form.submit();
    };

    // Bulk publish
    window.bulkPublish = function() {
        const selectedLectures = document.querySelectorAll('.bulk-select:checked');
        if (selectedLectures.length === 0) {
            alert('Please select lectures to publish.');
            return;
        }

        const lectureIds = Array.from(selectedLectures).map(cb => cb.value);

        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("instructor.courses.chapters.lectures.bulk-publish", [$course, $chapter]) }}';
        form.innerHTML = `
            @csrf
            @method('PATCH')
            <input type="hidden" name="lecture_ids" value="${lectureIds.join(',')}">
        `;
        document.body.appendChild(form);
        form.submit();
    };

    // Bulk unpublish
    window.bulkUnpublish = function() {
        const selectedLectures = document.querySelectorAll('.bulk-select:checked');
        if (selectedLectures.length === 0) {
            alert('Please select lectures to unpublish.');
            return;
        }

        const lectureIds = Array.from(selectedLectures).map(cb => cb.value);

        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("instructor.courses.chapters.lectures.bulk-unpublish", [$course, $chapter]) }}';
        form.innerHTML = `
            @csrf
            @method('PATCH')
            <input type="hidden" name="lecture_ids" value="${lectureIds.join(',')}">
        `;
        document.body.appendChild(form);
        form.submit();
    };

    // Clear selection
    window.clearSelection = function() {
        document.querySelectorAll('.bulk-select').forEach(cb => cb.checked = false);
        bulkSelectAll.checked = false;
        bulkSelectAll.indeterminate = false;
        updateBulkActionsPanel();
    };

    // Reorder lectures toggle
    window.reorderLectures = function() {
        const dragHandles = document.querySelectorAll('.drag-handle');
        const isReorderMode = dragHandles[0] && dragHandles[0].style.display !== 'none';

        dragHandles.forEach(handle => {
            handle.style.display = isReorderMode ? 'none' : 'block';
        });

        const reorderBtn = event.target.closest('button');
        reorderBtn.innerHTML = isReorderMode
            ? '<i class="fas fa-sort text-2xl mb-2"></i><div class="font-medium">Reorder Lectures</div><div class="text-sm opacity-90">Drag & drop to reorder</div>'
            : '<i class="fas fa-check text-2xl mb-2"></i><div class="font-medium">Done Reordering</div><div class="text-sm opacity-90">Click to finish</div>';
    };

    // Bulk actions toggle
    window.bulkActions = function() {
        const bulkCheckboxes = document.querySelectorAll('.bulk-select');
        const selectAllCheckbox = document.getElementById('bulk-select-all');
        const isBulkMode = bulkCheckboxes[0] && bulkCheckboxes[0].style.display !== 'none';

        bulkCheckboxes.forEach(cb => {
            cb.style.display = isBulkMode ? 'none' : 'block';
            cb.checked = false;
        });
        selectAllCheckbox.style.display = isBulkMode ? 'none' : 'block';
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;

        if (isBulkMode) {
            bulkActionsPanel.classList.add('hidden');
        }

        const bulkBtn = event.target.closest('button');
        bulkBtn.innerHTML = isBulkMode
            ? '<i class="fas fa-tasks text-2xl mb-2"></i><div class="font-medium">Bulk Actions</div><div class="text-sm opacity-90">Publish/unpublish multiple</div>'
            : '<i class="fas fa-check text-2xl mb-2"></i><div class="font-medium">Done with Bulk</div><div class="text-sm opacity-90">Click to finish</div>';
    };
});
</script>
@endpush

        <!-- Lectures Management -->
        <div class="bg-gray-800 border border-gray-700 rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-700">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-white">
                        Lectures ({{ $chapter->lectures->count() }})
                    </h3>
                    <div class="flex items-center space-x-4">
                        <div class="text-sm text-gray-400">
                            <i class="fas fa-info-circle mr-1"></i>
                            Drag lectures to reorder
                        </div>
                        <label class="flex items-center">
                            <input type="checkbox" id="bulk-select-all" class="rounded border-gray-600 text-red-600 focus:ring-red-500 focus:ring-offset-gray-800">
                            <span class="ml-2 text-sm text-gray-300">Select All</span>
                        </label>
                    </div>
                </div>
            </div>
