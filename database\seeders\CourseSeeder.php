<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Course;
use App\Models\Chapter;
use App\Models\Lecture;
use App\Models\CourseCategory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🎓 Creating sample courses with full content...');

        // Get instructor user
        $instructor = User::where('email', '<EMAIL>')->first();
        if (!$instructor) {
            $this->command->error('Instructor user not found. Please run DatabaseSeeder first.');
            return;
        }

        // Create additional instructors for variety
        $instructors = collect([$instructor]);
        $additionalInstructors = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'bio' => 'Full-stack developer with 8+ years experience in React, Node.js, and cloud technologies.'
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'bio' => 'AI/ML engineer and data scientist with expertise in Python, TensorFlow, and deep learning.'
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'bio' => 'Business strategist and entrepreneur with 12+ years in digital marketing and e-commerce.'
            ]
        ];

        foreach ($additionalInstructors as $instructorData) {
            $newInstructor = User::firstOrCreate(
                ['email' => $instructorData['email']],
                array_merge($instructorData, [
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                    'role' => 'instructor'
                ])
            );
            $newInstructor->roles()->sync([\App\Models\Role::where('name', 'instructor')->first()->id]);
            $instructors->push($newInstructor);
        }

        // Get categories
        $categories = CourseCategory::whereNull('parent_id')->with('children')->get();

        // Create comprehensive course data - assign first 3 <NAME_EMAIL>
        $coursesData = [
            [
                'title' => 'Complete Web Development Bootcamp 2024',
                'subtitle' => 'Learn HTML, CSS, JavaScript, React, Node.js, and MongoDB from scratch',
                'description' => 'Master web development with this comprehensive bootcamp covering front-end and back-end technologies. Build real-world projects and deploy them to production.',
                'what_you_will_learn' => [
                    'Build responsive websites with HTML5 and CSS3',
                    'Master JavaScript ES6+ and modern frameworks',
                    'Create dynamic web applications with React',
                    'Develop server-side applications with Node.js',
                    'Work with databases using MongoDB',
                    'Deploy applications to cloud platforms'
                ],
                'requirements' => [
                    'Basic computer skills',
                    'No prior programming experience required',
                    'A computer with internet connection'
                ],
                'target_audience' => [
                    'Complete beginners to web development',
                    'Career changers looking to enter tech',
                    'Students wanting to build real projects'
                ],
                'category' => 'Development',
                'subcategory' => 'Web Development',
                'level' => 'beginner',
                'price' => 89.99,
                'original_price' => 199.99,
                'language' => 'English',
                'featured' => true,
                'status' => 'published',
                'instructor_index' => 0, // <EMAIL>
                'chapters' => [
                    [
                        'title' => 'Getting Started with Web Development',
                        'description' => 'Introduction to web development fundamentals and setting up your development environment.',
                        'learning_objectives' => [
                            'Understand how the web works',
                            'Set up development environment',
                            'Learn about web technologies'
                        ],
                        'is_free_preview' => true,
                        'lectures' => [
                            [
                                'title' => 'Welcome to the Course',
                                'type' => 'video',
                                'description' => 'Course overview and what you will learn',
                                'duration_minutes' => 5,
                                'is_free_preview' => true,
                                'content' => 'Welcome to the Complete Web Development Bootcamp! In this course, you will learn everything you need to become a full-stack web developer.'
                            ],
                            [
                                'title' => 'How the Web Works',
                                'type' => 'video',
                                'description' => 'Understanding client-server architecture and HTTP',
                                'duration_minutes' => 15,
                                'is_free_preview' => true,
                                'content' => 'Learn about how browsers communicate with servers, what happens when you type a URL, and the basics of HTTP protocol.'
                            ],
                            [
                                'title' => 'Setting Up Your Development Environment',
                                'type' => 'video',
                                'description' => 'Installing VS Code, Node.js, and essential extensions',
                                'duration_minutes' => 20,
                                'content' => 'Step-by-step guide to setting up your development environment with all the tools you need for this course.'
                            ]
                        ]
                    ],
                    [
                        'title' => 'HTML5 Fundamentals',
                        'description' => 'Master HTML5 elements, semantic markup, and modern HTML features.',
                        'learning_objectives' => [
                            'Write semantic HTML5 markup',
                            'Use modern HTML5 elements',
                            'Create accessible web content'
                        ],
                        'lectures' => [
                            [
                                'title' => 'HTML5 Structure and Syntax',
                                'type' => 'video',
                                'description' => 'Learn HTML5 document structure and basic syntax',
                                'duration_minutes' => 25,
                                'content' => 'Understanding HTML5 document structure, DOCTYPE, and basic HTML elements.'
                            ],
                            [
                                'title' => 'Semantic HTML Elements',
                                'type' => 'video',
                                'description' => 'Using header, nav, main, section, article, and footer elements',
                                'duration_minutes' => 30,
                                'content' => 'Learn about semantic HTML5 elements and how they improve accessibility and SEO.'
                            ],
                            [
                                'title' => 'Forms and Input Elements',
                                'type' => 'video',
                                'description' => 'Creating interactive forms with HTML5 input types',
                                'duration_minutes' => 35,
                                'content' => 'Master HTML5 forms, input types, validation, and accessibility features.'
                            ],
                            [
                                'title' => 'HTML5 Knowledge Check',
                                'type' => 'quiz',
                                'description' => 'Test your understanding of HTML5 concepts',
                                'duration_minutes' => 10,
                                'content' => 'Quiz to assess your knowledge of HTML5 elements and best practices.',
                                'quiz_data' => [
                                    'questions' => [
                                        [
                                            'question' => 'Which HTML5 element is used to define the main content of a document?',
                                            'type' => 'multiple_choice',
                                            'options' => ['<main>', '<content>', '<body>', '<section>'],
                                            'correct_answer' => '0',
                                            'points' => 1
                                        ],
                                        [
                                            'question' => 'HTML5 is backward compatible with HTML4.',
                                            'type' => 'true_false',
                                            'correct_answer' => 'true',
                                            'points' => 1
                                        ],
                                        [
                                            'question' => 'What does semantic HTML mean?',
                                            'type' => 'short_answer',
                                            'sample_answer' => 'Semantic HTML uses HTML elements that have meaning and describe the content they contain, making it more accessible and SEO-friendly.',
                                            'points' => 2
                                        ]
                                    ],
                                    'passing_score' => 75,
                                    'allow_retakes' => true
                                ],
                                'quiz_passing_score' => 75,
                                'quiz_allow_retakes' => true
                            ],
                            [
                                'title' => 'HTML5 Practice Project',
                                'type' => 'assignment',
                                'description' => 'Build a complete HTML5 webpage using semantic elements',
                                'duration_minutes' => 60,
                                'estimated_completion_minutes' => 120,
                                'content' => 'Create a personal portfolio webpage using all the HTML5 concepts learned in this chapter. Include semantic elements, forms, and proper document structure.',
                                'is_mandatory' => true,
                                'attachments' => [
                                    [
                                        'original_name' => 'project_requirements.pdf',
                                        'filename' => 'html5_project_requirements.pdf',
                                        'path' => 'sample_files/assignments/html5_project_requirements.pdf',
                                        'size' => 245760,
                                        'mime_type' => 'application/pdf'
                                    ],
                                    [
                                        'original_name' => 'starter_template.html',
                                        'filename' => 'html5_starter_template.html',
                                        'path' => 'sample_files/assignments/html5_starter_template.html',
                                        'size' => 2048,
                                        'mime_type' => 'text/html'
                                    ]
                                ]
                            ],
                            [
                                'title' => 'HTML5 Reference Guide',
                                'type' => 'resource',
                                'description' => 'Downloadable reference materials for HTML5 elements and attributes',
                                'duration_minutes' => 0,
                                'content' => 'Comprehensive reference guide including HTML5 element cheat sheet, accessibility guidelines, and best practices document.',
                                'resources' => [
                                    [
                                        'original_name' => 'HTML5_Cheat_Sheet.pdf',
                                        'filename' => 'html5_cheat_sheet.pdf',
                                        'path' => 'sample_files/resources/html5_cheat_sheet.pdf',
                                        'size' => 512000,
                                        'mime_type' => 'application/pdf'
                                    ],
                                    [
                                        'original_name' => 'HTML5_Accessibility_Guide.pdf',
                                        'filename' => 'html5_accessibility_guide.pdf',
                                        'path' => 'sample_files/resources/html5_accessibility_guide.pdf',
                                        'size' => 768000,
                                        'mime_type' => 'application/pdf'
                                    ],
                                    [
                                        'original_name' => 'HTML5_Best_Practices.docx',
                                        'filename' => 'html5_best_practices.docx',
                                        'path' => 'sample_files/resources/html5_best_practices.docx',
                                        'size' => 156000,
                                        'mime_type' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            [
                'title' => 'Advanced JavaScript and ES6+',
                'subtitle' => 'Master Modern JavaScript Features and Asynchronous Programming',
                'description' => 'Take your JavaScript skills to the next level with advanced concepts, ES6+ features, and modern development patterns.',
                'what_you_will_learn' => [
                    'ES6+ features and syntax',
                    'Asynchronous JavaScript (Promises, Async/Await)',
                    'Advanced array and object methods',
                    'Modules and modern JavaScript architecture',
                    'Error handling and debugging techniques',
                    'Performance optimization strategies'
                ],
                'requirements' => [
                    'Basic JavaScript knowledge required',
                    'Understanding of HTML and CSS',
                    'Familiarity with programming concepts'
                ],
                'target_audience' => [
                    'JavaScript developers wanting to advance',
                    'Web developers seeking modern JS skills',
                    'Programmers preparing for senior roles'
                ],
                'category' => 'Development',
                'subcategory' => 'Web Development',
                'level' => 'intermediate',
                'price' => 69.99,
                'original_price' => 149.99,
                'language' => 'English',
                'featured' => false,
                'status' => 'published',
                'instructor_index' => 0, // <EMAIL>
                'chapters' => [
                    [
                        'title' => 'ES6+ Features and Syntax',
                        'description' => 'Master modern JavaScript syntax and features introduced in ES6 and beyond.',
                        'learning_objectives' => [
                            'Use arrow functions effectively',
                            'Understand destructuring and spread operator',
                            'Work with template literals and symbols'
                        ],
                        'lectures' => [
                            [
                                'title' => 'Arrow Functions and Lexical This',
                                'type' => 'video',
                                'description' => 'Understanding arrow functions and how they handle the this keyword',
                                'duration_minutes' => 18,
                                'content' => 'Learn about arrow functions, their syntax, and how they differ from regular functions in handling the this context.'
                            ],
                            [
                                'title' => 'Destructuring Assignment',
                                'type' => 'video',
                                'description' => 'Extract values from arrays and objects with destructuring',
                                'duration_minutes' => 22,
                                'content' => 'Master destructuring assignment for arrays and objects, including default values and nested destructuring.'
                            ]
                        ]
                    ],
                    [
                        'title' => 'Asynchronous JavaScript Mastery',
                        'description' => 'Master promises, async/await, and asynchronous programming patterns.',
                        'learning_objectives' => [
                            'Understand promises and promise chaining',
                            'Use async/await for cleaner asynchronous code',
                            'Handle errors in asynchronous operations'
                        ],
                        'lectures' => [
                            [
                                'title' => 'Understanding Promises',
                                'type' => 'video',
                                'description' => 'Deep dive into JavaScript promises and promise chaining',
                                'duration_minutes' => 25,
                                'content' => 'Learn how promises work, how to create them, and how to chain them effectively.'
                            ],
                            [
                                'title' => 'Async/Await Syntax',
                                'type' => 'video',
                                'description' => 'Simplify asynchronous code with async/await',
                                'duration_minutes' => 20,
                                'content' => 'Master the async/await syntax for writing cleaner asynchronous JavaScript code.'
                            ]
                        ]
                    ]
                ]
            ],
            [
                'title' => 'React.js Complete Guide',
                'subtitle' => 'Build Modern Web Applications with React, Hooks, and Context API',
                'description' => 'Master React.js from basics to advanced concepts. Build real-world applications with modern React patterns and best practices.',
                'what_you_will_learn' => [
                    'React fundamentals and JSX syntax',
                    'Component lifecycle and state management',
                    'React Hooks (useState, useEffect, custom hooks)',
                    'Context API for state management',
                    'React Router for navigation',
                    'Testing React applications'
                ],
                'requirements' => [
                    'Strong JavaScript knowledge required',
                    'Understanding of ES6+ features',
                    'Basic HTML and CSS skills'
                ],
                'target_audience' => [
                    'JavaScript developers learning React',
                    'Front-end developers wanting modern skills',
                    'Web developers building SPAs'
                ],
                'category' => 'Development',
                'subcategory' => 'Web Development',
                'level' => 'intermediate',
                'price' => 79.99,
                'original_price' => 169.99,
                'language' => 'English',
                'featured' => true,
                'status' => 'published',
                'instructor_index' => 0, // <EMAIL>
                'chapters' => [
                    [
                        'title' => 'React Fundamentals',
                        'description' => 'Learn the core concepts of React including components, JSX, and props.',
                        'learning_objectives' => [
                            'Understand React components and JSX',
                            'Work with props and state',
                            'Handle events in React'
                        ],
                        'is_free_preview' => true,
                        'lectures' => [
                            [
                                'title' => 'What is React?',
                                'type' => 'video',
                                'description' => 'Introduction to React and its ecosystem',
                                'duration_minutes' => 12,
                                'is_free_preview' => true,
                                'content' => 'Learn what React is, why it\'s popular, and how it fits into modern web development.'
                            ],
                            [
                                'title' => 'Your First React Component',
                                'type' => 'video',
                                'description' => 'Create your first React component and understand JSX',
                                'duration_minutes' => 20,
                                'is_free_preview' => true,
                                'content' => 'Build your first React component and learn JSX syntax and best practices.'
                            ]
                        ]
                    ]
                ]
            ],
            [
                'title' => 'AI & Machine Learning Masterclass',
                'subtitle' => 'Master Python, TensorFlow, and Deep Learning with Real Projects',
                'description' => 'Comprehensive course covering artificial intelligence and machine learning from basics to advanced topics. Build real AI applications and understand the theory behind them.',
                'what_you_will_learn' => [
                    'Python programming for data science',
                    'Machine learning algorithms and concepts',
                    'Deep learning with TensorFlow and Keras',
                    'Computer vision and image recognition',
                    'Natural language processing',
                    'Deploy ML models to production'
                ],
                'requirements' => [
                    'Basic programming knowledge helpful but not required',
                    'High school level mathematics',
                    'Computer with at least 8GB RAM'
                ],
                'target_audience' => [
                    'Aspiring data scientists',
                    'Software developers interested in AI',
                    'Students and professionals wanting to learn ML'
                ],
                'category' => 'Development',
                'subcategory' => 'Data Science',
                'level' => 'intermediate',
                'price' => 129.99,
                'original_price' => 249.99,
                'language' => 'English',
                'featured' => true,
                'status' => 'published',
                'instructor_index' => 2,
                'chapters' => [
                    [
                        'title' => 'Introduction to AI and Machine Learning',
                        'description' => 'Understanding the fundamentals of artificial intelligence and machine learning.',
                        'learning_objectives' => [
                            'Understand what AI and ML are',
                            'Learn different types of machine learning',
                            'Set up Python environment for ML'
                        ],
                        'is_free_preview' => true,
                        'lectures' => [
                            [
                                'title' => 'What is Artificial Intelligence?',
                                'type' => 'video',
                                'description' => 'Introduction to AI concepts and applications',
                                'duration_minutes' => 12,
                                'is_free_preview' => true,
                                'content' => 'Explore the fascinating world of artificial intelligence and its real-world applications.'
                            ],
                            [
                                'title' => 'Types of Machine Learning',
                                'type' => 'video',
                                'description' => 'Supervised, unsupervised, and reinforcement learning',
                                'duration_minutes' => 18,
                                'is_free_preview' => true,
                                'content' => 'Learn about different approaches to machine learning and when to use each one.'
                            ],
                            [
                                'title' => 'Machine Learning Fundamentals',
                                'type' => 'text',
                                'description' => 'Comprehensive text lesson on ML concepts',
                                'estimated_completion_minutes' => 15,
                                'content' => '<h2>Machine Learning Fundamentals</h2><p>Machine Learning is a subset of artificial intelligence that enables computers to learn and make decisions from data without being explicitly programmed for every task.</p><h3>Key Concepts:</h3><ul><li><strong>Algorithm:</strong> A set of rules or instructions for solving a problem</li><li><strong>Model:</strong> The output of an algorithm after training on data</li><li><strong>Training Data:</strong> The dataset used to teach the algorithm</li><li><strong>Features:</strong> Individual measurable properties of observed phenomena</li></ul><p>Understanding these concepts is crucial for your journey in machine learning.</p>',
                                'allow_comments' => true
                            ],
                            [
                                'title' => 'ML Concepts Quiz',
                                'type' => 'quiz',
                                'description' => 'Test your understanding of basic ML concepts',
                                'duration_minutes' => 15,
                                'content' => 'Assessment quiz covering fundamental machine learning concepts and terminology.',
                                'quiz_data' => [
                                    'questions' => [
                                        [
                                            'question' => 'What type of machine learning uses labeled training data?',
                                            'type' => 'multiple_choice',
                                            'options' => ['Supervised Learning', 'Unsupervised Learning', 'Reinforcement Learning', 'Deep Learning'],
                                            'correct_answer' => '0',
                                            'points' => 2
                                        ],
                                        [
                                            'question' => 'Machine learning models can make predictions on new, unseen data.',
                                            'type' => 'true_false',
                                            'correct_answer' => 'true',
                                            'points' => 1
                                        ],
                                        [
                                            'question' => 'Explain the difference between supervised and unsupervised learning.',
                                            'type' => 'short_answer',
                                            'sample_answer' => 'Supervised learning uses labeled data to train models for prediction, while unsupervised learning finds patterns in unlabeled data without predefined outcomes.',
                                            'points' => 3
                                        ]
                                    ],
                                    'passing_score' => 80,
                                    'allow_retakes' => true
                                ],
                                'quiz_passing_score' => 80,
                                'quiz_allow_retakes' => true
                            ],
                            [
                                'title' => 'Python Setup Assignment',
                                'type' => 'assignment',
                                'description' => 'Set up your Python environment for machine learning',
                                'estimated_completion_minutes' => 45,
                                'content' => 'Install Python, Jupyter Notebook, and essential ML libraries. Submit screenshots of your working environment.',
                                'is_mandatory' => true,
                                'attachments' => [
                                    [
                                        'original_name' => 'python_setup_guide.pdf',
                                        'filename' => 'python_ml_setup_guide.pdf',
                                        'path' => 'sample_files/assignments/python_ml_setup_guide.pdf',
                                        'size' => 1024000,
                                        'mime_type' => 'application/pdf'
                                    ]
                                ]
                            ],
                            [
                                'title' => 'ML Resources and Datasets',
                                'type' => 'resource',
                                'description' => 'Essential resources for machine learning practice',
                                'content' => 'Collection of datasets, cheat sheets, and reference materials for machine learning.',
                                'resources' => [
                                    [
                                        'original_name' => 'ML_Algorithms_Cheat_Sheet.pdf',
                                        'filename' => 'ml_algorithms_cheat_sheet.pdf',
                                        'path' => 'sample_files/resources/ml_algorithms_cheat_sheet.pdf',
                                        'size' => 2048000,
                                        'mime_type' => 'application/pdf'
                                    ],
                                    [
                                        'original_name' => 'sample_datasets.zip',
                                        'filename' => 'ml_sample_datasets.zip',
                                        'path' => 'sample_files/resources/ml_sample_datasets.zip',
                                        'size' => 5120000,
                                        'mime_type' => 'application/zip'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            [
                'title' => 'Digital Marketing Mastery 2024',
                'subtitle' => 'Complete Guide to SEO, Social Media, PPC, and Content Marketing',
                'description' => 'Master digital marketing with this comprehensive course covering all major channels and strategies. Learn to create effective campaigns that drive results.',
                'what_you_will_learn' => [
                    'Search engine optimization (SEO)',
                    'Google Ads and PPC advertising',
                    'Social media marketing strategies',
                    'Content marketing and copywriting',
                    'Email marketing automation',
                    'Analytics and performance tracking'
                ],
                'requirements' => [
                    'Basic computer and internet skills',
                    'No prior marketing experience required'
                ],
                'target_audience' => [
                    'Small business owners',
                    'Marketing professionals',
                    'Entrepreneurs and freelancers'
                ],
                'category' => 'Marketing',
                'subcategory' => 'Digital Marketing',
                'level' => 'beginner',
                'price' => 79.99,
                'original_price' => 159.99,
                'language' => 'English',
                'featured' => false,
                'status' => 'published',
                'instructor_index' => 3,
                'chapters' => [
                    [
                        'title' => 'Digital Marketing Fundamentals',
                        'description' => 'Understanding the digital marketing landscape and key concepts.',
                        'learning_objectives' => [
                            'Understand digital marketing ecosystem',
                            'Learn about customer journey',
                            'Set up tracking and analytics'
                        ],
                        'lectures' => [
                            [
                                'title' => 'Digital Marketing Overview',
                                'type' => 'video',
                                'description' => 'Introduction to digital marketing channels and strategies',
                                'duration_minutes' => 15,
                                'content' => 'Get an overview of the digital marketing landscape and key channels.'
                            ]
                        ]
                    ]
                ]
            ],
            [
                'title' => 'UI/UX Design Complete Course',
                'subtitle' => 'Learn User Interface and User Experience Design from Scratch',
                'description' => 'Master UI/UX design principles, tools, and processes. Create beautiful and functional designs that users love.',
                'what_you_will_learn' => [
                    'UI/UX design principles and best practices',
                    'User research and persona development',
                    'Wireframing and prototyping',
                    'Design systems and style guides',
                    'Figma and Adobe XD mastery',
                    'Usability testing and iteration'
                ],
                'requirements' => [
                    'No prior design experience required',
                    'Computer with design software access',
                    'Creative mindset and attention to detail'
                ],
                'target_audience' => [
                    'Aspiring UI/UX designers',
                    'Web developers wanting design skills',
                    'Career changers entering design field'
                ],
                'category' => 'Design',
                'subcategory' => 'UI/UX Design',
                'level' => 'beginner',
                'price' => 99.99,
                'original_price' => 179.99,
                'language' => 'English',
                'featured' => true,
                'status' => 'published',
                'instructor_index' => 1,
                'chapters' => [
                    [
                        'title' => 'Design Thinking and User Research',
                        'description' => 'Understanding users and the design thinking process.',
                        'learning_objectives' => [
                            'Apply design thinking methodology',
                            'Conduct user research',
                            'Create user personas and journey maps'
                        ],
                        'lectures' => [
                            [
                                'title' => 'Introduction to Design Thinking',
                                'type' => 'video',
                                'description' => 'Learn the design thinking process and mindset',
                                'duration_minutes' => 20,
                                'content' => 'Discover the design thinking methodology and how it applies to UI/UX design.'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Create courses
        foreach ($coursesData as $courseData) {
            $this->createCourse($courseData, $instructors, $categories);
        }

        $this->command->info('✅ Sample courses created successfully!');
    }

    private function createCourse($courseData, $instructors, $categories)
    {
        // Find category and subcategory
        $category = $categories->firstWhere('name', $courseData['category']);
        $subcategory = null;
        if ($category && $courseData['subcategory']) {
            $subcategory = $category->children->firstWhere('name', $courseData['subcategory']);
        }

        $instructor = $instructors[$courseData['instructor_index']];
        
        // Prepare course data
        $courseCreateData = [
            'title' => $courseData['title'],
            'subtitle' => $courseData['subtitle'],
            'slug' => Str::slug($courseData['title']),
            'description' => $courseData['description'],
            'what_you_will_learn' => $courseData['what_you_will_learn'], // Let model handle JSON casting
            'requirements' => $courseData['requirements'], // Let model handle JSON casting
            'target_audience' => $courseData['target_audience'], // Let model handle JSON casting
            'category' => $courseData['category'],
            'subcategory' => $courseData['subcategory'] ?? null,
            'category_id' => $category?->id,
            'subcategory_id' => $subcategory?->id,
            'level' => $courseData['level'],
            'price' => $courseData['price'],
            'original_price' => $courseData['original_price'] ?? null,
            'currency' => 'USD',
            'language' => $courseData['language'],
            'featured' => $courseData['featured'] ?? false,
            'status' => $courseData['status'],
            'instructor_id' => $instructor->id,
            'published_at' => $courseData['status'] === 'published' ? now() : null,
        ];

        $course = Course::create($courseCreateData);

        // Create chapters and lectures
        if (isset($courseData['chapters'])) {
            foreach ($courseData['chapters'] as $chapterIndex => $chapterData) {
                $this->createChapter($course, $chapterData, $chapterIndex + 1);
            }
        }

        // Update course statistics
        $course->updateStatistics();

        $this->command->info("Created course: {$course->title}");
    }

    private function createChapter($course, $chapterData, $sortOrder)
    {
        $chapter = Chapter::create([
            'title' => $chapterData['title'],
            'slug' => Str::slug($chapterData['title']),
            'description' => $chapterData['description'],
            'learning_objectives' => $chapterData['learning_objectives'], // Let model handle JSON casting
            'course_id' => $course->id,
            'instructor_id' => $course->instructor_id,
            'sort_order' => $sortOrder,
            'is_published' => true,
            'is_free_preview' => $chapterData['is_free_preview'] ?? false,
        ]);

        // Create lectures
        if (isset($chapterData['lectures'])) {
            foreach ($chapterData['lectures'] as $lectureIndex => $lectureData) {
                $this->createLecture($chapter, $lectureData, $lectureIndex + 1);
            }
        }

        return $chapter;
    }

    private function createLecture($chapter, $lectureData, $sortOrder)
    {
        $lectureCreateData = [
            'title' => $lectureData['title'],
            'slug' => Str::slug($lectureData['title']),
            'description' => $lectureData['description'],
            'type' => $lectureData['type'],
            'chapter_id' => $chapter->id,
            'course_id' => $chapter->course_id,
            'instructor_id' => $chapter->instructor_id,
            'sort_order' => $sortOrder,
            'is_published' => true,
            'is_free_preview' => $lectureData['is_free_preview'] ?? false,
            'duration_minutes' => $lectureData['duration_minutes'] ?? 0,
            'estimated_completion_minutes' => $lectureData['estimated_completion_minutes'] ?? null,
            'content' => $lectureData['content'],
            'is_mandatory' => $lectureData['is_mandatory'] ?? true,
        ];

        // Handle quiz data
        if ($lectureData['type'] === 'quiz' && !empty($lectureData['quiz_data'])) {
            $lectureCreateData['quiz_data'] = $lectureData['quiz_data'];
            $lectureCreateData['quiz_passing_score'] = $lectureData['quiz_passing_score'] ?? 80;
            $lectureCreateData['quiz_allow_retakes'] = $lectureData['quiz_allow_retakes'] ?? true;
        }

        // Handle resource files
        if ($lectureData['type'] === 'resource' && !empty($lectureData['resources'])) {
            $lectureCreateData['resources'] = $lectureData['resources'];
        }

        // Handle assignment attachments
        if ($lectureData['type'] === 'assignment' && !empty($lectureData['attachments'])) {
            $lectureCreateData['attachments'] = $lectureData['attachments'];
        }

        $lecture = Lecture::create($lectureCreateData);

        return $lecture;
    }
}
