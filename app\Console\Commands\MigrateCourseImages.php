<?php

namespace App\Console\Commands;

use App\Models\Course;
use App\Services\PrivateStorageService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class MigrateCourseImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'course:migrate-images {--dry-run : Show what would be migrated without actually doing it}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate course images from public storage to private storage';

    protected PrivateStorageService $storageService;

    public function __construct(PrivateStorageService $storageService)
    {
        parent::__construct();
        $this->storageService = $storageService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('DRY RUN MODE - No files will be moved');
        }

        $courses = Course::whereNotNull('image')
            ->where('image', 'not like', 'private/%')
            ->get();

        if ($courses->isEmpty()) {
            $this->info('No course images need migration.');
            return 0;
        }

        $this->info("Found {$courses->count()} courses with images to migrate.");

        $migrated = 0;
        $failed = 0;

        foreach ($courses as $course) {
            try {
                $this->line("Processing course: {$course->title}");

                if (!Storage::disk('public')->exists($course->image)) {
                    $this->warn("  Image file not found: {$course->image}");
                    $failed++;
                    continue;
                }

                if (!$dryRun) {
                    // Create course directories if they don't exist
                    $this->storageService->createCourseDirectories(
                        (string) $course->instructor_id,
                        (string) $course->id
                    );

                    // Move the image
                    $newPath = $this->moveImageToPrivateStorage($course);

                    if ($newPath) {
                        $course->update(['image' => $newPath]);
                        $this->info("  ✓ Migrated to: {$newPath}");
                        $migrated++;
                    } else {
                        $this->error("  ✗ Failed to migrate image");
                        $failed++;
                    }
                } else {
                    $this->info("  Would migrate: {$course->image}");
                    $migrated++;
                }

            } catch (\Exception $e) {
                $this->error("  ✗ Error migrating {$course->title}: " . $e->getMessage());
                $failed++;
            }
        }

        $this->newLine();
        if ($dryRun) {
            $this->info("DRY RUN COMPLETE:");
            $this->info("  Would migrate: {$migrated} images");
            $this->info("  Would fail: {$failed} images");
        } else {
            $this->info("MIGRATION COMPLETE:");
            $this->info("  Successfully migrated: {$migrated} images");
            $this->info("  Failed: {$failed} images");
        }

        return 0;
    }

    private function moveImageToPrivateStorage(Course $course): ?string
    {
        try {
            // Get the file contents from public storage
            $contents = Storage::disk('public')->get($course->image);

            // Create the private directory path
            $directory = "private/{$course->instructor_id}/{$course->id}/course-images";

            // Generate new filename
            $extension = pathinfo($course->image, PATHINFO_EXTENSION);
            $filename = time() . '_' . uniqid() . '.' . $extension;

            // Store in private storage
            $privatePath = "{$directory}/{$filename}";
            Storage::disk('private')->put($privatePath, $contents);

            // Delete from public storage
            Storage::disk('public')->delete($course->image);

            return $privatePath;

        } catch (\Exception $e) {
            $this->error("Error moving image: " . $e->getMessage());
            return null;
        }
    }
}
