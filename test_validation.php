<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\Validator;
use Illuminate\Foundation\Application;

// Create a minimal Laravel app instance for testing
$app = new Application(__DIR__);

// Test boolean validation
$testCases = [
    ['is_free_preview' => '0'],
    ['is_free_preview' => '1'],
    ['is_free_preview' => 'false'],
    ['is_free_preview' => 'true'],
    ['is_free_preview' => false],
    ['is_free_preview' => true],
];

foreach ($testCases as $index => $data) {
    $validator = Validator::make($data, ['is_free_preview' => 'boolean']);
    $status = $validator->fails() ? 'FAIL' : 'PASS';
    $value = var_export($data['is_free_preview'], true);
    echo "Test " . ($index + 1) . ": {$value} -> {$status}\n";
    if ($validator->fails()) {
        echo "  Errors: " . json_encode($validator->errors()) . "\n";
    }
}
