<?php

namespace App\Http\Controllers;

use App\Services\PrivateStorageService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;

class SecureFileController extends Controller
{
    protected PrivateStorageService $storageService;

    public function __construct(PrivateStorageService $storageService)
    {
        $this->storageService = $storageService;
    }

    /**
     * Serve a private file with proper access control
     */
    public function serve(Request $request, string $filePath): StreamedResponse
    {
        // The middleware has already verified access, so we can serve the file
        $fullPath = $filePath;
        
        // Get file info
        $mimeType = Storage::disk('private')->mimeType($fullPath);
        $size = Storage::disk('private')->size($fullPath);
        $lastModified = Storage::disk('private')->lastModified($fullPath);
        
        // Extract filename from path
        $filename = basename($fullPath);
        
        // Create streamed response for efficient file serving
        return Storage::disk('private')->response($fullPath, $filename, [
            'Content-Type' => $mimeType,
            'Content-Length' => $size,
            'Last-Modified' => gmdate('D, d M Y H:i:s', $lastModified) . ' GMT',
            'Cache-Control' => 'private, max-age=3600',
            'Content-Disposition' => 'inline; filename="' . $filename . '"'
        ]);
    }

    /**
     * Download a private file with proper access control
     */
    public function download(Request $request, string $filePath): StreamedResponse
    {
        // The middleware has already verified access, so we can serve the file
        $fullPath = $filePath;
        
        // Get file info
        $mimeType = Storage::disk('private')->mimeType($fullPath);
        $size = Storage::disk('private')->size($fullPath);
        
        // Extract original filename from path or use a default
        $filename = basename($fullPath);
        
        // Force download with proper headers
        return Storage::disk('private')->download($fullPath, $filename, [
            'Content-Type' => $mimeType,
            'Content-Length' => $size,
            'Cache-Control' => 'no-cache, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0'
        ]);
    }

    /**
     * Stream video content with range support for better performance
     */
    public function streamVideo(Request $request, string $filePath): Response
    {
        $fullPath = $filePath;
        
        if (!Storage::disk('private')->exists($fullPath)) {
            abort(404);
        }

        $fileSize = Storage::disk('private')->size($fullPath);
        $mimeType = Storage::disk('private')->mimeType($fullPath);
        
        // Handle range requests for video streaming
        $range = $request->header('Range');
        
        if ($range) {
            return $this->handleRangeRequest($fullPath, $range, $fileSize, $mimeType);
        }
        
        // Serve full file if no range requested
        return Storage::disk('private')->response($fullPath, null, [
            'Content-Type' => $mimeType,
            'Content-Length' => $fileSize,
            'Accept-Ranges' => 'bytes',
            'Cache-Control' => 'private, max-age=3600'
        ]);
    }

    /**
     * Handle HTTP range requests for video streaming
     */
    private function handleRangeRequest(string $filePath, string $range, int $fileSize, string $mimeType): Response
    {
        // Parse range header (e.g., "bytes=0-1023")
        if (!preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
            abort(416); // Range Not Satisfiable
        }

        $start = (int) $matches[1];
        $end = $matches[2] !== '' ? (int) $matches[2] : $fileSize - 1;
        
        // Validate range
        if ($start > $end || $start >= $fileSize || $end >= $fileSize) {
            abort(416); // Range Not Satisfiable
        }

        $length = $end - $start + 1;
        
        // Create response with partial content
        $response = new StreamedResponse(function() use ($filePath, $start, $length) {
            $stream = Storage::disk('private')->readStream($filePath);
            fseek($stream, $start);
            
            $remaining = $length;
            while ($remaining > 0 && !feof($stream)) {
                $chunkSize = min(8192, $remaining); // 8KB chunks
                echo fread($stream, $chunkSize);
                $remaining -= $chunkSize;
                
                if (ob_get_level()) {
                    ob_flush();
                }
                flush();
            }
            
            fclose($stream);
        }, 206); // Partial Content

        $response->headers->set('Content-Type', $mimeType);
        $response->headers->set('Content-Length', $length);
        $response->headers->set('Content-Range', "bytes {$start}-{$end}/{$fileSize}");
        $response->headers->set('Accept-Ranges', 'bytes');
        $response->headers->set('Cache-Control', 'private, max-age=3600');

        return $response;
    }
}
