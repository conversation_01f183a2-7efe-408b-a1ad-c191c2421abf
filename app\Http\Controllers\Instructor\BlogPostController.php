<?php

namespace App\Http\Controllers\Instructor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class BlogPostController extends Controller
{
    public function index()
    {
        return view('instructor.blog-posts.index');
    }

    public function create()
    {
        return view('instructor.blog-posts.create');
    }

    public function store(Request $request)
    {
        return redirect()->route('instructor.blog-posts.index')
            ->with('success', 'Blog post created successfully.');
    }

    public function show(string $id)
    {
        return view('instructor.blog-posts.show');
    }

    public function edit(string $id)
    {
        return view('instructor.blog-posts.edit');
    }

    public function update(Request $request, string $id)
    {
        return redirect()->route('instructor.blog-posts.index')
            ->with('success', 'Blog post updated successfully.');
    }

    public function destroy(string $id)
    {
        return redirect()->route('instructor.blog-posts.index')
            ->with('success', 'Blog post deleted successfully.');
    }
}
