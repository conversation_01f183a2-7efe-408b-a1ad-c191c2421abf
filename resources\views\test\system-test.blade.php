@extends('layouts.app')

@section('title', 'System Test Results')

@section('content')
<section class="py-12 bg-black">
    <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold text-white mb-8">System Test Results</h1>
            <p class="text-gray-400 mb-8">Comprehensive testing of the course management system - {{ $results['timestamp'] }}</p>
            
            <!-- Overall Status -->
            @php
                $totalTests = count($results['tests']);
                $passedTests = collect($results['tests'])->where('status', 'pass')->count();
                $warningTests = collect($results['tests'])->where('status', 'warning')->count();
                $failedTests = collect($results['tests'])->where('status', 'fail')->count();
                
                $overallStatus = $failedTests > 0 ? 'fail' : ($warningTests > 0 ? 'warning' : 'pass');
            @endphp
            
            <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 mb-8">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Overall Status</h2>
                    <div class="flex items-center gap-2">
                        @if($overallStatus === 'pass')
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-green-400 font-medium">All Tests Passing</span>
                        @elseif($overallStatus === 'warning')
                            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                            <span class="text-yellow-400 font-medium">Some Warnings</span>
                        @else
                            <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                            <span class="text-red-400 font-medium">Tests Failed</span>
                        @endif
                    </div>
                </div>
                
                <div class="grid grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-white">{{ $totalTests }}</div>
                        <div class="text-sm text-gray-400">Total Tests</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-400">{{ $passedTests }}</div>
                        <div class="text-sm text-gray-400">Passed</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-yellow-400">{{ $warningTests }}</div>
                        <div class="text-sm text-gray-400">Warnings</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-red-400">{{ $failedTests }}</div>
                        <div class="text-sm text-gray-400">Failed</div>
                    </div>
                </div>
            </div>

            <!-- Test Results -->
            <div class="space-y-6">
                @foreach($results['tests'] as $testKey => $test)
                    <div class="bg-gray-800 border border-gray-700 rounded-xl overflow-hidden">
                        <div class="p-6 border-b border-gray-700">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-white">{{ $test['name'] }}</h3>
                                <div class="flex items-center gap-2">
                                    @if($test['status'] === 'pass')
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        <span class="text-green-400 font-medium">PASS</span>
                                    @elseif($test['status'] === 'warning')
                                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                        <span class="text-yellow-400 font-medium">WARNING</span>
                                    @else
                                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                        <span class="text-red-400 font-medium">FAIL</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        
                        <div class="p-6">
                            <!-- Test Details -->
                            @if(!empty($test['details']))
                                <div class="mb-4">
                                    <h4 class="text-sm font-medium text-gray-300 mb-3">Test Details</h4>
                                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                        @foreach($test['details'] as $key => $value)
                                            <div class="bg-gray-900 rounded-lg p-3">
                                                <div class="text-xs text-gray-400 mb-1">{{ ucwords(str_replace('_', ' ', $key)) }}</div>
                                                <div class="text-sm font-medium text-white">
                                                    @if(is_bool($value))
                                                        {{ $value ? 'Yes' : 'No' }}
                                                    @elseif(is_array($value))
                                                        {{ implode(', ', $value) }}
                                                    @else
                                                        {{ $value }}
                                                    @endif
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                            
                            <!-- Issues -->
                            @if(!empty($test['issues']))
                                <div>
                                    <h4 class="text-sm font-medium text-red-400 mb-3">Issues Found</h4>
                                    <ul class="space-y-2">
                                        @foreach($test['issues'] as $issue)
                                            <li class="flex items-start gap-2">
                                                <svg class="w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <span class="text-sm text-gray-300">{{ $issue }}</span>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Actions -->
            <div class="mt-8 flex gap-4">
                <a href="{{ route('courses.index') }}" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg transition-colors">
                    Test Course Catalog
                </a>
                <a href="{{ route('my-courses') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
                    Test My Courses
                </a>
                <a href="{{ route('instructor.dashboard') }}" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors">
                    Test Instructor Dashboard
                </a>
                <a href="{{ route('test.payment.dashboard') }}" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors">
                    Test Payment System
                </a>
            </div>
        </div>
    </div>
</section>
@endsection
