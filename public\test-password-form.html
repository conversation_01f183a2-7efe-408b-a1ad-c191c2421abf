<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Form Test</title>
    <link rel="stylesheet" href="css/auth-forms.css">
    <style>
        body {
            background: linear-gradient(135deg, #000000 0%, #1f2937 50%, #000000 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
        }
        .test-container {
            background: rgba(17, 24, 39, 0.95);
            border: 1px solid #374151;
            border-radius: 12px;
            padding: 2rem;
            max-width: 400px;
            width: 100%;
            backdrop-filter: blur(10px);
        }
        .test-title {
            color: #ffffff;
            font-size: 1.5rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 class="test-title">Enhanced Password Form</h2>
        
        <form>
            <div class="auth-form-field">
                <label for="email" class="auth-form-label">Email Address</label>
                <input id="email" type="email" class="auth-form-input" placeholder="Enter your email">
            </div>
            
            <div class="auth-form-field">
                <label for="password" class="auth-form-label">Password</label>
                <div class="password-field-container">
                    <input id="password" type="password" class="password-input-field" placeholder="Enter your password">
                    <button type="button" class="password-toggle-btn" onclick="togglePasswordVisibility('password')">
                        <svg id="password-eye-closed" class="password-eye-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464m1.414 1.414L8.464 8.464m5.656 5.656l1.415 1.415m-1.415-1.415l1.415 1.415M14.828 14.828L16.243 16.243" />
                        </svg>
                        <svg id="password-eye-open" class="password-eye-icon hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                    </button>
                </div>
            </div>
            
            <div class="auth-form-field">
                <label for="confirm_password" class="auth-form-label">Confirm Password</label>
                <div class="password-field-container">
                    <input id="confirm_password" type="password" class="password-input-field" placeholder="Confirm your password">
                    <button type="button" class="password-toggle-btn" onclick="togglePasswordVisibility('confirm_password')">
                        <svg id="confirm_password-eye-closed" class="password-eye-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464m1.414 1.414L8.464 8.464m5.656 5.656l1.415 1.415m-1.415-1.415l1.415 1.415M14.828 14.828L16.243 16.243" />
                        </svg>
                        <svg id="confirm_password-eye-open" class="password-eye-icon hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                    </button>
                </div>
            </div>
            
            <button type="submit" class="auth-form-button">
                Test Submit Button
            </button>
        </form>
    </div>
    
    <script src="js/auth-forms.js"></script>
</body>
</html>