@extends('instructor.layouts.app')

@section('title', isset($resource) ? 'Edit Resource - ' . $resource->title : 'Create Resource - Instructor Dashboard')

@section('content')
<div class="py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-4 mb-4">
                <a href="{{ route('instructor.resources.index') }}" class="text-gray-400 hover:text-white transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <h1 class="text-3xl font-bold text-white">{{ isset($resource) ? 'Edit' : 'Create' }} Resource</h1>
            </div>
            <p class="text-gray-400">{{ isset($resource) ? 'Update your resource information' : 'Add a new resource to your course library' }}</p>
        </div>

        <!-- Form -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
            <form method="POST" action="{{ isset($resource) ? route('instructor.resources.update', $resource) : route('instructor.resources.store') }}" enctype="multipart/form-data" class="space-y-6">
                @csrf
                @if(isset($resource))
                    @method('PUT')
                @endif

                <!-- Title -->
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-300 mb-2">Title *</label>
                    <input type="text" name="title" id="title" value="{{ old('title', $resource->title ?? '') }}" 
                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('title') border-red-500 @enderror"
                           placeholder="Enter resource title" required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                    <textarea name="description" id="description" rows="4" 
                              class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('description') border-red-500 @enderror"
                              placeholder="Enter resource description">{{ old('description', $resource->description ?? '') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Type and Course Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Type -->
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-300 mb-2">Type *</label>
                        <div class="relative">
                            <select name="type" id="type"
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('type') border-red-500 @enderror appearance-none" required>
                                <option value="">Select type</option>
                                <option value="link" {{ old('type', $resource->type ?? '') === 'link' ? 'selected' : '' }}>🔗 External Link</option>
                                <option value="file" {{ old('type', $resource->type ?? '') === 'file' ? 'selected' : '' }}>📁 File Upload</option>
                                <option value="video" {{ old('type', $resource->type ?? '') === 'video' ? 'selected' : '' }}>🎥 Video Resource</option>
                                <option value="tool" {{ old('type', $resource->type ?? '') === 'tool' ? 'selected' : '' }}>🛠️ Tool/Software</option>
                                <option value="reference" {{ old('type', $resource->type ?? '') === 'reference' ? 'selected' : '' }}>📚 Reference Material</option>
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        @error('type')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Course -->
                    <div>
                        <label for="course_id" class="block text-sm font-medium text-gray-300 mb-2">Course (Optional)</label>
                        <div class="relative">
                            <select name="course_id" id="course_id"
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('course_id') border-red-500 @enderror appearance-none">
                                <option value="">No course (standalone resource)</option>
                                @if(isset($courses))
                                    @foreach($courses as $course)
                                        <option value="{{ $course->id }}" {{ old('course_id', $selectedCourse->id ?? $resource->course_id ?? '') == $course->id ? 'selected' : '' }}>
                                            {{ $course->title }}
                                        </option>
                                    @endforeach
                                @endif
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        @error('course_id')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- URL Field (for link type) -->
                <div id="url-field" class="hidden">
                    <label for="url" class="block text-sm font-medium text-gray-300 mb-2">URL *</label>
                    <input type="url" name="url" id="url" value="{{ old('url', $resource->url ?? '') }}" 
                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('url') border-red-500 @enderror"
                           placeholder="https://example.com">
                    @error('url')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- File Upload (for file type) -->
                <div id="file-field" class="hidden">
                    <label for="file" class="block text-sm font-medium text-gray-300 mb-2">File *</label>
                    @if(isset($resource) && $resource->file_path)
                        <div class="mb-3 p-3 bg-gray-800 rounded-lg">
                            <p class="text-white text-sm">Current file: {{ $resource->file_name }}</p>
                            <p class="text-gray-400 text-xs">{{ $resource->formatted_file_size ?? 'Unknown size' }}</p>
                        </div>
                    @endif
                    <div class="flex items-center justify-center w-full">
                        <label for="file" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-700 border-dashed rounded-lg cursor-pointer bg-gray-800 hover:bg-gray-750 transition-colors">
                            <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                <svg class="w-8 h-8 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <p class="mb-2 text-sm text-gray-400">
                                    <span class="font-semibold">Click to upload</span> or drag and drop
                                </p>
                                <p class="text-xs text-gray-500">Any file type. Max file size: 50MB</p>
                            </div>
                            <input id="file" name="file" type="file" class="hidden">
                        </label>
                    </div>
                    @error('file')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                    <!-- File Preview Container -->
                    <div id="file-preview-container"></div>
                </div>

                <!-- Video URL Field (for video type) -->
                <div id="video-field" class="hidden">
                    <label for="video_url" class="block text-sm font-medium text-gray-300 mb-2">Video URL *</label>
                    <input type="url" name="url" id="video_url" value="{{ old('url', $resource->url ?? '') }}"
                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('url') border-red-500 @enderror"
                           placeholder="https://youtube.com/watch?v=... or https://vimeo.com/...">
                    @error('url')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                    <p class="text-xs text-gray-500 mt-1">Supported: YouTube, Vimeo, or direct video links</p>
                </div>

                <!-- Tool URL Field (for tool type) -->
                <div id="tool-field" class="hidden">
                    <label for="tool_url" class="block text-sm font-medium text-gray-300 mb-2">Tool/Software URL *</label>
                    <input type="url" name="url" id="tool_url" value="{{ old('url', $resource->url ?? '') }}"
                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('url') border-red-500 @enderror"
                           placeholder="https://example.com/tool or download link">
                    @error('url')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                    <p class="text-xs text-gray-500 mt-1">Link to the tool, software, or download page</p>
                </div>

                <!-- Reference URL Field (for reference type) -->
                <div id="reference-field" class="hidden">
                    <label for="reference_url" class="block text-sm font-medium text-gray-300 mb-2">Reference URL *</label>
                    <input type="url" name="url" id="reference_url" value="{{ old('url', $resource->url ?? '') }}"
                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('url') border-red-500 @enderror"
                           placeholder="https://documentation.com or reference link">
                    @error('url')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                    <p class="text-xs text-gray-500 mt-1">Link to documentation, articles, or reference materials</p>
                </div>

                <!-- Sort Order and Publication Status -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Sort Order -->
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-300 mb-2">Sort Order</label>
                        <input type="number" name="sort_order" id="sort_order" value="{{ old('sort_order', $resource->sort_order ?? 0) }}" 
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('sort_order') border-red-500 @enderror"
                               placeholder="0" min="0">
                        @error('sort_order')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">Lower numbers appear first</p>
                    </div>

                    <!-- Publication Status -->
                    <div>
                        <label for="is_published" class="block text-sm font-medium text-gray-300 mb-2">Publication Status *</label>
                        <div class="relative">
                            <select name="is_published" id="is_published"
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent @error('is_published') border-red-500 @enderror appearance-none" required>
                                <option value="0" {{ old('is_published', $resource->is_published ?? '0') == '0' ? 'selected' : '' }}>
                                    📝 Draft - Not visible to students
                                </option>
                                <option value="1" {{ old('is_published', $resource->is_published ?? '0') == '1' ? 'selected' : '' }}>
                                    🌟 Published - Live and available to students
                                </option>
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        @error('is_published')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">Choose the visibility status for this resource</p>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-800">
                    <a href="{{ route('instructor.resources.index') }}" class="px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                        {{ isset($resource) ? 'Update Resource' : 'Create Resource' }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize common form components
        if (typeof initInstructorFormComponents === 'function') {
            initInstructorFormComponents();
        }

        const typeSelect = document.getElementById('type');
        const urlField = document.getElementById('url-field');
        const fileField = document.getElementById('file-field');
        const videoField = document.getElementById('video-field');
        const toolField = document.getElementById('tool-field');
        const referenceField = document.getElementById('reference-field');
        const urlInput = document.getElementById('url');
        const fileInput = document.getElementById('file');
        const videoInput = document.getElementById('video_url');
        const toolInput = document.getElementById('tool_url');
        const referenceInput = document.getElementById('reference_url');

        // Show/hide fields based on type selection
        function toggleFields() {
            const selectedType = typeSelect.value;

            console.log('Toggle fields called with type:', selectedType); // Debug log

            // Hide all conditional fields
            urlField.classList.add('hidden');
            fileField.classList.add('hidden');
            videoField.classList.add('hidden');
            toolField.classList.add('hidden');
            referenceField.classList.add('hidden');

            // Remove required attributes
            urlInput.removeAttribute('required');
            fileInput.removeAttribute('required');
            videoInput.removeAttribute('required');
            toolInput.removeAttribute('required');
            referenceInput.removeAttribute('required');

            // Show relevant field and add required attribute
            if (selectedType === 'link') {
                console.log('Showing URL field'); // Debug log
                urlField.classList.remove('hidden');
                urlInput.setAttribute('required', 'required');
            } else if (selectedType === 'file') {
                console.log('Showing file field'); // Debug log
                fileField.classList.remove('hidden');
                @if(!isset($resource) || !$resource->file_path)
                    fileInput.setAttribute('required', 'required');
                @endif
            } else if (selectedType === 'video') {
                console.log('Showing video field'); // Debug log
                videoField.classList.remove('hidden');
                videoInput.setAttribute('required', 'required');
            } else if (selectedType === 'tool') {
                console.log('Showing tool field'); // Debug log
                toolField.classList.remove('hidden');
                toolInput.setAttribute('required', 'required');
            } else if (selectedType === 'reference') {
                console.log('Showing reference field'); // Debug log
                referenceField.classList.remove('hidden');
                referenceInput.setAttribute('required', 'required');
            }
        }

        // Ensure elements exist before proceeding
        if (typeSelect && urlField && fileField && videoField && toolField && referenceField) {
            // Initialize on page load
            toggleFields();

            // Listen for type changes
            typeSelect.addEventListener('change', toggleFields);
        } else {
            console.error('Required elements not found:', {
                typeSelect: !!typeSelect,
                urlField: !!urlField,
                fileField: !!fileField,
                videoField: !!videoField,
                toolField: !!toolField,
                referenceField: !!referenceField
            });
        }

        // Enhanced file upload with preview
        if (typeof enhanceFileUpload === 'function') {
            enhanceFileUpload('file', 'file-preview-container', {
                maxSize: 50 * 1024 * 1024, // 50MB
                allowedTypes: [], // Allow all file types
                onError: function(error) {
                    // Create error message element
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'mt-2 p-3 bg-red-900 border border-red-700 rounded-lg text-red-300 text-sm';
                    errorDiv.textContent = error;

                    // Remove any existing error messages
                    const existingError = document.querySelector('.file-upload-error');
                    if (existingError) existingError.remove();

                    // Add error class and insert error message
                    errorDiv.classList.add('file-upload-error');
                    document.getElementById('file-preview-container').appendChild(errorDiv);

                    // Remove error after 5 seconds
                    setTimeout(() => {
                        if (errorDiv.parentNode) {
                            errorDiv.remove();
                        }
                    }, 5000);
                }
            });
        } else {
            console.error('enhanceFileUpload function not available');
        }
    });
</script>
@endpush
@endsection
