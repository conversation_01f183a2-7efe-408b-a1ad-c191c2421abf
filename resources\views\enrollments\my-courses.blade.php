@extends('layouts.app')

@section('title', 'My Courses - Escape Matrix Academy')

@section('content')
<!-- Hero Section -->
<section class="py-16 bg-gradient-to-br from-black via-gray-900 to-black">
    <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
            <div class="flex flex-col lg:flex-row items-start justify-between gap-8">
                <div>
                    <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
                        My <span class="text-red-500">Learning</span>
                    </h1>
                    <p class="text-xl text-gray-400 max-w-2xl">
                        Track your progress, continue learning, and achieve your goals.
                    </p>
                </div>

                @if($enrollments->count() > 0)
                    <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-6 min-w-[300px]">
                        <h3 class="text-lg font-semibold text-white mb-4">Learning Stats</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-red-500">{{ $stats['total_courses'] }}</div>
                                <div class="text-sm text-gray-400">Total Courses</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-500">{{ number_format($stats['average_progress'], 1) }}%</div>
                                <div class="text-sm text-gray-400">Avg Progress</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-500">{{ floor($stats['total_watch_time'] / 60) }}h</div>
                                <div class="text-sm text-gray-400">Watch Time</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-500">{{ $stats['certificates_earned'] }}</div>
                                <div class="text-sm text-gray-400">Certificates</div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Recently Accessed Courses -->
@if($recentCourses->count() > 0)
<section class="py-8 bg-gray-900">
    <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
            <h2 class="text-2xl font-bold text-white mb-6">Continue Learning</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                @foreach($recentCourses as $enrollment)
                    <div class="bg-gray-800 border border-gray-700 rounded-xl overflow-hidden hover:border-red-500 transition-all duration-300 group">
                        <div class="relative">
                            <img src="{{ $enrollment->course->image_url ?: 'https://via.placeholder.com/400x225/1f2937/ef4444?text=' . urlencode($enrollment->course->title) }}"
                                 alt="{{ $enrollment->course->title }}"
                                 class="w-full h-32 object-cover">

                            <!-- Progress Overlay -->
                            <div class="absolute bottom-0 left-0 right-0 bg-black/80 p-3">
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-300">{{ number_format($enrollment->progress_percentage, 1) }}% complete</span>
                                    <span class="text-gray-400">{{ $enrollment->completed_lectures }}/{{ $enrollment->total_lectures }} lessons</span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-2 mt-2">
                                    <div class="bg-red-500 h-2 rounded-full transition-all duration-300" style="width: {{ $enrollment->progress_percentage }}%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="p-4">
                            <h3 class="text-lg font-semibold text-white mb-2 line-clamp-2">{{ $enrollment->course->title }}</h3>
                            <p class="text-gray-400 text-sm mb-3">{{ $enrollment->course->instructor->name ?? 'Instructor' }}</p>

                            <a href="{{ route('my-courses.view', $enrollment->course) }}"
                               class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors inline-block text-center font-medium">
                                Continue Learning
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</section>
@endif

<!-- My Courses Content -->
<section class="py-16 bg-black">
    <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
            @if($enrollments->count() > 0)
                <!-- Filter Tabs -->
                <div class="flex flex-wrap gap-2 mb-8">
                    <button class="filter-btn active px-4 py-2 bg-red-600 text-white rounded-lg font-medium transition-colors" data-filter="all">
                        All Courses ({{ $stats['total_courses'] }})
                    </button>
                    <button class="filter-btn px-4 py-2 bg-gray-800 text-gray-300 hover:bg-gray-700 rounded-lg font-medium transition-colors" data-filter="in-progress">
                        In Progress ({{ $stats['in_progress_courses'] }})
                    </button>
                    <button class="filter-btn px-4 py-2 bg-gray-800 text-gray-300 hover:bg-gray-700 rounded-lg font-medium transition-colors" data-filter="completed">
                        Completed ({{ $stats['completed_courses'] }})
                    </button>
                    <button class="filter-btn px-4 py-2 bg-gray-800 text-gray-300 hover:bg-gray-700 rounded-lg font-medium transition-colors" data-filter="not-started">
                        Not Started ({{ $stats['not_started_courses'] }})
                    </button>
                </div>

                <!-- Courses Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="courses-grid">
                @foreach($enrollments as $enrollment)
                    @php
                        $statusClass = '';
                        $filterClass = '';

                        if ($enrollment->status === 'completed') {
                            $statusClass = 'completed';
                            $filterClass = 'completed';
                        } elseif ($enrollment->progress_percentage > 0) {
                            $statusClass = 'in-progress';
                            $filterClass = 'in-progress';
                        } else {
                            $statusClass = 'not-started';
                            $filterClass = 'not-started';
                        }
                    @endphp

                    <div class="course-card bg-gray-800 border border-gray-700 rounded-xl overflow-hidden hover:border-red-500 hover:shadow-2xl hover:shadow-red-500/10 transition-all duration-300 group"
                         data-filter="{{ $filterClass }}" data-status="{{ $statusClass }}">
                        <div class="relative">
                            <img src="{{ $enrollment->course->image_url ?: 'https://via.placeholder.com/400x225/1f2937/ef4444?text=' . urlencode($enrollment->course->title) }}"
                                 alt="{{ $enrollment->course->title }}"
                                 class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">

                            <!-- Status Badge -->
                            <div class="absolute top-3 left-3">
                                @if($enrollment->status === 'completed')
                                    <span class="bg-green-600 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center gap-1">
                                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        COMPLETED
                                    </span>
                                @elseif($enrollment->progress_percentage > 0)
                                    <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-bold">
                                        IN PROGRESS
                                    </span>
                                @else
                                    <span class="bg-gray-600 text-white px-3 py-1 rounded-full text-xs font-bold">
                                        NOT STARTED
                                    </span>
                                @endif
                            </div>

                            <!-- Certificate Badge -->
                            @if($enrollment->certificate_issued)
                                <div class="absolute top-3 right-3">
                                    <span class="bg-yellow-500 text-black px-2 py-1 rounded-full text-xs font-bold">
                                        🏆 CERTIFIED
                                    </span>
                                </div>
                            @endif

                            <!-- Progress Overlay -->
                            <div class="absolute bottom-0 left-0 right-0 bg-black/90 p-3">
                                <div class="flex justify-between text-sm text-white mb-2">
                                    <span>{{ number_format($enrollment->progress_percentage, 1) }}% complete</span>
                                    <span>{{ $enrollment->completed_lectures }}/{{ $enrollment->total_lectures }} lessons</span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-2">
                                    <div class="bg-red-500 h-2 rounded-full transition-all duration-300" style="width: {{ $enrollment->progress_percentage }}%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="p-5">
                            <!-- Course Category -->
                            <div class="flex items-center gap-2 mb-2">
                                <span class="text-red-400 text-xs font-medium">{{ $enrollment->course->category }}</span>
                                @if($enrollment->course->subcategory)
                                    <span class="text-gray-500 text-xs">•</span>
                                    <span class="text-gray-400 text-xs">{{ $enrollment->course->subcategory }}</span>
                                @endif
                            </div>

                            <!-- Course Title -->
                            <h3 class="text-lg font-bold text-white mb-2 group-hover:text-red-400 transition-colors line-clamp-2 min-h-[3.5rem]">
                                {{ $enrollment->course->title }}
                            </h3>

                            <!-- Instructor -->
                            <div class="flex items-center gap-2 mb-3">
                                <div class="w-6 h-6 bg-red-600 rounded-full flex items-center justify-center">
                                    <span class="text-white text-xs font-bold">{{ substr($enrollment->course->instructor->name ?? 'I', 0, 1) }}</span>
                                </div>
                                <span class="text-gray-400 text-sm">{{ $enrollment->course->instructor->name ?? 'Instructor' }}</span>
                            </div>

                            <!-- Course Stats -->
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-4">
                                <div class="flex items-center gap-3">
                                    <!-- Watch Time -->
                                    <div class="flex items-center gap-1">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span>{{ floor($enrollment->total_watch_time_minutes / 60) }}h {{ $enrollment->total_watch_time_minutes % 60 }}m</span>
                                    </div>

                                    <!-- Last Accessed -->
                                    @if($enrollment->last_accessed_at)
                                        <div class="flex items-center gap-1">
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                            <span>{{ $enrollment->last_accessed_at->diffForHumans() }}</span>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            @if($enrollment->status === 'completed')
                                <div class="flex gap-2">
                                    <a href="{{ route('my-courses.view', $enrollment->course) }}"
                                       class="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg transition-colors text-center font-medium">
                                        Review Course
                                    </a>
                                    @if($enrollment->certificate_issued)
                                        <a href="#" class="bg-yellow-600 hover:bg-yellow-700 text-white py-3 px-4 rounded-lg transition-colors font-medium">
                                            📜 Certificate
                                        </a>
                                    @endif
                                </div>
                            @else
                                <a href="{{ route('my-courses.view', $enrollment->course) }}"
                                   class="w-full bg-red-600 hover:bg-red-700 text-white py-3 px-4 rounded-lg transition-colors inline-block text-center font-medium">
                                    @if($enrollment->progress_percentage > 0)
                                        Continue Learning
                                    @else
                                        Start Course
                                    @endif
                                </a>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Recommended Courses -->
            @if($recommendedCourses->count() > 0)
                <div class="mt-16">
                    <h2 class="text-2xl font-bold text-white mb-6">Recommended for You</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        @foreach($recommendedCourses as $course)
                            <div class="bg-gray-800 border border-gray-700 rounded-xl overflow-hidden hover:border-red-500 transition-all duration-300 group">
                                <div class="relative">
                                    <img src="{{ $course->image_url ?: 'https://via.placeholder.com/400x225/1f2937/ef4444?text=' . urlencode($course->title) }}"
                                         alt="{{ $course->title }}"
                                         class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300">

                                    <div class="absolute top-2 right-2">
                                        @if($course->price == 0)
                                            <span class="bg-green-600 text-white px-2 py-1 rounded-full text-xs font-bold">FREE</span>
                                        @else
                                            <span class="bg-black/80 text-white px-2 py-1 rounded-full text-xs font-bold">${{ number_format($course->price, 0) }}</span>
                                        @endif
                                    </div>
                                </div>

                                <div class="p-4">
                                    <div class="text-red-400 text-xs font-medium mb-1">{{ $course->category }}</div>
                                    <h3 class="text-sm font-semibold text-white mb-2 line-clamp-2">{{ $course->title }}</h3>
                                    <p class="text-gray-400 text-xs mb-3">{{ $course->instructor->name ?? 'Instructor' }}</p>

                                    <a href="{{ route('courses.show', $course) }}"
                                       class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-3 rounded-lg transition-colors inline-block text-center text-sm font-medium">
                                        View Course
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        @else
            <!-- No Courses Enrolled -->
            <div class="text-center py-16">
                <div class="text-6xl mb-6">📚</div>
                <h3 class="text-2xl font-bold text-white mb-4">No Courses Enrolled Yet</h3>
                <p class="text-gray-400 mb-8 max-w-md mx-auto">
                    Start your transformation journey by enrolling in one of our expert-designed courses.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('courses.index') }}" class="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-md text-lg font-semibold transition-colors">
                        Browse Courses
                    </a>
                    <a href="{{ route('home') }}" class="border border-gray-600 text-gray-300 hover:bg-gray-800 px-8 py-3 rounded-md text-lg font-semibold transition-colors">
                        Back to Home
                    </a>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- Recommended Courses -->
@if($enrollments->count() > 0)
<section class="py-16 bg-gray-900">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-white mb-4">Continue Your Journey</h2>
            <p class="text-gray-400">Discover more courses to expand your skills</p>
        </div>

        <div class="text-center">
            <a href="{{ route('courses.index') }}" class="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-md text-lg font-semibold transition-colors inline-flex items-center">
                Explore More Courses
                <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
            </a>
        </div>
    </div>
</section>
@endif
@endsection

@push('styles')
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.course-card {
    transition: all 0.3s ease;
}

.course-card.hidden {
    display: none;
}

.filter-btn.active {
    background-color: #dc2626;
    color: white;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const courseCards = document.querySelectorAll('.course-card');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');

            // Update active button
            filterButtons.forEach(btn => {
                btn.classList.remove('active', 'bg-red-600', 'text-white');
                btn.classList.add('bg-gray-800', 'text-gray-300');
            });

            this.classList.add('active', 'bg-red-600', 'text-white');
            this.classList.remove('bg-gray-800', 'text-gray-300');

            // Filter courses
            courseCards.forEach(card => {
                const cardFilter = card.getAttribute('data-filter');

                if (filter === 'all' || cardFilter === filter) {
                    card.classList.remove('hidden');
                    card.style.display = 'block';
                } else {
                    card.classList.add('hidden');
                    card.style.display = 'none';
                }
            });

            // Add animation
            setTimeout(() => {
                courseCards.forEach(card => {
                    if (!card.classList.contains('hidden')) {
                        card.style.opacity = '0';
                        card.style.transform = 'translateY(20px)';

                        setTimeout(() => {
                            card.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                            card.style.opacity = '1';
                            card.style.transform = 'translateY(0)';
                        }, 50);
                    }
                });
            }, 50);
        });
    });
});
</script>
@endpush
