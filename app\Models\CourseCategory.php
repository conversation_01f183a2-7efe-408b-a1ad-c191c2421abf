<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class CourseCategory extends Model
{
    use HasFactory;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'color',
        'image',
        'parent_id',
        'sort_order',
        'is_active',
        'is_featured',
        'course_count',
    ];

    protected $casts = [
        'sort_order' => 'integer',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'course_count' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
            if (empty($model->slug)) {
                $model->slug = Str::slug($model->name);
            }
        });

        static::updating(function ($model) {
            if ($model->isDirty('name') && empty($model->slug)) {
                $model->slug = Str::slug($model->name);
            }
        });
    }

    /**
     * Get the parent category.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(CourseCategory::class, 'parent_id');
    }

    /**
     * Get the child categories (subcategories).
     */
    public function children(): HasMany
    {
        return $this->hasMany(CourseCategory::class, 'parent_id')->orderBy('sort_order');
    }

    /**
     * Get the active child categories.
     */
    public function activeChildren(): HasMany
    {
        return $this->children()->where('is_active', true);
    }

    /**
     * Get the courses in this category.
     */
    public function courses(): HasMany
    {
        return $this->hasMany(Course::class, 'category_id');
    }

    /**
     * Get the published courses in this category.
     */
    public function publishedCourses(): HasMany
    {
        return $this->courses()->where('status', 'published');
    }

    /**
     * Get the courses in this subcategory.
     */
    public function subcategoryCourses(): HasMany
    {
        return $this->hasMany(Course::class, 'subcategory_id');
    }

    /**
     * Scope to filter active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter featured categories.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to get only parent categories.
     */
    public function scopeParents($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope to get only subcategories.
     */
    public function scopeSubcategories($query)
    {
        return $query->whereNotNull('parent_id');
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Check if this is a parent category.
     */
    public function isParent(): bool
    {
        return is_null($this->parent_id);
    }

    /**
     * Check if this is a subcategory.
     */
    public function isSubcategory(): bool
    {
        return !is_null($this->parent_id);
    }

    /**
     * Check if the category is active.
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Check if the category is featured.
     */
    public function isFeatured(): bool
    {
        return $this->is_featured;
    }

    /**
     * Get the full category path.
     */
    public function getFullPath(): string
    {
        if ($this->isParent()) {
            return $this->name;
        }
        return $this->parent->name . ' > ' . $this->name;
    }

    /**
     * Update course count for this category.
     */
    public function updateCourseCount(): void
    {
        $count = $this->publishedCourses()->count();
        if ($this->isSubcategory()) {
            $count += $this->subcategoryCourses()->where('status', 'published')->count();
        }
        $this->update(['course_count' => $count]);
    }

    /**
     * Get category icon HTML.
     */
    public function getIconHtml(): string
    {
        if ($this->icon) {
            return '<i class="' . $this->icon . '"></i>';
        }
        return '<i class="fas fa-folder"></i>';
    }

    /**
     * Get category color style.
     */
    public function getColorStyle(): string
    {
        if ($this->color) {
            return 'color: ' . $this->color . ';';
        }
        return '';
    }

    /**
     * Get category background color style.
     */
    public function getBackgroundColorStyle(): string
    {
        if ($this->color) {
            return 'background-color: ' . $this->color . ';';
        }
        return '';
    }

    /**
     * Get all descendants (children and their children).
     */
    public function getAllDescendants()
    {
        $descendants = collect();
        
        foreach ($this->children as $child) {
            $descendants->push($child);
            $descendants = $descendants->merge($child->getAllDescendants());
        }
        
        return $descendants;
    }

    /**
     * Get all ancestors (parent and their parents).
     */
    public function getAllAncestors()
    {
        $ancestors = collect();
        
        if ($this->parent) {
            $ancestors->push($this->parent);
            $ancestors = $ancestors->merge($this->parent->getAllAncestors());
        }
        
        return $ancestors;
    }

    /**
     * Get breadcrumb array.
     */
    public function getBreadcrumb(): array
    {
        $breadcrumb = [];
        
        if ($this->parent) {
            $breadcrumb = array_merge($this->parent->getBreadcrumb(), $breadcrumb);
        }
        
        $breadcrumb[] = [
            'name' => $this->name,
            'slug' => $this->slug,
            'url' => route('courses.category', $this->slug)
        ];
        
        return $breadcrumb;
    }
}
