<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Support\Str;

class Course extends Model
{
    use HasFactory;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'title',
        'subtitle',
        'slug',
        'description',
        'what_you_will_learn',
        'requirements',
        'target_audience',
        'category',
        'subcategory',
        'category_id',
        'subcategory_id',
        'language',
        'level',
        'price',
        'original_price',
        'currency',
        'image',
        'promotional_video',
        'featured',
        'status',
        'instructor_id',
        'total_duration_minutes',
        'total_lectures',
        'total_chapters',
        'average_rating',
        'total_reviews',
        'total_students',
        'published_at',
    ];

    protected $casts = [
        'what_you_will_learn' => 'array',
        'requirements' => 'array',
        'target_audience' => 'array',
        'price' => 'decimal:2',
        'original_price' => 'decimal:2',
        'featured' => 'boolean',
        'total_duration_minutes' => 'integer',
        'total_lectures' => 'integer',
        'total_chapters' => 'integer',
        'average_rating' => 'float',
        'total_reviews' => 'integer',
        'total_students' => 'integer',
        'published_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
            if (empty($model->slug)) {
                $model->slug = Str::slug($model->title);
            }
        });

        static::updating(function ($model) {
            if ($model->isDirty('title') && empty($model->slug)) {
                $model->slug = Str::slug($model->title);
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the instructor that owns the course.
     */
    public function instructor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'instructor_id');
    }

    /**
     * Get the category for the course.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(CourseCategory::class, 'category_id');
    }

    /**
     * Get the subcategory for the course.
     */
    public function subcategory(): BelongsTo
    {
        return $this->belongsTo(CourseCategory::class, 'subcategory_id');
    }

    /**
     * Get the chapters for the course.
     */
    public function chapters(): HasMany
    {
        return $this->hasMany(Chapter::class)->orderBy('sort_order');
    }

    /**
     * Get the published chapters for the course.
     */
    public function publishedChapters(): HasMany
    {
        return $this->chapters()->where('is_published', true);
    }

    /**
     * Get all lectures through chapters.
     */
    public function lectures(): HasManyThrough
    {
        return $this->hasManyThrough(Lecture::class, Chapter::class)->orderBy('chapters.sort_order')->orderBy('lectures.sort_order');
    }

    /**
     * Get published lectures through chapters.
     */
    public function publishedLectures(): HasManyThrough
    {
        return $this->lectures()->where('lectures.is_published', true);
    }

    /**
     * Get the enrollments for the course.
     */
    public function enrollments(): HasMany
    {
        return $this->hasMany(Enrollment::class);
    }

    /**
     * Get the active enrollments for the course.
     */
    public function activeEnrollments(): HasMany
    {
        return $this->enrollments()->where('status', 'active');
    }

    /**
     * Get the payments for the course.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the completed payments for the course.
     */
    public function completedPayments(): HasMany
    {
        return $this->payments()->where('status', 'completed');
    }

    /**
     * Get the reviews for the course.
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(CourseReview::class);
    }

    /**
     * Get the published reviews for the course.
     */
    public function publishedReviews(): HasMany
    {
        return $this->reviews()->where('is_published', true);
    }

    /**
     * Scope to filter published courses.
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope to filter featured courses.
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Scope to filter by instructor.
     */
    public function scopeByInstructor($query, $instructorId)
    {
        return $query->where('instructor_id', $instructorId);
    }

    /**
     * Scope to filter by category.
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope to filter by level.
     */
    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    /**
     * Scope to filter by price range.
     */
    public function scopeByPriceRange($query, $minPrice = null, $maxPrice = null)
    {
        if ($minPrice !== null) {
            $query->where('price', '>=', $minPrice);
        }
        if ($maxPrice !== null) {
            $query->where('price', '<=', $maxPrice);
        }
        return $query;
    }

    /**
     * Check if the course is published.
     */
    public function isPublished(): bool
    {
        return $this->status === 'published';
    }

    /**
     * Check if the course is free.
     */
    public function isFree(): bool
    {
        return $this->price == 0;
    }

    /**
     * Check if the course has a discount.
     */
    public function hasDiscount(): bool
    {
        return $this->original_price && $this->original_price > $this->price;
    }

    /**
     * Get the discount percentage.
     */
    public function getDiscountPercentage(): int
    {
        if (!$this->hasDiscount()) {
            return 0;
        }
        return round((($this->original_price - $this->price) / $this->original_price) * 100);
    }

    /**
     * Get the course duration in human readable format.
     */
    public function getFormattedDuration(): string
    {
        if ($this->total_duration_minutes < 60) {
            return $this->total_duration_minutes . ' minutes';
        }
        
        $hours = floor($this->total_duration_minutes / 60);
        $minutes = $this->total_duration_minutes % 60;
        
        if ($minutes === 0) {
            return $hours . ' hour' . ($hours > 1 ? 's' : '');
        }
        
        return $hours . 'h ' . $minutes . 'm';
    }

    /**
     * Get the URL for the course image.
     */
    public function getImageUrl(): ?string
    {
        if (!$this->image) {
            return null;
        }

        // Extract filename from the stored path
        $filename = basename($this->image);

        // Generate URL using the FileController route for instructor files
        return route('files.instructor-file', [
            'userId' => $this->instructor_id,
            'courseId' => $this->id,
            'type' => 'images',
            'filename' => $filename
        ]);
    }

    /**
     * Update course statistics.
     */
    public function updateStatistics(): void
    {
        $this->update([
            'total_chapters' => $this->chapters()->count(),
            'total_lectures' => $this->lectures()->count(),
            'total_duration_minutes' => $this->lectures()->sum('duration_minutes'),
            'total_students' => $this->enrollments()->count(),
            'total_reviews' => $this->reviews()->where('is_published', true)->count(),
            'average_rating' => $this->reviews()->where('is_published', true)->avg('rating') ?: 0,
        ]);
    }
}
