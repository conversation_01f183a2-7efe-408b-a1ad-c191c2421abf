<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\TeamMember;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class HomeController extends Controller
{
    public function index()
    {
        $featuredCourses = Course::where('featured', true)
            ->where('status', 'published')
            ->with(['instructor'])
            ->take(3)
            ->get();

        // Add enrollment status for authenticated users
        if (auth()->check()) {
            $userEnrollments = auth()->user()->enrollments()->pluck('course_id')->toArray();
            $featuredCourses->each(function ($course) use ($userEnrollments) {
                $course->is_enrolled = in_array($course->id, $userEnrollments);
            });
        } else {
            $featuredCourses->each(function ($course) {
                $course->is_enrolled = false;
            });
        }

        $stats = [
            'students' => $this->getStudentCount(),
            'courses' => Course::where('status', 'published')->count(),
            'success_rate' => 95,
            'support_hours' => '24/7'
        ];

        return view('home', compact('featuredCourses', 'stats'));
    }

    /**
     * Get the count of students using both RBAC and legacy role column.
     */
    private function getStudentCount(): int
    {
        // Count users with student role in RBAC system
        $rbacStudents = User::whereHas('activeRoles', function ($query) {
            $query->where('name', 'student');
        })->count();

        // Count users with legacy role column (for backward compatibility)
        $legacyStudents = User::where('role', 'student')->count();

        // Return the higher count to account for both systems
        return max($rbacStudents, $legacyStudents);
    }
    
    public function about()
    {
        $team = TeamMember::active()->ordered()->get();

        return view('about', compact('team'));
    }
    
    public function contact()
    {
        return view('contact');
    }
    
    public function submitContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000'
        ]);
        
        // Send email notification
        // Mail::to('<EMAIL>')->send(new ContactFormMail($request->all()));
        
        return back()->with('success', 'Thank you for your message! We\'ll get back to you within 24 hours.');
    }
}
