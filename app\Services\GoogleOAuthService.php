<?php

namespace App\Services;

use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Lara<PERSON>\Socialite\Contracts\User as SocialiteUser;
use Exception;

class GoogleOAuthService
{
    /**
     * Handle Google user authentication and account creation/linking
     */
    public function handleGoogleUser(SocialiteUser $googleUser): ?User
    {
        try {
            // Validate Google user data first
            if (!$this->validateGoogleUser($googleUser)) {
                return null;
            }

            return DB::transaction(function () use ($googleUser) {
                // First, try to find user by Google ID
                $user = User::where('google_id', $googleUser->getId())->first();
                
                if ($user) {
                    // Update user info from Google if needed
                    $this->updateUserFromGoogle($user, $googleUser);
                    return $user;
                }
                
                // Check if user exists with same email
                $existingUser = User::where('email', $googleUser->getEmail())->first();
                
                if ($existingUser) {
                    // Link Google account to existing user
                    return $this->linkGoogleToExistingUser($existingUser, $googleUser);
                }
                
                // Create new user
                return $this->createUserFromGoogle($googleUser);
            });
        } catch (Exception $e) {
            Log::error('Google OAuth user handling failed', [
                'google_id' => $googleUser->getId(),
                'email' => $googleUser->getEmail(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return null;
        }
    }

    /**
     * Create a new user from Google OAuth data
     */
    protected function createUserFromGoogle(SocialiteUser $googleUser): User
    {
        $userData = [
            'name' => $googleUser->getName() ?: $this->extractNameFromEmail($googleUser->getEmail()),
            'email' => $googleUser->getEmail(),
            'google_id' => $googleUser->getId(),
            'password' => bcrypt(Str::random(32)), // Random password since they'll use Google OAuth
            'email_verified_at' => now(), // Google accounts are pre-verified
            'avatar' => $googleUser->getAvatar(),
        ];

        $user = User::create($userData);
        
        // Assign default student role
        $this->assignDefaultRole($user);
        
        Log::info('New user created via Google OAuth', [
            'user_id' => $user->id,
            'email' => $user->email,
            'google_id' => $user->google_id
        ]);
        
        return $user;
    }

    /**
     * Link Google account to existing user
     */
    protected function linkGoogleToExistingUser(User $user, SocialiteUser $googleUser): User
    {
        // Security check: ensure the Google account isn't already linked to another user
        $googleLinkedUser = User::where('google_id', $googleUser->getId())
            ->where('id', '!=', $user->id)
            ->first();
            
        if ($googleLinkedUser) {
            throw new Exception('Google account is already linked to another user');
        }
        
        // Update user with Google data
        $updateData = [
            'google_id' => $googleUser->getId(),
            'email_verified_at' => $user->email_verified_at ?: now(), // Verify email if not already verified
        ];
        
        // Update avatar if user doesn't have one
        if (!$user->avatar && $googleUser->getAvatar()) {
            $updateData['avatar'] = $googleUser->getAvatar();
        }
        
        $user->update($updateData);
        
        Log::info('Google account linked to existing user', [
            'user_id' => $user->id,
            'email' => $user->email,
            'google_id' => $user->google_id
        ]);
        
        return $user;
    }

    /**
     * Update user information from Google data
     */
    protected function updateUserFromGoogle(User $user, SocialiteUser $googleUser): void
    {
        $updateData = [];
        
        // Update avatar if Google has a newer one and user doesn't have a custom avatar
        if ($googleUser->getAvatar() && (!$user->avatar || str_contains($user->avatar, 'googleusercontent.com'))) {
            $updateData['avatar'] = $googleUser->getAvatar();
        }
        
        // Update name if it's significantly different (but be careful not to overwrite custom names)
        $googleName = $googleUser->getName();
        if ($googleName && $this->shouldUpdateName($user->name, $googleName)) {
            $updateData['name'] = $googleName;
        }
        
        if (!empty($updateData)) {
            $user->update($updateData);
            
            Log::info('User updated from Google OAuth', [
                'user_id' => $user->id,
                'updated_fields' => array_keys($updateData)
            ]);
        }
    }

    /**
     * Assign default role to new user
     */
    protected function assignDefaultRole(User $user): void
    {
        try {
            // Assign student role by default
            $user->assignRole(Role::STUDENT);
            
            Log::info('Default role assigned to new Google OAuth user', [
                'user_id' => $user->id,
                'role' => Role::STUDENT
            ]);
        } catch (Exception $e) {
            Log::error('Failed to assign default role to Google OAuth user', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            
            // Fallback to legacy role field
            $user->update(['role' => 'student']);
        }
    }

    /**
     * Extract name from email if no name is provided
     */
    protected function extractNameFromEmail(string $email): string
    {
        $localPart = explode('@', $email)[0];
        
        // Replace common separators with spaces and title case
        $name = str_replace(['.', '_', '-', '+'], ' ', $localPart);
        $name = ucwords(strtolower($name));
        
        return $name ?: 'User';
    }

    /**
     * Determine if we should update the user's name
     */
    protected function shouldUpdateName(string $currentName, string $googleName): bool
    {
        // Don't update if names are very similar
        if (similar_text(strtolower($currentName), strtolower($googleName)) > 0.8) {
            return false;
        }
        
        // Don't update if current name looks like it was manually set
        // (contains multiple words, proper capitalization, etc.)
        if (str_word_count($currentName) > 1 && $currentName === ucwords($currentName)) {
            return false;
        }
        
        // Update if current name looks auto-generated (like "User" or email-derived)
        if (in_array(strtolower($currentName), ['user', 'guest']) || 
            str_contains($currentName, '@') || 
            preg_match('/^[a-z]+\d+$/i', $currentName)) {
            return true;
        }
        
        return false;
    }

    /**
     * Validate Google user data
     */
    public function validateGoogleUser(SocialiteUser $googleUser): bool
    {
        if (!$googleUser->getId()) {
            Log::warning('Google OAuth: Missing Google ID');
            return false;
        }
        
        if (!$googleUser->getEmail()) {
            Log::warning('Google OAuth: Missing email', ['google_id' => $googleUser->getId()]);
            return false;
        }
        
        if (!filter_var($googleUser->getEmail(), FILTER_VALIDATE_EMAIL)) {
            Log::warning('Google OAuth: Invalid email format', [
                'google_id' => $googleUser->getId(),
                'email' => $googleUser->getEmail()
            ]);
            return false;
        }
        
        return true;
    }
}
