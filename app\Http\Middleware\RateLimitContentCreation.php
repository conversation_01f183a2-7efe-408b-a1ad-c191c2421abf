<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class RateLimitContentCreation
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $maxAttempts = '50', string $decayMinutes = '1440'): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        $key = 'content_creation:' . $user->id;

        // Different limits based on user role
        if ($user->isSuperAdmin()) {
            return $next($request); // No limits for super admin
        }

        if ($user->isAdmin()) {
            $maxAttempts = 100; // Higher limit for admin
        } elseif ($user->isInstructor()) {
            $maxAttempts = 50; // Standard limit for instructors
        } else {
            abort(403, 'Unauthorized to create content.');
        }

        // Check rate limit
        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $seconds = RateLimiter::availableIn($key);
            $hours = ceil($seconds / 3600);
            
            return back()->withErrors([
                'rate_limit' => "Too many content creation attempts. Please try again in {$hours} hour(s)."
            ])->withInput();
        }

        // Increment the rate limiter
        RateLimiter::hit($key, $decayMinutes * 60);

        return $next($request);
    }
}
