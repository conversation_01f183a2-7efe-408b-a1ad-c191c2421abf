<?php

namespace App\Policies;

use App\Models\Course;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CoursePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any courses.
     */
    public function viewAny(User $user): bool
    {
        return $user->isInstructor() || $user->hasAdminPrivileges();
    }

    /**
     * Determine whether the user can view the course.
     */
    public function view(User $user, Course $course): bool
    {
        // Admins can view any course
        if ($user->hasAdminPrivileges()) {
            return true;
        }

        // Instructors can only view their own courses
        if ($user->isInstructor()) {
            return $course->instructor_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can create courses.
     */
    public function create(User $user): bool
    {
        return $user->isInstructor() || $user->hasAdminPrivileges();
    }

    /**
     * Determine whether the user can update the course.
     */
    public function update(User $user, Course $course): bool
    {
        // Admins can update any course
        if ($user->hasAdminPrivileges()) {
            return true;
        }

        // Instructors can only update their own courses
        if ($user->isInstructor()) {
            return $course->instructor_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the course.
     */
    public function delete(User $user, Course $course): bool
    {
        // Admins can delete any course
        if ($user->hasAdminPrivileges()) {
            return true;
        }

        // Instructors can only delete their own courses if they have no enrollments
        if ($user->isInstructor() && $course->instructor_id === $user->id) {
            return $course->enrollments()->count() === 0;
        }

        return false;
    }

    /**
     * Determine whether the user can restore the course.
     */
    public function restore(User $user, Course $course): bool
    {
        return $user->hasAdminPrivileges();
    }

    /**
     * Determine whether the user can permanently delete the course.
     */
    public function forceDelete(User $user, Course $course): bool
    {
        return $user->isSuperAdmin();
    }

    /**
     * Determine whether the user can publish/unpublish the course.
     */
    public function publish(User $user, Course $course): bool
    {
        // Admins can publish/unpublish any course
        if ($user->hasAdminPrivileges()) {
            return true;
        }

        // Instructors can only publish/unpublish their own courses
        if ($user->isInstructor()) {
            return $course->instructor_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can manage course content (chapters, lectures).
     */
    public function manageContent(User $user, Course $course): bool
    {
        return $this->update($user, $course);
    }

    /**
     * Determine whether the user can view course analytics.
     */
    public function viewAnalytics(User $user, Course $course): bool
    {
        return $this->view($user, $course);
    }

    /**
     * Determine whether the user can manage course enrollments.
     */
    public function manageEnrollments(User $user, Course $course): bool
    {
        // Admins can manage any course enrollments
        if ($user->hasAdminPrivileges()) {
            return true;
        }

        // Instructors can manage their own course enrollments
        if ($user->isInstructor()) {
            return $course->instructor_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can duplicate the course.
     */
    public function duplicate(User $user, Course $course): bool
    {
        // Must be able to view the course and create new courses
        return $this->view($user, $course) && $this->create($user);
    }
}
