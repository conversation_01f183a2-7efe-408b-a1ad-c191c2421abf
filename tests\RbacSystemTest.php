<?php

// Simple RBAC System Test Script
// Run with: php tests/RbacSystemTest.php

require_once __DIR__ . '/../vendor/autoload.php';

use App\Models\User;
use App\Models\Role;
use App\Models\Permission;

// Bootstrap Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== RBAC System Comprehensive Test ===\n\n";

// Test 1: Verify roles exist
echo "1. Testing Roles:\n";
$roles = Role::all();
foreach ($roles as $role) {
    echo "   - {$role->name}: {$role->display_name} (Priority: {$role->priority})\n";
}
echo "   Total roles: " . $roles->count() . "\n\n";

// Test 2: Verify permissions exist
echo "2. Testing Permissions:\n";
$permissions = Permission::all();
$permissionsByCategory = $permissions->groupBy('category');
foreach ($permissionsByCategory as $category => $perms) {
    echo "   {$category}: " . $perms->count() . " permissions\n";
}
echo "   Total permissions: " . $permissions->count() . "\n\n";

// Test 3: Test user role assignments
echo "3. Testing User Role Assignments:\n";
$testUsers = [
    '<EMAIL>' => 'student',
    '<EMAIL>' => 'instructor', 
    '<EMAIL>' => 'admin',
    '<EMAIL>' => 'superadmin'
];

foreach ($testUsers as $email => $expectedRole) {
    $user = User::where('email', $email)->first();
    if ($user) {
        $hasRole = $user->hasRole($expectedRole);
        $userRoles = $user->roles->pluck('name')->toArray();
        echo "   {$email}: Has {$expectedRole} role? " . ($hasRole ? 'YES' : 'NO') . 
             " (Roles: " . implode(', ', $userRoles) . ")\n";
    } else {
        echo "   {$email}: USER NOT FOUND\n";
    }
}
echo "\n";

// Test 4: Test permission checking
echo "4. Testing Permission Checking:\n";
$student = User::where('email', '<EMAIL>')->first();
$instructor = User::where('email', '<EMAIL>')->first();
$admin = User::where('email', '<EMAIL>')->first();
$superadmin = User::where('email', '<EMAIL>')->first();

$testPermissions = [
    'courses.view' => ['student' => true, 'instructor' => true, 'admin' => true, 'superadmin' => true],
    'courses.create' => ['student' => false, 'instructor' => true, 'admin' => true, 'superadmin' => true],
    'users.manage_roles' => ['student' => false, 'instructor' => false, 'admin' => true, 'superadmin' => true],
    'payments.manage' => ['student' => false, 'instructor' => false, 'admin' => false, 'superadmin' => true],
];

foreach ($testPermissions as $permission => $expectedResults) {
    echo "   Permission: {$permission}\n";
    
    if ($student) {
        $hasPermission = $student->hasPermission($permission);
        $expected = $expectedResults['student'];
        $status = ($hasPermission === $expected) ? 'PASS' : 'FAIL';
        echo "     Student: {$status} (Expected: " . ($expected ? 'true' : 'false') . ", Got: " . ($hasPermission ? 'true' : 'false') . ")\n";
    }
    
    if ($instructor) {
        $hasPermission = $instructor->hasPermission($permission);
        $expected = $expectedResults['instructor'];
        $status = ($hasPermission === $expected) ? 'PASS' : 'FAIL';
        echo "     Instructor: {$status} (Expected: " . ($expected ? 'true' : 'false') . ", Got: " . ($hasPermission ? 'true' : 'false') . ")\n";
    }
    
    if ($admin) {
        $hasPermission = $admin->hasPermission($permission);
        $expected = $expectedResults['admin'];
        $status = ($hasPermission === $expected) ? 'PASS' : 'FAIL';
        echo "     Admin: {$status} (Expected: " . ($expected ? 'true' : 'false') . ", Got: " . ($hasPermission ? 'true' : 'false') . ")\n";
    }
    
    if ($superadmin) {
        $hasPermission = $superadmin->hasPermission($permission);
        $expected = $expectedResults['superadmin'];
        $status = ($hasPermission === $expected) ? 'PASS' : 'FAIL';
        echo "     SuperAdmin: {$status} (Expected: " . ($expected ? 'true' : 'false') . ", Got: " . ($hasPermission ? 'true' : 'false') . ")\n";
    }
    echo "\n";
}

// Test 5: Test role hierarchy methods
echo "5. Testing Role Hierarchy Methods:\n";
if ($admin) {
    echo "   Admin isAdmin(): " . ($admin->isAdmin() ? 'true' : 'false') . "\n";
    echo "   Admin isSuperAdmin(): " . ($admin->isSuperAdmin() ? 'true' : 'false') . "\n";
}
if ($superadmin) {
    echo "   SuperAdmin isAdmin(): " . ($superadmin->isAdmin() ? 'true' : 'false') . "\n";
    echo "   SuperAdmin isSuperAdmin(): " . ($superadmin->isSuperAdmin() ? 'true' : 'false') . "\n";
}
echo "\n";

// Test 6: Test database relationships
echo "6. Testing Database Relationships:\n";
$roleCount = Role::count();
$permissionCount = Permission::count();
$userRoleCount = \DB::table('user_roles')->count();
$rolePermissionCount = \DB::table('role_permissions')->count();

echo "   Roles in database: {$roleCount}\n";
echo "   Permissions in database: {$permissionCount}\n";
echo "   User-Role assignments: {$userRoleCount}\n";
echo "   Role-Permission assignments: {$rolePermissionCount}\n";

echo "\n=== RBAC System Test Complete ===\n";
