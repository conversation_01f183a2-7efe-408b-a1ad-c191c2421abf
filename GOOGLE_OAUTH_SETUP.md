# Google OAuth Setup Guide

## Overview
This guide explains how to set up Google OAuth authentication for the Escape Matrix Academy Laravel application.

## Prerequisites
- Laravel Socialite package (already installed)
- Google Cloud Console account
- Domain configured for your application

## Google Cloud Console Setup

### 1. Create a Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API and Google OAuth2 API

### 2. Configure OAuth Consent Screen
1. Navigate to "APIs & Services" > "OAuth consent screen"
2. Choose "External" user type (unless you have a Google Workspace)
3. Fill in the required information:
   - **App name**: Escape Matrix Academy
   - **User support email**: Your support email
   - **Developer contact information**: Your contact email
4. Add scopes:
   - `../auth/userinfo.email`
   - `../auth/userinfo.profile`
5. Add test users if in development mode

### 3. Create OAuth 2.0 Credentials
1. Navigate to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Choose "Web application" as application type
4. Configure:
   - **Name**: Escape Matrix Academy Web Client
   - **Authorized JavaScript origins**: 
     - `http://localhost:8000` (development)
     - `https://yourdomain.com` (production)
   - **Authorized redirect URIs**:
     - `http://localhost:8000/auth/google/callback` (development)
     - `https://yourdomain.com/auth/google/callback` (production)

### 4. Get Your Credentials
1. Copy the **Client ID** and **Client Secret**
2. Update your `.env` file with these values

## Environment Configuration

Update your `.env` file with the Google OAuth credentials:

```env
# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_REDIRECT_URI="${APP_URL}/auth/google/callback"
```

**Important**: Replace `your_google_client_id_here` and `your_google_client_secret_here` with your actual Google OAuth credentials.

## Security Features Implemented

### 1. Rate Limiting
- OAuth routes are rate-limited to prevent abuse
- 10 requests per minute for OAuth flow
- 5 requests per minute for profile linking/unlinking

### 2. Data Validation
- Google user data is validated before processing
- Email format validation
- Required fields validation (Google ID, email)

### 3. Account Security
- Prevents account takeover by validating existing accounts
- Secure account linking with duplicate checks
- Proper error handling and logging

### 4. Session Management
- Intended URL storage for post-login redirects
- Secure session handling
- Proper logout functionality

## User Experience Features

### 1. Seamless Integration
- Google OAuth buttons on login and register pages
- Consistent styling with existing design
- Clear visual separation between OAuth and email/password options

### 2. Account Management
- Users can link/unlink Google accounts from their profile
- Support for both Google-only and hybrid accounts
- Avatar integration from Google profiles

### 3. Role-Based Access
- Maintains existing RBAC system
- New Google users get default "student" role
- Existing users retain their roles when linking Google accounts

## Testing

### Development Testing
1. Ensure your local development server is running on `http://localhost:8000`
2. Test the OAuth flow:
   - Visit `/login` and click "Continue with Google"
   - Complete the Google OAuth flow
   - Verify successful login and proper role assignment

### Production Deployment
1. Update OAuth redirect URIs in Google Cloud Console
2. Update `APP_URL` in production `.env`
3. Test OAuth flow in production environment

## Troubleshooting

### Common Issues

1. **"redirect_uri_mismatch" error**
   - Ensure redirect URI in Google Console matches exactly
   - Check for trailing slashes and protocol (http vs https)

2. **"invalid_client" error**
   - Verify Client ID and Client Secret are correct
   - Check that OAuth consent screen is properly configured

3. **"access_denied" error**
   - User cancelled the OAuth flow
   - Check OAuth consent screen configuration

### Logs
- OAuth events are logged in `storage/logs/laravel.log`
- Check logs for detailed error information

## Security Considerations

1. **Never commit OAuth credentials to version control**
2. **Use HTTPS in production**
3. **Regularly rotate OAuth credentials**
4. **Monitor OAuth usage for suspicious activity**
5. **Keep Laravel Socialite package updated**

## Support
For issues with Google OAuth integration, check:
1. Laravel logs (`storage/logs/laravel.log`)
2. Google Cloud Console error messages
3. Browser developer tools for client-side errors
