<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Lecture;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;

class FileController extends Controller
{
    /**
     * Serve course images.
     */
    public function serveCourseImage(Course $course, string $filename)
    {
        // Check if user can view the course or if it's published
        if (!$course->isPublished() && !auth()->user()?->can('view', $course)) {
            abort(404);
        }

        $path = "courses/{$course->instructor_id}/{$course->id}/images/{$filename}";
        
        return $this->serveFile($path);
    }

    /**
     * Serve lecture resources.
     */
    public function serveLectureResource(Course $course, Lecture $lecture, string $filename)
    {
        // Check if user can access the lecture
        if (!$this->canAccessLecture($course, $lecture)) {
            abort(403, 'Access denied to this resource.');
        }

        $path = "private/courses/{$course->instructor_id}/{$course->id}/materials/resources/{$filename}";
        
        return $this->serveFile($path);
    }

    /**
     * Serve instructor files (for instructors only).
     */
    public function serveInstructorFile(string $userId, string $courseId, string $type, string $filename)
    {
        // Only allow instructors to access their own files or admins
        if (!auth()->user()?->isInstructor() && !auth()->user()?->hasAdminPrivileges()) {
            abort(403);
        }

        // If instructor, ensure they own the course
        if (auth()->user()->isInstructor() && auth()->id() !== $userId) {
            abort(403);
        }

        $path = "private/courses/{$userId}/{$courseId}/materials/{$type}/{$filename}";
        
        return $this->serveFile($path);
    }

    /**
     * Serve temporary files (for course wizard).
     */
    public function serveTempFile(string $userId, string $filename)
    {
        // Only allow users to access their own temp files
        if (auth()->id() !== $userId && !auth()->user()?->hasAdminPrivileges()) {
            abort(403);
        }

        $path = "temp/course-images/{$userId}/{$filename}";
        
        return $this->serveFile($path);
    }

    /**
     * Serve a file from private storage.
     */
    private function serveFile(string $path): StreamedResponse
    {
        if (!Storage::disk('private')->exists($path)) {
            abort(404);
        }

        $mimeType = Storage::disk('private')->mimeType($path);
        $size = Storage::disk('private')->size($path);
        $lastModified = Storage::disk('private')->lastModified($path);

        $headers = [
            'Content-Type' => $mimeType,
            'Content-Length' => $size,
            'Last-Modified' => gmdate('D, d M Y H:i:s', $lastModified) . ' GMT',
            'Cache-Control' => 'public, max-age=3600', // Cache for 1 hour
        ];

        // Handle range requests for video files
        if (str_starts_with($mimeType, 'video/')) {
            return $this->serveVideoFile($path, $headers);
        }

        return Storage::disk('private')->response($path, null, $headers);
    }

    /**
     * Serve video files with range support.
     */
    private function serveVideoFile(string $path, array $headers): StreamedResponse
    {
        $fullPath = Storage::disk('private')->path($path);
        $size = filesize($fullPath);
        $start = 0;
        $end = $size - 1;

        // Handle range requests
        if (request()->hasHeader('Range')) {
            $range = request()->header('Range');
            if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
                $start = intval($matches[1]);
                if (!empty($matches[2])) {
                    $end = intval($matches[2]);
                }
            }
        }

        $length = $end - $start + 1;

        $headers = array_merge($headers, [
            'Accept-Ranges' => 'bytes',
            'Content-Range' => "bytes {$start}-{$end}/{$size}",
            'Content-Length' => $length,
        ]);

        $response = new StreamedResponse(function () use ($fullPath, $start, $length) {
            $file = fopen($fullPath, 'rb');
            fseek($file, $start);
            
            $buffer = 8192; // 8KB buffer
            $bytesRemaining = $length;
            
            while ($bytesRemaining > 0 && !feof($file)) {
                $bytesToRead = min($buffer, $bytesRemaining);
                echo fread($file, $bytesToRead);
                $bytesRemaining -= $bytesToRead;
                
                if (ob_get_level()) {
                    ob_flush();
                }
                flush();
            }
            
            fclose($file);
        }, request()->hasHeader('Range') ? 206 : 200, $headers);

        return $response;
    }

    /**
     * Check if user can access a lecture.
     */
    private function canAccessLecture(Course $course, Lecture $lecture): bool
    {
        $user = auth()->user();

        // Admins and course instructors can always access
        if ($user?->hasAdminPrivileges() || $user?->can('view', $course)) {
            return true;
        }

        // If course is not published, deny access
        if (!$course->isPublished()) {
            return false;
        }

        // If lecture is free preview, allow access
        if ($lecture->is_free_preview) {
            return true;
        }

        // If user is not authenticated, deny access
        if (!$user) {
            return false;
        }

        // Check if user is enrolled in the course
        $enrollment = $course->enrollments()
            ->where('user_id', $user->id)
            ->where('status', 'active')
            ->first();

        return $enrollment !== null;
    }

    /**
     * Download a lecture resource.
     */
    public function downloadLectureResource(Course $course, Lecture $lecture, string $filename)
    {
        // Check if user can access the lecture
        if (!$this->canAccessLecture($course, $lecture)) {
            abort(403, 'Access denied to this resource.');
        }

        $path = "private/courses/{$course->instructor_id}/{$course->id}/materials/resources/{$filename}";
        
        if (!Storage::disk('private')->exists($path)) {
            abort(404);
        }

        // Find the resource in lecture data
        $resource = collect($lecture->resources ?? [])->firstWhere('file_path', $path);
        $downloadName = $resource['name'] ?? $filename;

        return Storage::disk('private')->download($path, $downloadName);
    }

    /**
     * Get file info (for AJAX requests).
     */
    public function getFileInfo(string $path)
    {
        if (!Storage::disk('private')->exists($path)) {
            return response()->json(['error' => 'File not found'], 404);
        }

        return response()->json([
            'exists' => true,
            'size' => Storage::disk('private')->size($path),
            'mime_type' => Storage::disk('private')->mimeType($path),
            'last_modified' => Storage::disk('private')->lastModified($path),
        ]);
    }

    /**
     * Securely serve a resource file for preview (instructor only).
     */
    public function secureResourceView(Request $request)
    {
        $filePath = $request->query('path');
        if (!$filePath || !Storage::disk('private')->exists($filePath)) {
            abort(404);
        }
        // Only allow instructors or admins to view
        if (!auth()->user()?->isInstructor() && !auth()->user()?->hasAdminPrivileges()) {
            abort(403);
        }
        // If instructor, ensure they own the file
        $segments = explode('/', $filePath);
        if (auth()->user()->isInstructor() && isset($segments[1]) && auth()->id() != $segments[1]) {
            abort(403);
        }
        return Storage::disk('private')->response($filePath);
    }

    /**
     * Securely download a resource file (instructor only).
     */
    public function secureResourceDownload(Request $request)
    {
        $filePath = $request->query('path');
        if (!$filePath || !Storage::disk('private')->exists($filePath)) {
            abort(404);
        }
        // Only allow instructors or admins to download
        if (!auth()->user()?->isInstructor() && !auth()->user()?->hasAdminPrivileges()) {
            abort(403);
        }
        // If instructor, ensure they own the file
        $segments = explode('/', $filePath);
        if (auth()->user()->isInstructor() && isset($segments[1]) && auth()->id() != $segments[1]) {
            abort(403);
        }
        $downloadName = basename($filePath);
        return Storage::disk('private')->download($filePath, $downloadName);
    }
}
