<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Payment;
use App\Models\User;
use App\Services\PayPalService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Exception;

class PayPalTestController extends Controller
{
    private PayPalService $paypalService;

    public function __construct(PayPalService $paypalService)
    {
        $this->paypalService = $paypalService;
        
        // Only allow in sandbox mode
        if (config('services.paypal.mode') !== 'sandbox') {
            abort(404, 'Test features only available in sandbox mode');
        }
    }

    /**
     * Show PayPal testing dashboard
     */
    public function dashboard()
    {
        $stats = [
            'total_payments' => Payment::count(),
            'completed_payments' => Payment::where('status', 'completed')->count(),
            'pending_payments' => Payment::where('status', 'pending')->count(),
            'failed_payments' => Payment::where('status', 'failed')->count(),
            'total_revenue' => Payment::where('status', 'completed')->sum('amount'),
        ];

        $recentPayments = Payment::with(['user', 'course'])
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        $testCourses = Course::where('status', 'published')
            ->where('price', '>', 0)
            ->take(5)
            ->get();

        return view('paypal.test.dashboard', compact('stats', 'recentPayments', 'testCourses'));
    }

    /**
     * Create test payment for debugging
     */
    public function createTestPayment(Request $request)
    {
        try {
            $request->validate([
                'course_id' => 'required|exists:courses,id',
                'amount' => 'nullable|numeric|min:0.01|max:10000'
            ]);

            $course = Course::findOrFail($request->course_id);
            $user = Auth::user();

            // Override course price if amount is provided (for testing)
            if ($request->filled('amount')) {
                $originalPrice = $course->price;
                $course->price = $request->amount;
                
                Log::info('PayPal test payment with custom amount', [
                    'course_id' => $course->id,
                    'original_price' => $originalPrice,
                    'test_amount' => $request->amount,
                    'user_id' => $user->id
                ]);
            }

            $result = $this->paypalService->createOrder($course, $user);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'payment_id' => $result['payment']->id,
                    'paypal_order_id' => $result['paypal_order_id'],
                    'approval_url' => $result['approval_url'],
                    'message' => 'Test payment created successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => $result['error']
                ], 400);
            }

        } catch (Exception $e) {
            Log::error('PayPal test payment creation failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Test payment creation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Simulate webhook events for testing
     */
    public function simulateWebhook(Request $request)
    {
        try {
            $request->validate([
                'event_type' => 'required|in:PAYMENT.CAPTURE.COMPLETED,PAYMENT.CAPTURE.DENIED,PAYMENT.CAPTURE.DECLINED',
                'payment_id' => 'required|exists:payments,id'
            ]);

            $payment = Payment::findOrFail($request->payment_id);
            
            // Create mock webhook data
            $webhookData = [
                'id' => 'WH-' . uniqid(),
                'event_version' => '1.0',
                'create_time' => now()->toISOString(),
                'resource_type' => 'capture',
                'event_type' => $request->event_type,
                'summary' => 'Payment capture ' . strtolower(str_replace('PAYMENT.CAPTURE.', '', $request->event_type)),
                'resource' => [
                    'id' => 'CAPTURE-' . uniqid(),
                    'status' => $request->event_type === 'PAYMENT.CAPTURE.COMPLETED' ? 'COMPLETED' : 'DECLINED',
                    'amount' => [
                        'currency_code' => $payment->currency,
                        'value' => number_format($payment->amount, 2, '.', '')
                    ],
                    'custom_id' => $payment->id,
                    'invoice_id' => 'INV-' . $payment->id,
                    'create_time' => now()->toISOString(),
                    'update_time' => now()->toISOString()
                ]
            ];

            $result = $this->paypalService->processWebhookEvent($webhookData);

            Log::info('PayPal webhook simulation completed', [
                'payment_id' => $payment->id,
                'event_type' => $request->event_type,
                'result' => $result
            ]);

            return response()->json([
                'success' => true,
                'webhook_data' => $webhookData,
                'processing_result' => $result,
                'message' => 'Webhook simulation completed'
            ]);

        } catch (Exception $e) {
            Log::error('PayPal webhook simulation failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Webhook simulation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get PayPal sandbox credentials and configuration
     */
    public function getConfiguration()
    {
        $config = [
            'mode' => config('services.paypal.mode'),
            'client_id' => config('services.paypal.client_id'),
            'currency' => config('services.paypal.currency'),
            'platform_fee_percentage' => config('services.paypal.platform_fee_percentage'),
            'webhook_configured' => !empty(config('services.paypal.webhook_id')),
        ];

        // Don't expose client secret
        $config['client_secret_configured'] = !empty(config('services.paypal.client_secret'));

        return response()->json($config);
    }

    /**
     * Test PayPal API connectivity
     */
    public function testConnection()
    {
        try {
            // Try to get an access token
            $reflection = new \ReflectionClass($this->paypalService);
            $method = $reflection->getMethod('getAccessToken');
            $method->setAccessible(true);
            
            $token = $method->invoke($this->paypalService);
            
            if ($token) {
                return response()->json([
                    'success' => true,
                    'message' => 'PayPal API connection successful',
                    'token_preview' => substr($token, 0, 20) . '...',
                    'timestamp' => now()->toISOString()
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to obtain access token'
                ], 400);
            }

        } catch (Exception $e) {
            Log::error('PayPal connection test failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Connection test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear test data (payments, enrollments)
     */
    public function clearTestData(Request $request)
    {
        try {
            $request->validate([
                'confirm' => 'required|in:yes'
            ]);

            $deletedPayments = Payment::where('payment_method', 'paypal')->count();
            
            // Delete PayPal payments and related enrollments
            Payment::where('payment_method', 'paypal')->delete();
            
            // Clear webhook cache
            cache()->flush();

            Log::warning('PayPal test data cleared', [
                'deleted_payments' => $deletedPayments,
                'cleared_by' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'message' => "Cleared {$deletedPayments} test payments and related data",
                'timestamp' => now()->toISOString()
            ]);

        } catch (Exception $e) {
            Log::error('PayPal test data clearing failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to clear test data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate test webhook payload
     */
    public function generateWebhookPayload(Request $request)
    {
        $request->validate([
            'event_type' => 'required|string',
            'payment_id' => 'nullable|exists:payments,id'
        ]);

        $payment = $request->payment_id ? Payment::find($request->payment_id) : null;

        $payload = [
            'id' => 'WH-' . uniqid(),
            'event_version' => '1.0',
            'create_time' => now()->toISOString(),
            'resource_type' => 'capture',
            'event_type' => $request->event_type,
            'summary' => 'Test webhook event',
            'resource' => [
                'id' => 'CAPTURE-' . uniqid(),
                'status' => 'COMPLETED',
                'amount' => [
                    'currency_code' => $payment ? $payment->currency : 'USD',
                    'value' => $payment ? number_format($payment->amount, 2, '.', '') : '10.00'
                ],
                'custom_id' => $payment ? $payment->id : 'test-payment-id',
                'create_time' => now()->toISOString(),
                'update_time' => now()->toISOString()
            ]
        ];

        return response()->json([
            'payload' => $payload,
            'headers' => [
                'paypal-auth-algo' => 'SHA256withRSA',
                'paypal-transmission-id' => 'TEST-' . uniqid(),
                'paypal-cert-id' => 'TEST-CERT-ID',
                'paypal-transmission-sig' => 'TEST-SIGNATURE-' . uniqid(),
                'paypal-transmission-time' => time()
            ]
        ]);
    }
}
