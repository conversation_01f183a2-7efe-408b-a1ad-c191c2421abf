<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayPal Debug Dashboard</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .json-display {
            background: #1f2937;
            color: #f3f4f6;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            word-break: break-all;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 mb-8">PayPal Debug Dashboard</h1>
            
            <!-- Configuration Info -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">PayPal Configuration</h2>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div>
                        <span class="font-medium">Mode:</span>
                        <span class="ml-2 px-2 py-1 rounded text-sm {{ $config['mode'] === 'sandbox' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800' }}">
                            {{ strtoupper($config['mode']) }}
                        </span>
                    </div>
                    <div>
                        <span class="font-medium">Base URL:</span>
                        <span class="ml-2 text-sm text-gray-600">{{ $config['base_url'] }}</span>
                    </div>
                    <div>
                        <span class="font-medium">Client ID:</span>
                        <span class="ml-2 text-sm {{ $config['client_id_configured'] ? 'text-green-600' : 'text-red-600' }}">
                            {{ $config['client_id_configured'] ? 'Configured' : 'Missing' }}
                        </span>
                    </div>
                    <div>
                        <span class="font-medium">Client Secret:</span>
                        <span class="ml-2 text-sm {{ $config['client_secret_configured'] ? 'text-green-600' : 'text-red-600' }}">
                            {{ $config['client_secret_configured'] ? 'Configured' : 'Missing' }}
                        </span>
                    </div>
                    <div>
                        <span class="font-medium">Currency:</span>
                        <span class="ml-2 text-sm text-gray-600">{{ $config['currency'] }}</span>
                    </div>
                    <div>
                        <span class="font-medium">Platform Fee:</span>
                        <span class="ml-2 text-sm text-gray-600">{{ $config['platform_fee_percentage'] }}%</span>
                    </div>
                </div>
            </div>

            <!-- Test Controls -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Debug Tests</h2>
                
                <div class="space-y-4">
                    <!-- Create Test Order -->
                    <div>
                        <button id="createTestOrder" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                            Create Test Order
                        </button>
                        <span class="ml-4 text-sm text-gray-600">Creates a test order for debugging capture requests</span>
                    </div>

                    <!-- Debug Capture -->
                    <div class="flex items-center space-x-4">
                        <input type="text" id="orderIdInput" placeholder="PayPal Order ID" 
                               class="border border-gray-300 rounded px-3 py-2 w-64">
                        <button id="debugCapture" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                            Debug Capture Request
                        </button>
                        <button id="testCurrentImpl" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                            Test Current Implementation
                        </button>
                    </div>

                    <!-- Log Controls -->
                    <div class="flex items-center space-x-4">
                        <button id="showLogs" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">
                            Show Recent Logs
                        </button>
                        <button id="clearLogs" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded">
                            Clear Logs
                        </button>
                    </div>
                </div>
            </div>

            <!-- Results Display -->
            <div id="resultsContainer" class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Results</h2>
                <div id="resultsContent" class="text-gray-600">
                    Click a button above to run tests and see results here.
                </div>
            </div>
        </div>
    </div>

    <script>
        // Set up CSRF token for AJAX requests
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        // Helper function to display results
        function displayResults(title, data) {
            const container = document.getElementById('resultsContent');
            container.innerHTML = `
                <h3 class="text-lg font-semibold mb-3">${title}</h3>
                <div class="json-display p-4 rounded text-sm overflow-auto max-h-96">
${JSON.stringify(data, null, 2)}
                </div>
            `;
        }

        // Helper function to show loading
        function showLoading(message) {
            const container = document.getElementById('resultsContent');
            container.innerHTML = `
                <div class="flex items-center">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mr-3"></div>
                    <span>${message}</span>
                </div>
            `;
        }

        // Create Test Order
        document.getElementById('createTestOrder').addEventListener('click', function() {
            showLoading('Creating test order...');
            
            fetch('/paypal/debug/create-test-order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                displayResults('Test Order Created', data);
                if (data.success && data.order_id) {
                    document.getElementById('orderIdInput').value = data.order_id;
                }
            })
            .catch(error => {
                displayResults('Error Creating Test Order', { error: error.message });
            });
        });

        // Debug Capture
        document.getElementById('debugCapture').addEventListener('click', function() {
            const orderId = document.getElementById('orderIdInput').value;
            if (!orderId) {
                alert('Please enter a PayPal Order ID');
                return;
            }

            showLoading('Running capture debug tests...');
            
            fetch(`/paypal/debug/capture?order_id=${encodeURIComponent(orderId)}`, {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                displayResults('Capture Debug Results', data);
            })
            .catch(error => {
                displayResults('Error Running Debug Tests', { error: error.message });
            });
        });

        // Test Current Implementation
        document.getElementById('testCurrentImpl').addEventListener('click', function() {
            const orderId = document.getElementById('orderIdInput').value;
            if (!orderId) {
                alert('Please enter a PayPal Order ID');
                return;
            }

            showLoading('Testing current implementation...');
            
            fetch(`/paypal/debug/test-current?order_id=${encodeURIComponent(orderId)}`, {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                displayResults('Current Implementation Test', data);
            })
            .catch(error => {
                displayResults('Error Testing Current Implementation', { error: error.message });
            });
        });

        // Show Logs
        document.getElementById('showLogs').addEventListener('click', function() {
            showLoading('Loading recent logs...');
            
            fetch('/paypal/debug/logs', {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                displayResults('Recent PayPal Logs', data);
            })
            .catch(error => {
                displayResults('Error Loading Logs', { error: error.message });
            });
        });

        // Clear Logs
        document.getElementById('clearLogs').addEventListener('click', function() {
            if (!confirm('Are you sure you want to clear the logs? A backup will be created.')) {
                return;
            }

            showLoading('Clearing logs...');
            
            fetch('/paypal/debug/clear-logs', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                displayResults('Logs Cleared', data);
            })
            .catch(error => {
                displayResults('Error Clearing Logs', { error: error.message });
            });
        });
    </script>
</body>
</html>
