<?php

namespace App\Http\Middleware;

use App\Services\CourseMaterialAccessService;
use App\Services\PrivateStorageService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class SecureFileAccess
{
    protected PrivateStorageService $storageService;
    protected CourseMaterialAccessService $accessService;

    public function __construct(
        PrivateStorageService $storageService,
        CourseMaterialAccessService $accessService
    ) {
        $this->storageService = $storageService;
        $this->accessService = $accessService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            abort(401, 'Authentication required');
        }

        $user = Auth::user();
        $filePath = $request->route('filePath');

        // Check if file exists first
        if (!$this->storageService->fileExists($filePath)) {
            abort(404, 'File not found');
        }

        // Special handling for course images
        if ($this->isCourseImage($filePath)) {
            if (!$this->canAccessCourseImage($filePath, $user)) {
                abort(403, 'Access denied to this course image');
            }
        } elseif ($this->isCourseMaterial($filePath)) {
            // Enhanced course material access control
            if (!$this->accessService->canAccessFile($user, $filePath)) {
                abort(403, 'Access denied. You must be enrolled and have valid payment to access this course material.');
            }
        } else {
            // Standard file access verification for other files
            if (!$this->storageService->verifyFileAccess($filePath, $user->id)) {
                abort(403, 'Access denied to this file');
            }
        }

        return $next($request);
    }

    /**
     * Check if the file path is a course image.
     */
    private function isCourseImage(string $filePath): bool
    {
        return str_contains($filePath, '/course-images/');
    }

    /**
     * Check if the file path is a course material.
     */
    private function isCourseMaterial(string $filePath): bool
    {
        return str_contains($filePath, '/materials/') && str_contains($filePath, 'private/');
    }

    /**
     * Check if the user can access a course image.
     */
    private function canAccessCourseImage(string $filePath, $user): bool
    {
        // Extract user ID and course ID from the file path
        // Format: private/{userId}/{courseId}/course-images/{filename}
        $pathParts = explode('/', $filePath);

        if (count($pathParts) < 4) {
            return false;
        }

        $fileUserId = $pathParts[1];
        $courseId = $pathParts[2];

        // Admins and superadmins can access all course images
        if ($user->isAdmin() || $user->isSuperAdmin()) {
            return true;
        }

        // Course owners (instructors) can always access their course images
        if ($fileUserId === (string) $user->id) {
            return true;
        }

        // For other users, check if the course is published and they have access
        $course = \App\Models\Course::find($courseId);

        if (!$course) {
            return false;
        }

        // Published courses are accessible to all authenticated users
        if ($course->status === 'published') {
            return true;
        }

        // Archived courses are accessible to enrolled students
        if ($course->status === 'archived') {
            return $user->enrollments()->where('course_id', $courseId)->exists();
        }

        // Draft courses are only accessible to the instructor
        return false;
    }
}
