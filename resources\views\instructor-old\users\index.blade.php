@extends('instructor.layouts.app')

@section('title', 'Student Management - Instructor Dashboard')

@section('content')
<div class="py-12">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">Student Management</h1>
                <p class="text-gray-400">Manage students enrolled in your courses</p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="{{ route('instructor.users.export', request()->query()) }}" 
                   class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-download mr-2"></i>Export CSV
                </a>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-600 rounded-lg">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Total Students</p>
                        <p class="text-2xl font-bold text-white">{{ $stats['total_students'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-green-600 rounded-lg">
                        <i class="fas fa-user-check text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Active Enrollments</p>
                        <p class="text-2xl font-bold text-white">{{ $stats['active_enrollments'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-purple-600 rounded-lg">
                        <i class="fas fa-graduation-cap text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Completed</p>
                        <p class="text-2xl font-bold text-white">{{ $stats['completed_enrollments'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-yellow-600 rounded-lg">
                        <i class="fas fa-book-open text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Total Enrollments</p>
                        <p class="text-2xl font-bold text-white">{{ $stats['total_enrollments'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-red-600 rounded-lg">
                        <i class="fas fa-calendar-plus text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">New This Month</p>
                        <p class="text-2xl font-bold text-white">{{ $stats['new_this_month'] }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-8">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Search</label>
                    <input type="text" name="search" value="{{ request('search') }}" 
                           placeholder="Name or email..." 
                           class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                    <select name="status" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500">
                        <option value="">All Statuses</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Completed</option>
                        <option value="suspended" {{ request('status') === 'suspended' ? 'selected' : '' }}>Suspended</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Course</label>
                    <select name="course_id" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500">
                        <option value="">All Courses</option>
                        @foreach($courses as $course)
                            <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>
                                {{ $course->title }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">From Date</label>
                    <input type="date" name="date_from" value="{{ request('date_from') }}" 
                           class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500">
                </div>

                <div class="flex items-end">
                    <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-search mr-2"></i>Filter
                    </button>
                </div>
            </form>
        </div>

        <!-- Students Table -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-800">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Student</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Courses</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Progress</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Last Activity</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Enrolled</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-800">
                        @forelse($users as $user)
                            <tr class="hover:bg-gray-800">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            @if($user->avatar)
                                                <img class="h-10 w-10 rounded-full object-cover" src="{{ Storage::url($user->avatar) }}" alt="{{ $user->name }}">
                                            @else
                                                <div class="h-10 w-10 rounded-full bg-red-600 flex items-center justify-center">
                                                    <span class="text-white font-medium">{{ substr($user->name, 0, 1) }}</span>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-white">{{ $user->name }}</div>
                                            <div class="text-sm text-gray-400">{{ $user->email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-white">
                                        {{ $user->enrollments->count() }} course(s)
                                    </div>
                                    <div class="text-xs text-gray-400">
                                        {{ $user->enrollments->pluck('course.title')->take(2)->implode(', ') }}
                                        @if($user->enrollments->count() > 2)
                                            <span class="text-gray-500">+{{ $user->enrollments->count() - 2 }} more</span>
                                        @endif
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @php
                                        $avgProgress = $user->enrollments->avg('progress') ?? 0;
                                    @endphp
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-700 rounded-full h-2 mr-2">
                                            <div class="bg-red-600 h-2 rounded-full" style="width: {{ $avgProgress }}%"></div>
                                        </div>
                                        <span class="text-sm text-white">{{ round($avgProgress) }}%</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @php
                                        $statuses = $user->enrollments->pluck('status')->unique();
                                        $primaryStatus = $statuses->first();
                                    @endphp
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                        @if($primaryStatus === 'active') bg-green-100 text-green-800
                                        @elseif($primaryStatus === 'completed') bg-blue-100 text-blue-800
                                        @elseif($primaryStatus === 'suspended') bg-red-100 text-red-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ ucfirst($primaryStatus) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                                    @php
                                        $lastActivity = $user->enrollments->max('last_activity_at');
                                    @endphp
                                    {{ $lastActivity ? $lastActivity->diffForHumans() : 'Never' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                                    {{ $user->enrollments->min('enrolled_at')?->format('M j, Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{{ route('instructor.users.show', $user) }}" 
                                       class="text-red-500 hover:text-red-400 mr-3">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="px-6 py-12 text-center">
                                    <div class="text-gray-400">
                                        <i class="fas fa-users text-4xl mb-4"></i>
                                        <p class="text-lg">No students found</p>
                                        <p class="text-sm">Students will appear here when they enroll in your courses.</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($users->hasPages())
                <div class="px-6 py-4 border-t border-gray-800">
                    {{ $users->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
