<?php

namespace Tests\Feature;

use App\Models\Course;
use App\Models\User;
use App\Models\LearningMaterial;
use App\Models\ContentFile;
use App\Services\PrivateStorageService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class PrivateStorageSystemTest extends TestCase
{
    use RefreshDatabase;

    protected $instructor;
    protected $otherInstructor;
    protected $course;
    protected $storageService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->instructor = User::factory()->create([
            'role' => 'instructor'
        ]);
        
        $this->otherInstructor = User::factory()->create([
            'role' => 'instructor'
        ]);
        
        // Create test course
        $this->course = Course::factory()->create([
            'instructor_id' => $this->instructor->id
        ]);
        
        $this->storageService = app(PrivateStorageService::class);
        
        // Setup storage disks for testing
        Storage::fake('private');
        Storage::fake('public');
    }

    /** @test */
    public function it_creates_course_directories_automatically_when_course_is_created()
    {
        // Test the service method directly
        $this->storageService->createCourseDirectories((string) $this->instructor->id, (string) $this->course->id);

        // Verify directories were created
        $materialTypes = ['learning-materials', 'ebooks', 'videos', 'resources', 'content-files'];

        foreach ($materialTypes as $type) {
            $directory = "private/{$this->instructor->id}/{$this->course->id}/materials/{$type}";
            $this->assertTrue(Storage::disk('private')->exists($directory));
        }
    }

    /** @test */
    public function it_stores_files_in_private_user_specific_directories()
    {
        $file = UploadedFile::fake()->create('test-document.pdf', 1000);
        
        $fileData = $this->storageService->storeFile(
            $file,
            (string) $this->instructor->id,
            (string) $this->course->id,
            'learning-materials'
        );
        
        // Verify file was stored in correct location
        $expectedPath = "private/{$this->instructor->id}/{$this->course->id}/materials/learning-materials";
        $this->assertStringContainsString($expectedPath, $fileData['path']);
        
        // Verify file exists
        $this->assertTrue(Storage::disk('private')->exists($fileData['path']));
        
        // Verify file data
        $this->assertEquals('test-document.pdf', $fileData['original_name']);
        $this->assertEquals(1000, $fileData['size']);
        $this->assertEquals('application/pdf', $fileData['mime_type']);
    }

    /** @test */
    public function it_prevents_unauthorized_file_access()
    {
        // Create a file owned by instructor
        $file = UploadedFile::fake()->create('private-document.pdf', 1000);
        
        $fileData = $this->storageService->storeFile(
            $file,
            (string) $this->instructor->id,
            (string) $this->course->id,
            'learning-materials'
        );
        
        // Try to access file as other instructor
        $this->actingAs($this->otherInstructor);
        
        $response = $this->get(route('secure.files.serve', ['filePath' => $fileData['path']]));
        $response->assertStatus(403);
    }

    /** @test */
    public function it_allows_authorized_file_access()
    {
        // Create a file owned by instructor
        $file = UploadedFile::fake()->create('authorized-document.pdf', 1000);
        
        $fileData = $this->storageService->storeFile(
            $file,
            (string) $this->instructor->id,
            (string) $this->course->id,
            'learning-materials'
        );
        
        // Access file as owner
        $this->actingAs($this->instructor);
        
        $response = $this->get(route('secure.files.serve', ['filePath' => $fileData['path']]));
        $response->assertStatus(200);
    }

    /** @test */
    public function it_creates_learning_materials_with_private_storage()
    {
        $this->actingAs($this->instructor);
        
        $file = UploadedFile::fake()->create('learning-material.pdf', 2000);
        
        $materialData = [
            'title' => 'Test Learning Material',
            'description' => 'Test Description',
            'content' => 'Test Content',
            'type' => 'document',
            'course_id' => $this->course->id,
            'is_published' => true,
            'file' => $file
        ];
        
        $response = $this->post(route('instructor.learning-materials.store'), $materialData);
        
        $material = LearningMaterial::where('title', 'Test Learning Material')->first();
        $this->assertNotNull($material);
        
        // Verify file is stored in private storage
        $expectedPath = "private/{$this->instructor->id}/{$this->course->id}/materials/learning-materials";
        $this->assertStringContainsString($expectedPath, $material->file_path);

        // Verify secure URLs are generated
        $this->assertStringContainsString('secure/files', $material->file_url);
        $this->assertStringContainsString('secure/download', $material->download_url);
    }

    /** @test */
    public function it_creates_content_files_with_private_storage()
    {
        $this->actingAs($this->instructor);
        
        $file = UploadedFile::fake()->create('test-image.jpg', 1000);
        
        $fileData = [
            'title' => 'Test Content File',
            'description' => 'Test Description',
            'category' => 'image',
            'course_id' => $this->course->id,
            'is_public' => false,
            'file' => $file
        ];
        
        $response = $this->post(route('instructor.content-files.store'), $fileData);
        
        $contentFile = ContentFile::where('title', 'Test Content File')->first();
        $this->assertNotNull($contentFile);
        
        // Verify file is stored in private storage
        $expectedPath = "private/{$this->instructor->id}/{$this->course->id}/materials/content-files";
        $this->assertStringContainsString($expectedPath, $contentFile->file_path);

        // Verify secure URLs are generated
        $this->assertStringContainsString('secure/files', $contentFile->file_url);
        $this->assertStringContainsString('secure/download', $contentFile->download_url);
    }

    /** @test */
    public function it_verifies_file_access_permissions()
    {
        $instructorFilePath = "private/{$this->instructor->id}/123/materials/test.pdf";
        $otherInstructorFilePath = "private/{$this->otherInstructor->id}/456/materials/test.pdf";
        
        // Instructor can access their own files
        $this->assertTrue(
            $this->storageService->verifyFileAccess($instructorFilePath, (string) $this->instructor->id)
        );
        
        // Instructor cannot access other instructor's files
        $this->assertFalse(
            $this->storageService->verifyFileAccess($otherInstructorFilePath, (string) $this->instructor->id)
        );
    }

    /** @test */
    public function it_deletes_files_properly()
    {
        $file = UploadedFile::fake()->create('delete-test.pdf', 1000);
        
        $fileData = $this->storageService->storeFile(
            $file,
            (string) $this->instructor->id,
            (string) $this->course->id,
            'learning-materials'
        );
        
        // Verify file exists
        $this->assertTrue($this->storageService->fileExists($fileData['path']));
        
        // Delete file
        $deleted = $this->storageService->deleteFile($fileData['path']);
        $this->assertTrue($deleted);
        
        // Verify file no longer exists
        $this->assertFalse($this->storageService->fileExists($fileData['path']));
    }

    /** @test */
    public function it_maintains_backward_compatibility_for_existing_functionality()
    {
        $this->actingAs($this->instructor);
        
        // Test course creation still works
        $response = $this->get(route('instructor.courses.create'));
        $response->assertStatus(200);
        
        // Test course listing still works
        $response = $this->get(route('instructor.courses.index'));
        $response->assertStatus(200);
        
        // Test learning materials listing still works
        $response = $this->get(route('instructor.learning-materials.index'));
        $response->assertStatus(200);
        
        // Test content files listing still works
        $response = $this->get(route('instructor.content-files.index'));
        $response->assertStatus(200);
    }

    protected function tearDown(): void
    {
        Storage::fake('private');
        Storage::fake('public');
        parent::tearDown();
    }
}
