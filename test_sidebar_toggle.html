<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Course Viewer Sidebar Toggle Test</title>
    <link rel="stylesheet" href="public/css/course-viewer.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Additional test styles */
        body {
            margin: 0;
            padding: 0;
        }
        .test-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #1f2937;
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <div>Screen: <span id="screen-size"></span></div>
        <div>Sidebar: <span id="sidebar-state">Visible</span></div>
    </div>

    <!-- Course Viewer Container -->
    <div class="course-viewer">
        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay"></div>

        <!-- Course Viewer Layout -->
        <div class="course-viewer-container">
            <!-- Course Sidebar -->
            <aside class="course-sidebar" role="complementary" aria-label="Course Navigation">
                <!-- Sidebar Header -->
                <div class="sidebar-header">
                    <button class="sidebar-close-btn" aria-label="Close sidebar">
                        <i class="fas fa-times"></i>
                    </button>

                    <h1 class="course-title">Test Course Title</h1>
                    
                    <!-- Course Progress -->
                    <div class="course-progress">
                        <div class="progress-info">
                            <span class="progress-text">Progress: 3 of 10 lectures</span>
                            <span class="progress-percentage">30%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 30%"></div>
                        </div>
                    </div>
                </div>

                <!-- Curriculum Section -->
                <div class="curriculum-section">
                    <h2 class="curriculum-title">
                        <button class="sidebar-hamburger-toggle" aria-label="Toggle sidebar">
                            <i class="fas fa-bars curriculum-icon"></i>
                        </button>
                        Course Content
                    </h2>

                    <!-- Test Chapter -->
                    <div class="chapter expanded">
                        <div class="chapter-header">
                            <h3 class="chapter-title">
                                <span>1. Introduction</span>
                                <i class="fas fa-chevron-down chapter-toggle"></i>
                            </h3>
                        </div>
                        <div class="lectures-list">
                            <a href="#" class="lecture-item current">
                                <div class="lecture-content">
                                    <div class="lecture-info">
                                        <span class="lecture-title">Welcome to the Course</span>
                                        <span class="lecture-duration">5:30</span>
                                    </div>
                                    <div class="lecture-meta">
                                        <span class="lecture-type">
                                            <i class="fas fa-play"></i> Video
                                        </span>
                                    </div>
                                </div>
                            </a>
                            <a href="#" class="lecture-item">
                                <div class="lecture-content">
                                    <div class="lecture-info">
                                        <span class="lecture-title">Course Overview</span>
                                        <span class="lecture-duration">3:45</span>
                                    </div>
                                    <div class="lecture-meta">
                                        <span class="lecture-type">
                                            <i class="fas fa-play"></i> Video
                                        </span>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Main Content Area -->
            <main class="course-content" role="main">
                <!-- Content Header -->
                <header class="content-header">
                    <div class="header-controls">
                        <button class="sidebar-toggle" aria-label="Toggle sidebar">
                            <i class="fas fa-bars"></i>
                        </button>

                        <!-- Navigation Controls -->
                        <nav class="lecture-navigation" aria-label="Lecture navigation">
                            <a href="#" class="nav-btn">
                                <i class="fas fa-chevron-left"></i>
                                Previous
                            </a>
                            <a href="#" class="nav-btn primary">
                                Next
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </nav>
                    </div>
                </header>

                <!-- Lecture Content -->
                <div class="lecture-content-area">
                    <div style="padding: 2rem; color: white;">
                        <h1>Test Content Area</h1>
                        <p>This is a test of the sidebar toggle functionality.</p>
                        <h2>Instructions:</h2>
                        <ul>
                            <li><strong>Desktop (≥1024px):</strong> Click either hamburger menu to hide/show sidebar</li>
                            <li><strong>Mobile/Tablet (&lt;1024px):</strong> Click hamburger menu to open sidebar, click overlay or X to close</li>
                            <li><strong>Lecture clicks:</strong> Should close sidebar on mobile, hide on desktop</li>
                        </ul>
                        <h2>Test Points:</h2>
                        <ol>
                            <li>Hamburger menu in sidebar header should be clickable</li>
                            <li>Hamburger menu in content header should be clickable</li>
                            <li>Both should have the same toggle behavior</li>
                            <li>Clicking lectures should close/hide sidebar</li>
                            <li>Responsive behavior should work correctly</li>
                            <li>Keyboard shortcuts: Ctrl+S (Cmd+S) or 'B' key to toggle sidebar</li>
                        </ol>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="public/js/course-viewer.js"></script>
    <script>
        // Initialize the course viewer
        const courseViewer = new CourseViewer();
        
        // Update screen size indicator
        function updateScreenInfo() {
            const screenSize = document.getElementById('screen-size');
            const sidebarState = document.getElementById('sidebar-state');
            const width = window.innerWidth;
            
            screenSize.textContent = `${width}px (${width >= 1024 ? 'Desktop' : 'Mobile/Tablet'})`;
            
            const sidebar = document.querySelector('.course-sidebar');
            if (width >= 1024) {
                // Desktop
                if (sidebar.classList.contains('desktop-hidden')) {
                    sidebarState.textContent = 'Hidden (Desktop)';
                } else {
                    sidebarState.textContent = 'Visible (Desktop)';
                }
            } else {
                // Mobile
                if (sidebar.classList.contains('active')) {
                    sidebarState.textContent = 'Open (Mobile)';
                } else {
                    sidebarState.textContent = 'Closed (Mobile)';
                }
            }
        }
        
        // Update on load and resize
        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);
        
        // Update when sidebar state changes
        const observer = new MutationObserver(updateScreenInfo);
        observer.observe(document.querySelector('.course-sidebar'), {
            attributes: true,
            attributeFilter: ['class']
        });
        observer.observe(document.querySelector('.course-viewer-container'), {
            attributes: true,
            attributeFilter: ['class']
        });
    </script>
</body>
</html>
