import "./bootstrap"

// Mobile menu toggle
document.addEventListener("DOMContentLoaded", () => {
  const mobileMenuButton = document.getElementById("mobile-menu-button")
  const mobileMenu = document.getElementById("mobile-menu")

  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener("click", () => {
      mobileMenu.classList.toggle("hidden")
    })
  }

  // Close mobile menu when clicking outside
  document.addEventListener("click", (event) => {
    if (mobileMenu && !mobileMenu.contains(event.target) && !mobileMenuButton.contains(event.target)) {
      mobileMenu.classList.add("hidden")
    }
  })

  // User dropdown menu
  const userMenuButton = document.getElementById("user-menu-button")
  const userMenu = document.getElementById("user-menu")
  const userMenuArrow = document.getElementById("user-menu-arrow")

  if (userMenuButton && userMenu) {
    userMenuButton.addEventListener("click", (e) => {
      e.stopPropagation()
      const isOpen = !userMenu.classList.contains("opacity-0")
      
      if (isOpen) {
        closeUserMenu()
      } else {
        openUserMenu()
      }
    })

    // Close user menu when clicking outside
    document.addEventListener("click", (event) => {
      if (!userMenu.contains(event.target) && !userMenuButton.contains(event.target)) {
        closeUserMenu()
      }
    })

    // Close user menu on escape key
    document.addEventListener("keydown", (event) => {
      if (event.key === "Escape") {
        closeUserMenu()
      }
    })

    function openUserMenu() {
      userMenu.classList.remove("opacity-0", "invisible", "scale-95")
      userMenu.classList.add("opacity-100", "visible", "scale-100")
      if (userMenuArrow) {
        userMenuArrow.style.transform = "rotate(180deg)"
      }
      userMenuButton.setAttribute("aria-expanded", "true")
    }

    function closeUserMenu() {
      userMenu.classList.add("opacity-0", "invisible", "scale-95")
      userMenu.classList.remove("opacity-100", "visible", "scale-100")
      if (userMenuArrow) {
        userMenuArrow.style.transform = "rotate(0deg)"
      }
      userMenuButton.setAttribute("aria-expanded", "false")
    }
  }

  // Form validation
  const forms = document.querySelectorAll("form[data-validate]")
  forms.forEach((form) => {
    form.addEventListener("submit", (e) => {
      if (!validateForm(form)) {
        e.preventDefault()
      }
    })
  })

  // Search functionality
  const searchInput = document.querySelector('input[name="search"]')
  if (searchInput) {
    let searchTimeout
    searchInput.addEventListener("input", function () {
      clearTimeout(searchTimeout)
      searchTimeout = setTimeout(() => {
        // Auto-submit search after 500ms of no typing
        if (this.value.length > 2 || this.value.length === 0) {
          this.form.submit()
        }
      }, 500)
    })
  }

  // Progress bars animation
  const progressBars = document.querySelectorAll(".progress-fill")
  const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -50px 0px",
  }

  const progressObserver = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const progressBar = entry.target
        const width = progressBar.dataset.width || "0"
        progressBar.style.width = width + "%"
      }
    })
  }, observerOptions)

  progressBars.forEach((bar) => {
    progressObserver.observe(bar)
  })

  // Smooth scroll for anchor links
  const anchorLinks = document.querySelectorAll('a[href^="#"]')
  anchorLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      e.preventDefault()
      const target = document.querySelector(this.getAttribute("href"))
      if (target) {
        target.scrollIntoView({
          behavior: "smooth",
          block: "start",
        })
      }
    })
  })

  // Auto-hide flash messages
  const flashMessages = document.querySelectorAll(".alert")
  flashMessages.forEach((message) => {
    setTimeout(() => {
      message.style.opacity = "0"
      setTimeout(() => {
        message.remove()
      }, 300)
    }, 5000)
  })

  // Course enrollment confirmation
  const enrollButtons = document.querySelectorAll("[data-confirm-enroll]")
  enrollButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      const courseName = this.dataset.courseName || "this course"
      if (!confirm(`Are you sure you want to enroll in ${courseName}?`)) {
        e.preventDefault()
      }
    })
  })

  // Image lazy loading
  const images = document.querySelectorAll("img[data-src]")
  const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const img = entry.target
        img.src = img.dataset.src
        img.classList.remove("lazy")
        imageObserver.unobserve(img)
      }
    })
  })

  images.forEach((img) => {
    imageObserver.observe(img)
  })

  // Dropdown menus
  const dropdowns = document.querySelectorAll(".dropdown")
  dropdowns.forEach((dropdown) => {
    const trigger = dropdown.querySelector(".dropdown-trigger")
    const menu = dropdown.querySelector(".dropdown-menu")

    if (trigger && menu) {
      trigger.addEventListener("click", (e) => {
        e.stopPropagation()
        menu.classList.toggle("hidden")
      })

      // Close dropdown when clicking outside
      document.addEventListener("click", () => {
        menu.classList.add("hidden")
      })
    }
  })

  // Copy to clipboard functionality
  const copyButtons = document.querySelectorAll("[data-copy]")
  copyButtons.forEach((button) => {
    button.addEventListener("click", function () {
      const text = this.dataset.copy
      navigator.clipboard.writeText(text).then(() => {
        window.showToast("Copied to clipboard!", "success")
      })
    })
  })

  // Toast notifications
  window.showToast = (message, type = "info") => {
    const toast = document.createElement("div")
    toast.className = `fixed top-4 right-4 px-6 py-3 rounded-md text-white z-50 animate-fade-in ${getToastClass(type)}`
    toast.textContent = message

    document.body.appendChild(toast)

    setTimeout(() => {
      toast.style.opacity = "0"
      setTimeout(() => {
        toast.remove()
      }, 300)
    }, 3000)
  }

  function getToastClass(type) {
    switch (type) {
      case "success":
        return "bg-green-600"
      case "error":
        return "bg-red-600"
      case "warning":
        return "bg-yellow-600"
      default:
        return "bg-blue-600"
    }
  }

  // Form validation function
  function validateForm(form) {
    let isValid = true
    const requiredFields = form.querySelectorAll("[required]")

    requiredFields.forEach((field) => {
      if (!field.value.trim()) {
        showFieldError(field, "This field is required")
        isValid = false
      } else {
        clearFieldError(field)
      }
    })

    // Email validation
    const emailFields = form.querySelectorAll('input[type="email"]')
    emailFields.forEach((field) => {
      if (field.value && !isValidEmail(field.value)) {
        showFieldError(field, "Please enter a valid email address")
        isValid = false
      }
    })

    // Password confirmation
    const passwordField = form.querySelector('input[name="password"]')
    const confirmField = form.querySelector('input[name="password_confirmation"]')

    if (passwordField && confirmField && passwordField.value !== confirmField.value) {
      showFieldError(confirmField, "Passwords do not match")
      isValid = false
    }

    return isValid
  }

  function showFieldError(field, message) {
    field.classList.add("border-red-500")

    let errorElement = field.parentNode.querySelector(".error-message")
    if (!errorElement) {
      errorElement = document.createElement("p")
      errorElement.className = "error-message text-red-500 text-sm mt-1"
      field.parentNode.appendChild(errorElement)
    }
    errorElement.textContent = message
  }

  function clearFieldError(field) {
    field.classList.remove("border-red-500")
    const errorElement = field.parentNode.querySelector(".error-message")
    if (errorElement) {
      errorElement.remove()
    }
  }

  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // Loading states for forms
  const submitButtons = document.querySelectorAll('button[type="submit"]')
  submitButtons.forEach((button) => {
    // Store original text
    button.dataset.originalText = button.textContent
  })

  // Handle form submissions with proper loading states
  // Exclude authentication forms to prevent interference with Laravel's auth system
  const allForms = document.querySelectorAll('form:not([action*="login"]):not([action*="register"]):not([action*="password"]):not([action*="logout"]):not([action*="two-factor"]):not([action*="auth"])')
  allForms.forEach((form) => {
    let isSubmitting = false

    form.addEventListener('submit', function(e) {
      const submitButton = this.querySelector('button[type="submit"]')

      // Prevent multiple submissions
      if (isSubmitting) {
        e.preventDefault()
        return
      }

      if (submitButton && validateForm(this)) {
        isSubmitting = true

        // Set loading state
        submitButton.disabled = true
        submitButton.innerHTML = '<span class="spinner mr-2"></span>Loading...'

        // Add loading class to form for additional styling
        this.classList.add('form-loading')

        // Re-enable after 15 seconds as fallback (increased from 10)
        const fallbackTimeout = setTimeout(() => {
          isSubmitting = false
          submitButton.disabled = false
          submitButton.innerHTML = submitButton.dataset.originalText || "Submit"
          this.classList.remove('form-loading')
        }, 15000)

        // Reset button if page becomes visible again (user navigated back)
        document.addEventListener('visibilitychange', function() {
          if (!document.hidden) {
            clearTimeout(fallbackTimeout)
            isSubmitting = false
            submitButton.disabled = false
            submitButton.innerHTML = submitButton.dataset.originalText || "Submit"
            form.classList.remove('form-loading')
          }
        }, { once: true })

        // Reset on page unload
        window.addEventListener('beforeunload', function() {
          clearTimeout(fallbackTimeout)
        }, { once: true })

      } else if (!validateForm(this)) {
        // Prevent form submission if validation fails
        e.preventDefault()
      }
    })
  })

  // Keyboard shortcuts
  document.addEventListener("keydown", (e) => {
    // Ctrl/Cmd + K for search
    if ((e.ctrlKey || e.metaKey) && e.key === "k") {
      e.preventDefault()
      const searchInput = document.querySelector('input[name="search"]')
      if (searchInput) {
        searchInput.focus()
      }
    }

    // Escape to close modals/dropdowns
    if (e.key === "Escape") {
      const openDropdowns = document.querySelectorAll(".dropdown-menu:not(.hidden)")
      openDropdowns.forEach((dropdown) => {
        dropdown.classList.add("hidden")
      })

      const mobileMenu = document.getElementById("mobile-menu")
      if (mobileMenu && !mobileMenu.classList.contains("hidden")) {
        mobileMenu.classList.add("hidden")
      }
    }
  })

  // Performance monitoring
  if ("performance" in window) {
    window.addEventListener("load", () => {
      setTimeout(() => {
        const perfData = performance.getEntriesByType("navigation")[0]
        if (perfData.loadEventEnd - perfData.loadEventStart > 3000) {
          console.warn("Page load time is slow:", perfData.loadEventEnd - perfData.loadEventStart, "ms")
        }
      }, 0)
    })
  }

  // Service worker registration removed - not needed for basic functionality
})

// Utility functions
window.utils = {
  formatCurrency: (amount) =>
    new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount),

  formatDate: (date) =>
    new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }).format(new Date(date)),

  debounce: (func, wait) => {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  },

  throttle: (func, limit) => {
    let inThrottle
    return function () {
      const args = arguments
      
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => (inThrottle = false), limit)
      }
    }
  },
}
