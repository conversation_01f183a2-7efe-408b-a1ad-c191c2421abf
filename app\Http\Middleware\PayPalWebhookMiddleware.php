<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class PayPalWebhookMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Verify the request is coming from PayPal
        if (!$this->isValidPayPalRequest($request)) {
            Log::warning('Invalid PayPal webhook request', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'headers' => $request->headers->all()
            ]);
            
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        // Verify content type
        if (!$request->isJson()) {
            Log::warning('PayPal webhook invalid content type', [
                'content_type' => $request->header('Content-Type')
            ]);
            
            return response()->json(['error' => 'Invalid content type'], 400);
        }

        // Verify request has body
        if (empty($request->getContent())) {
            Log::warning('PayPal webhook empty body');
            return response()->json(['error' => 'Empty request body'], 400);
        }

        // Rate limiting - prevent too many requests from same IP
        $ip = $request->ip();
        $cacheKey = "paypal_webhook_rate_limit:{$ip}";
        $attempts = cache()->get($cacheKey, 0);
        
        if ($attempts > 100) { // Max 100 requests per hour per IP
            Log::warning('PayPal webhook rate limit exceeded', ['ip' => $ip]);
            return response()->json(['error' => 'Rate limit exceeded'], 429);
        }
        
        cache()->put($cacheKey, $attempts + 1, 3600); // 1 hour

        return $next($request);
    }

    /**
     * Verify if the request is coming from PayPal
     */
    private function isValidPayPalRequest(Request $request): bool
    {
        // Check for required PayPal headers
        $requiredHeaders = [
            'paypal-auth-algo',
            'paypal-transmission-id',
            'paypal-cert-id',
            'paypal-transmission-sig',
            'paypal-transmission-time'
        ];

        foreach ($requiredHeaders as $header) {
            if (!$request->hasHeader($header)) {
                return false;
            }
        }

        // Verify User-Agent contains PayPal
        $userAgent = $request->userAgent();
        if (!$userAgent || !str_contains(strtolower($userAgent), 'paypal')) {
            return false;
        }

        // Check transmission time to prevent replay attacks
        $transmissionTime = $request->header('paypal-transmission-time');
        if ($transmissionTime) {
            $timestamp = intval($transmissionTime);
            $currentTime = time();
            $timeDiff = abs($currentTime - $timestamp);
            
            // Reject requests older than 5 minutes or from the future
            if ($timeDiff > 300) {
                Log::warning('PayPal webhook timestamp validation failed', [
                    'transmission_time' => $transmissionTime,
                    'current_time' => $currentTime,
                    'difference' => $timeDiff
                ]);
                return false;
            }
        }

        return true;
    }
}
