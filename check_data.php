<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "📊 Checking Course Wizard Data\n";
echo "==============================\n\n";

// Check courses
$courseCount = App\Models\Course::count();
echo "Total courses: {$courseCount}\n";

// Check lectures by type
$videoCount = App\Models\Lecture::where('type', 'video')->count();
$textCount = App\Models\Lecture::where('type', 'text')->count();
$quizCount = App\Models\Lecture::where('type', 'quiz')->count();
$assignmentCount = App\Models\Lecture::where('type', 'assignment')->count();
$resourceCount = App\Models\Lecture::where('type', 'resource')->count();

echo "Lecture types:\n";
echo "  - Video: {$videoCount}\n";
echo "  - Text: {$textCount}\n";
echo "  - Quiz: {$quizCount}\n";
echo "  - Assignment: {$assignmentCount}\n";
echo "  - Resource: {$resourceCount}\n\n";

// Check JSON data integrity
$course = App\Models\Course::first();
if ($course) {
    echo "Sample course data:\n";
    echo "  - Title: {$course->title}\n";
    echo "  - What you'll learn: " . (is_array($course->what_you_will_learn) ? 'ARRAY ✓' : 'STRING ✗') . "\n";
    echo "  - Requirements: " . (is_array($course->requirements) ? 'ARRAY ✓' : 'STRING ✗') . "\n";
    echo "  - Target audience: " . (is_array($course->target_audience) ? 'ARRAY ✓' : 'STRING ✗') . "\n\n";
}

// Check quiz lectures
$quizLecture = App\Models\Lecture::where('type', 'quiz')->first();
if ($quizLecture) {
    echo "Sample quiz lecture:\n";
    echo "  - Title: {$quizLecture->title}\n";
    echo "  - Quiz data: " . (is_array($quizLecture->quiz_data) ? 'ARRAY ✓' : 'STRING ✗') . "\n";
    if (is_array($quizLecture->quiz_data) && isset($quizLecture->quiz_data['questions'])) {
        echo "  - Questions count: " . count($quizLecture->quiz_data['questions']) . "\n";
    }
    echo "\n";
}

// Check resource lectures
$resourceLecture = App\Models\Lecture::where('type', 'resource')->first();
if ($resourceLecture) {
    echo "Sample resource lecture:\n";
    echo "  - Title: {$resourceLecture->title}\n";
    echo "  - Resources: " . (is_array($resourceLecture->resources) ? 'ARRAY ✓' : 'STRING ✗') . "\n";
    if (is_array($resourceLecture->resources)) {
        echo "  - Resource files count: " . count($resourceLecture->resources) . "\n";
    }
    echo "\n";
}

echo "✅ Data check complete!\n";
