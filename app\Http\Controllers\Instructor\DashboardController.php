<?php

namespace App\Http\Controllers\Instructor;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Chapter;
use App\Models\Lecture;
use App\Models\Enrollment;
use App\Models\Payment;
use App\Models\CourseReview;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Display the instructor dashboard.
     */
    public function index()
    {
        $instructor = auth()->user();
        
        // Get instructor's content statistics
        $stats = [
            'total_courses' => $instructor->courses()->count(),
            'published_courses' => $instructor->courses()->where('status', 'published')->count(),
            'draft_courses' => $instructor->courses()->where('status', 'draft')->count(),
            'total_chapters' => $instructor->chapters()->count(),
            'total_lectures' => $instructor->lectures()->count(),
            'total_students' => Enrollment::whereIn('course_id', $instructor->courses()->pluck('id'))->count(),
            'active_students' => Enrollment::whereIn('course_id', $instructor->courses()->pluck('id'))
                ->where('status', 'active')->count(),
            'completed_students' => Enrollment::whereIn('course_id', $instructor->courses()->pluck('id'))
                ->where('status', 'completed')->count(),
        ];

        // Get revenue statistics
        $revenueStats = [
            'total_revenue' => $instructor->instructorPayments()->where('status', 'completed')->sum('instructor_amount'),
            'this_month_revenue' => $instructor->instructorPayments()
                ->where('status', 'completed')
                ->whereMonth('paid_at', now()->month)
                ->whereYear('paid_at', now()->year)
                ->sum('instructor_amount'),
            'pending_revenue' => $instructor->instructorPayments()->where('status', 'pending')->sum('instructor_amount'),
            'total_sales' => $instructor->instructorPayments()->where('status', 'completed')->count(),
        ];

        // Get review statistics
        $reviewStats = [
            'total_reviews' => CourseReview::whereIn('course_id', $instructor->courses()->pluck('id'))
                ->where('is_published', true)->count(),
            'average_rating' => CourseReview::whereIn('course_id', $instructor->courses()->pluck('id'))
                ->where('is_published', true)->avg('rating') ?: 0,
            'five_star_reviews' => CourseReview::whereIn('course_id', $instructor->courses()->pluck('id'))
                ->where('is_published', true)->where('rating', 5)->count(),
            'recent_reviews' => CourseReview::whereIn('course_id', $instructor->courses()->pluck('id'))
                ->where('is_published', true)
                ->with(['user', 'course'])
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get(),
        ];

        // Get recent courses
        $recentCourses = $instructor->courses()
            ->with(['enrollments', 'reviews'])
            ->withCount(['enrollments', 'chapters', 'lectures'])
            ->orderBy('updated_at', 'desc')
            ->take(5)
            ->get();

        // Get recent enrollments
        $recentEnrollments = Enrollment::whereIn('course_id', $instructor->courses()->pluck('id'))
            ->with(['user', 'course'])
            ->orderBy('enrolled_at', 'desc')
            ->take(10)
            ->get();

        // Get monthly revenue chart data (last 12 months)
        $monthlyRevenue = $instructor->instructorPayments()
            ->where('status', 'completed')
            ->where('paid_at', '>=', now()->subMonths(12))
            ->select(
                DB::raw('YEAR(paid_at) as year'),
                DB::raw('MONTH(paid_at) as month'),
                DB::raw('SUM(instructor_amount) as revenue'),
                DB::raw('COUNT(*) as sales')
            )
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();

        // Get course performance data
        $coursePerformance = $instructor->courses()
            ->with(['enrollments', 'reviews'])
            ->withCount(['enrollments as total_enrollments', 'reviews as total_reviews'])
            ->withAvg('reviews as average_rating', 'rating')
            ->where('status', 'published')
            ->orderBy('total_enrollments', 'desc')
            ->take(10)
            ->get();

        return view('instructor.dashboard', compact(
            'instructor',
            'stats',
            'revenueStats',
            'reviewStats',
            'recentCourses',
            'recentEnrollments',
            'monthlyRevenue',
            'coursePerformance'
        ));
    }

    /**
     * Get dashboard analytics data for AJAX requests.
     */
    public function analytics(Request $request)
    {
        $instructor = auth()->user();
        $period = $request->get('period', '30'); // days

        $startDate = now()->subDays($period);

        // Get enrollment trends
        $enrollmentTrends = Enrollment::whereIn('course_id', $instructor->courses()->pluck('id'))
            ->where('enrolled_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(enrolled_at) as date'),
                DB::raw('COUNT(*) as enrollments')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Get revenue trends
        $revenueTrends = Payment::where('instructor_id', $instructor->id)
            ->where('status', 'completed')
            ->where('paid_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(paid_at) as date'),
                DB::raw('SUM(instructor_amount) as revenue')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Get course completion rates
        $completionRates = $instructor->courses()
            ->with(['enrollments' => function ($query) {
                $query->select('course_id', 'status', DB::raw('COUNT(*) as count'))
                    ->groupBy('course_id', 'status');
            }])
            ->where('status', 'published')
            ->get()
            ->map(function ($course) {
                $totalEnrollments = $course->enrollments->sum('count');
                $completedEnrollments = $course->enrollments->where('status', 'completed')->sum('count');
                
                return [
                    'course_title' => $course->title,
                    'total_enrollments' => $totalEnrollments,
                    'completed_enrollments' => $completedEnrollments,
                    'completion_rate' => $totalEnrollments > 0 ? round(($completedEnrollments / $totalEnrollments) * 100, 2) : 0,
                ];
            });

        return response()->json([
            'enrollment_trends' => $enrollmentTrends,
            'revenue_trends' => $revenueTrends,
            'completion_rates' => $completionRates,
        ]);
    }

    /**
     * Get quick stats for dashboard widgets.
     */
    public function quickStats()
    {
        $instructor = auth()->user();

        $stats = [
            'courses_this_month' => $instructor->courses()
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
            'students_this_week' => Enrollment::whereIn('course_id', $instructor->courses()->pluck('id'))
                ->where('enrolled_at', '>=', now()->subWeek())
                ->count(),
            'revenue_today' => $instructor->instructorPayments()
                ->where('status', 'completed')
                ->whereDate('paid_at', now()->toDateString())
                ->sum('instructor_amount'),
            'reviews_this_week' => CourseReview::whereIn('course_id', $instructor->courses()->pluck('id'))
                ->where('created_at', '>=', now()->subWeek())
                ->count(),
        ];

        return response()->json($stats);
    }
}
