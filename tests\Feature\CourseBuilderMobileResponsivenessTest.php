<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\CourseCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CourseBuilderMobileResponsivenessTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $instructor;
    protected $course;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an instructor user
        $this->instructor = User::factory()->create([
            'role' => 'instructor',
            'email_verified_at' => now(),
        ]);

        // Create a basic category
        CourseCategory::create([
            'name' => 'General',
            'slug' => 'general',
            'description' => 'General courses',
            'is_active' => true,
        ]);

        // Create a test course
        $this->course = Course::create([
            'title' => 'Test Mobile Course',
            'subtitle' => 'Testing mobile responsiveness',
            'description' => 'Test description for mobile responsiveness',
            'instructor_id' => $this->instructor->id,
            'slug' => 'test-mobile-course',
            'status' => 'draft',
            'level' => 'beginner',
            'language' => 'English',
            'price' => 0,
            'category' => 'General',
            'what_you_will_learn' => [],
            'requirements' => [],
            'target_audience' => [],
        ]);
    }

    /** @test */
    public function course_builder_contains_mobile_responsive_elements()
    {
        $this->actingAs($this->instructor);

        $response = $this->get(route('instructor.course-builder.show', $this->course));

        $response->assertStatus(200);

        // Check for mobile/tablet sidebar toggle button
        $response->assertSee('id="mobile-sidebar-toggle"', false);
        $response->assertSee('fas fa-bars', false);

        // Check for mobile/tablet sidebar overlay
        $response->assertSee('id="mobile-sidebar-overlay"', false);

        // Check for mobile/tablet sidebar close button
        $response->assertSee('id="mobile-sidebar-close"', false);
        $response->assertSee('fas fa-times', false);

        // Check for mobile/tablet add chapter button
        $response->assertSee('id="add-chapter-btn-mobile"', false);

        // Check for responsive CSS classes (now using lg: breakpoint)
        $response->assertSee('lg:hidden', false);
        $response->assertSee('flex-col lg:flex-row', false);
        $response->assertSee('fixed inset-y-0 left-0 z-50', false);
        $response->assertSee('transform -translate-x-full', false);
    }

    /** @test */
    public function course_builder_header_is_mobile_responsive()
    {
        $this->actingAs($this->instructor);

        $response = $this->get(route('instructor.course-builder.show', $this->course));

        $response->assertStatus(200);

        // Check for mobile/tablet header layout classes
        $response->assertSee('flex-col space-y-3 lg:flex-row lg:items-center lg:justify-between lg:space-y-0', false);

        // Check for mobile/tablet-specific layout structure
        $response->assertSee('lg:hidden', false); // Mobile/tablet-only sections
        $response->assertSee('hidden lg:flex', false); // Desktop-only sections

        // Check for responsive text sizing
        $response->assertSee('text-lg', false);
        $response->assertSee('text-xl', false);

        // Check for mobile-specific button text
        $response->assertSee('sm:hidden', false);
        $response->assertSee('hidden sm:inline', false);

        // Check for mobile title layout
        $response->assertSee('truncate pr-4', false);
    }

    /** @test */
    public function course_builder_overview_section_is_mobile_responsive()
    {
        $this->actingAs($this->instructor);

        $response = $this->get(route('instructor.course-builder.show', $this->course));

        $response->assertStatus(200);

        // Check for mobile and desktop layout sections
        $response->assertSee('block md:hidden', false); // Mobile layout
        $response->assertSee('hidden md:block', false); // Desktop layout
        
        // Check for mobile grid layout
        $response->assertSee('grid-cols-2 gap-3', false);
        
        // Check for responsive padding
        $response->assertSee('p-4 md:p-6', false);
    }

    /** @test */
    public function course_builder_forms_are_mobile_responsive()
    {
        $this->actingAs($this->instructor);

        $response = $this->get(route('instructor.course-builder.show', $this->course));

        $response->assertStatus(200);

        // Check for responsive form layouts
        $response->assertSee('grid-cols-1 lg:grid-cols-2', false);
        $response->assertSee('grid-cols-1 md:grid-cols-2 lg:grid-cols-3', false);
        
        // Check for responsive input styling
        $response->assertSee('px-3 py-3 md:px-4 md:py-3', false);
        $response->assertSee('text-sm md:text-base', false);
        
        // Check for responsive spacing
        $response->assertSee('space-y-4 md:space-y-6', false);
        $response->assertSee('gap-4 md:gap-6', false);
    }

    /** @test */
    public function course_builder_contains_mobile_specific_css()
    {
        $this->actingAs($this->instructor);

        $response = $this->get(route('instructor.course-builder.show', $this->course));

        $response->assertStatus(200);

        // Check for mobile-specific CSS media queries
        $response->assertSee('@media (max-width: 767px)', false);
        
        // Check for mobile touch target sizing
        $response->assertSee('min-height: 48px', false);
        $response->assertSee('min-height: 44px', false);
        
        // Check for mobile font size adjustments
        $response->assertSee('font-size: 16px', false);
        
        // Check for mobile-specific utility classes
        $response->assertSee('overflow-hidden', false);
        $response->assertSee('backdrop-filter: blur(2px)', false);
    }

    /** @test */
    public function course_builder_javascript_includes_mobile_functionality()
    {
        $this->actingAs($this->instructor);

        $response = $this->get(route('instructor.course-builder.show', $this->course));

        $response->assertStatus(200);

        // Check for mobile navigation JavaScript functions
        $response->assertSee('initializeMobileNavigation', false);
        $response->assertSee('closeMobileSidebar', false);
        
        // Check for mobile event listeners
        $response->assertSee('mobile-sidebar-toggle', false);
        $response->assertSee('mobile-sidebar-close', false);
        $response->assertSee('mobile-sidebar-overlay', false);
        
        // Check for responsive behavior
        $response->assertSee('window.innerWidth < 768', false);
        $response->assertSee('ontouchstart', false);
    }
}
