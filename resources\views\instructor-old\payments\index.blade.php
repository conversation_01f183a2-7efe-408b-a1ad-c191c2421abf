@extends('instructor.layouts.app')

@section('title', 'Payment Dashboard - Instructor Dashboard')

@section('content')
<div class="py-12">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">Payment Dashboard</h1>
                <p class="text-gray-400">Track your revenue and payment analytics</p>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-4">
                <a href="{{ route('instructor.payments.analytics') }}" 
                   class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-chart-line mr-2"></i>Analytics
                </a>
                <a href="{{ route('instructor.payments.history') }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-history mr-2"></i>Full History
                </a>
            </div>
        </div>

        <!-- Date Range Filter -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-8">
            <form method="GET" class="flex flex-wrap items-end gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Start Date</label>
                    <input type="date" name="start_date" value="{{ $startDate }}" 
                           class="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">End Date</label>
                    <input type="date" name="end_date" value="{{ $endDate }}" 
                           class="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500">
                </div>
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-filter mr-2"></i>Update
                </button>
            </form>
        </div>

        <!-- Revenue Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-green-600 rounded-lg">
                        <i class="fas fa-dollar-sign text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Total Revenue</p>
                        <p class="text-2xl font-bold text-white">${{ number_format($stats['total_revenue'], 2) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-600 rounded-lg">
                        <i class="fas fa-credit-card text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Transactions</p>
                        <p class="text-2xl font-bold text-white">{{ $stats['total_transactions'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-yellow-600 rounded-lg">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Pending</p>
                        <p class="text-2xl font-bold text-white">{{ $stats['pending_payments'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-red-600 rounded-lg">
                        <i class="fas fa-times-circle text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Failed</p>
                        <p class="text-2xl font-bold text-white">{{ $stats['failed_payments'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-purple-600 rounded-lg">
                        <i class="fas fa-undo text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Refunded</p>
                        <p class="text-2xl font-bold text-white">${{ number_format($stats['refunded_amount'], 2) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-indigo-600 rounded-lg">
                        <i class="fas fa-chart-bar text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-400 text-sm">Avg. Transaction</p>
                        <p class="text-2xl font-bold text-white">${{ number_format($stats['average_transaction'], 2) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Chart -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-8">
            <h3 class="text-xl font-bold text-white mb-4">Revenue Over Time</h3>
            <div class="h-64 flex items-center justify-center">
                @if($revenueData->count() > 0)
                    <canvas id="revenueChart" width="400" height="200"></canvas>
                @else
                    <div class="text-gray-400 text-center">
                        <i class="fas fa-chart-line text-4xl mb-4"></i>
                        <p>No revenue data available for the selected period</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Top Performing Courses -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <h3 class="text-xl font-bold text-white mb-4">Top Performing Courses</h3>
                @if($topCourses->count() > 0)
                    <div class="space-y-4">
                        @foreach($topCourses as $courseData)
                            <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                                <div>
                                    <h4 class="text-white font-medium">{{ $courseData->course->title }}</h4>
                                    <p class="text-gray-400 text-sm">{{ $courseData->sales }} sales</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-green-400 font-bold">${{ number_format($courseData->revenue, 2) }}</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-gray-400 text-center py-8">
                        <i class="fas fa-trophy text-4xl mb-4"></i>
                        <p>No course data available</p>
                    </div>
                @endif
            </div>

            <!-- Recent Payments -->
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-bold text-white">Recent Payments</h3>
                    <a href="{{ route('instructor.payments.history') }}" class="text-red-500 hover:text-red-400 text-sm">
                        View All <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
                @if($recentPayments->count() > 0)
                    <div class="space-y-3">
                        @foreach($recentPayments as $payment)
                            <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <h4 class="text-white font-medium">{{ $payment->user->name }}</h4>
                                        @if($payment->payment_method === 'paypal')
                                            <svg class="w-4 h-4 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.028-.026.056-.052.08-.65 3.85-3.197 5.341-6.957 5.341h-2.504c-.524 0-.968.382-1.05.9L8.937 19.9c-.013.06-.004.119.021.176.067.153.211.261.379.261h2.94c.458 0 .848-.334.922-.788l.04-.207.738-4.68.047-.257c.075-.453.465-.788.922-.788h.58c3.57 0 6.36-1.45 7.17-5.64.34-1.75.17-3.21-.72-4.25-.27-.31-.61-.56-1.01-.72z"/>
                                            </svg>
                                        @endif
                                    </div>
                                    <p class="text-gray-400 text-sm">{{ $payment->course->title }}</p>
                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                        <span>{{ $payment->created_at->format('M j, Y g:i A') }}</span>
                                        @if($payment->payment_id)
                                            <span>•</span>
                                            <span>ID: {{ Str::limit($payment->payment_id, 12) }}</span>
                                        @endif
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-green-400 font-bold">${{ number_format($payment->instructor_amount, 2) }}</p>
                                    <div class="flex items-center space-x-1 mt-1">
                                        <span class="px-2 py-1 text-xs rounded-full
                                            @if($payment->status === 'completed') bg-green-900 text-green-300
                                            @elseif($payment->status === 'pending') bg-yellow-900 text-yellow-300
                                            @elseif($payment->status === 'failed') bg-red-900 text-red-300
                                            @else bg-gray-900 text-gray-300 @endif">
                                            {{ ucfirst($payment->status) }}
                                        </span>
                                        @if($payment->payment_method)
                                            <span class="text-xs text-gray-500">{{ ucfirst($payment->payment_method) }}</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-gray-400 text-center py-8">
                        <i class="fas fa-receipt text-4xl mb-4"></i>
                        <p>No recent payments</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@if($revenueData->count() > 0)
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('revenueChart').getContext('2d');
    const revenueData = @json($revenueData);
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: revenueData.map(item => item.date),
            datasets: [{
                label: 'Revenue',
                data: revenueData.map(item => item.revenue),
                borderColor: '#ef4444',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#ffffff'
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: '#9ca3af'
                    },
                    grid: {
                        color: '#374151'
                    }
                },
                y: {
                    ticks: {
                        color: '#9ca3af',
                        callback: function(value) {
                            return '$' + value.toFixed(2);
                        }
                    },
                    grid: {
                        color: '#374151'
                    }
                }
            }
        }
    });
});
</script>
@endif
@endsection
