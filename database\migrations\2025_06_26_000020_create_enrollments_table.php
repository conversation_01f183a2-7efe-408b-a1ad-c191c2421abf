<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('enrollments', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->uuid('course_id');
            $table->uuid('instructor_id'); // Denormalized for performance
            $table->timestamp('enrolled_at')->useCurrent();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('last_accessed_at')->nullable();
            $table->enum('status', ['active', 'inactive', 'suspended', 'completed', 'refunded'])->default('active');
            $table->integer('progress_percentage')->default(0); // 0-100
            $table->integer('completed_lectures')->default(0);
            $table->integer('total_lectures')->default(0); // Snapshot at enrollment time
            $table->integer('total_watch_time_minutes')->default(0);
            $table->uuid('current_lecture_id')->nullable(); // Last accessed lecture
            $table->json('completed_lecture_ids')->nullable(); // Array of completed lecture IDs
            $table->json('quiz_scores')->nullable(); // Quiz results
            $table->decimal('final_score', 5, 2)->nullable(); // Overall course score
            $table->boolean('certificate_issued')->default(false);
            $table->timestamp('certificate_issued_at')->nullable();
            $table->string('certificate_url')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('cascade');
            $table->foreign('instructor_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('current_lecture_id')->references('id')->on('lectures')->onDelete('set null');

            // Indexes for performance
            $table->index(['user_id', 'status']);
            $table->index(['course_id', 'status']);
            $table->index(['instructor_id', 'status']);
            $table->index(['status', 'enrolled_at']);
            $table->index('last_accessed_at');
            $table->index('progress_percentage');
            
            // Unique constraint to prevent duplicate enrollments
            $table->unique(['user_id', 'course_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('enrollments');
    }
};
