<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Payment extends Model
{
    use HasFactory;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'user_id',
        'course_id',
        'instructor_id',
        'payment_method',
        'payment_provider_id',
        'transaction_id',
        'amount',
        'instructor_amount',
        'platform_fee',
        'processing_fee',
        'currency',
        'status',
        'payment_details',
        'refund_details',
        'paid_at',
        'refunded_at',
        'notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'instructor_amount' => 'decimal:2',
        'platform_fee' => 'decimal:2',
        'processing_fee' => 'decimal:2',
        'payment_details' => 'array',
        'refund_details' => 'array',
        'paid_at' => 'datetime',
        'refunded_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
            if (empty($model->transaction_id)) {
                $model->transaction_id = 'TXN_' . strtoupper(Str::random(12));
            }
        });
    }

    /**
     * Get the user that made the payment.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the course for the payment.
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get the instructor for the payment.
     */
    public function instructor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'instructor_id');
    }

    /**
     * Scope to filter completed payments.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to filter pending payments.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to filter failed payments.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope to filter refunded payments.
     */
    public function scopeRefunded($query)
    {
        return $query->where('status', 'refunded');
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by course.
     */
    public function scopeByCourse($query, $courseId)
    {
        return $query->where('course_id', $courseId);
    }

    /**
     * Scope to filter by instructor.
     */
    public function scopeForInstructor($query, $instructorId)
    {
        return $query->where('instructor_id', $instructorId);
    }

    /**
     * Scope to filter by payment method.
     */
    public function scopeByPaymentMethod($query, $method)
    {
        return $query->where('payment_method', $method);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Check if the payment is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the payment is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the payment is failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if the payment is refunded.
     */
    public function isRefunded(): bool
    {
        return $this->status === 'refunded';
    }

    /**
     * Mark payment as completed.
     */
    public function markAsCompleted(array $paymentDetails = []): void
    {
        $this->update([
            'status' => 'completed',
            'paid_at' => now(),
            'payment_details' => array_merge($this->payment_details ?? [], $paymentDetails),
        ]);

        // Create enrollment when payment is completed
        $this->createEnrollment();
    }

    /**
     * Mark payment as failed.
     */
    public function markAsFailed(string $reason = null): void
    {
        $details = $this->payment_details ?? [];
        if ($reason) {
            $details['failure_reason'] = $reason;
        }

        $this->update([
            'status' => 'failed',
            'payment_details' => $details,
        ]);
    }

    /**
     * Process refund.
     */
    public function processRefund(array $refundDetails = []): void
    {
        $this->update([
            'status' => 'refunded',
            'refunded_at' => now(),
            'refund_details' => $refundDetails,
        ]);

        // Deactivate enrollment when payment is refunded
        $this->deactivateEnrollment();
    }

    /**
     * Calculate platform fee based on amount.
     */
    public function calculatePlatformFee(float $platformFeePercentage = 10): void
    {
        $this->platform_fee = ($this->amount * $platformFeePercentage) / 100;
        $this->instructor_amount = $this->amount - $this->platform_fee - $this->processing_fee;
    }

    /**
     * Get formatted amount.
     */
    public function getFormattedAmount(): string
    {
        return $this->currency . ' ' . number_format($this->amount, 2);
    }

    /**
     * Get formatted instructor amount.
     */
    public function getFormattedInstructorAmount(): string
    {
        return $this->currency . ' ' . number_format($this->instructor_amount, 2);
    }

    /**
     * Create enrollment after successful payment.
     */
    private function createEnrollment(): void
    {
        // Check if enrollment already exists
        $existingEnrollment = Enrollment::where('user_id', $this->user_id)
            ->where('course_id', $this->course_id)
            ->first();

        if (!$existingEnrollment) {
            Enrollment::create([
                'user_id' => $this->user_id,
                'course_id' => $this->course_id,
                'instructor_id' => $this->instructor_id,
                'status' => 'active',
            ]);
        } elseif ($existingEnrollment->status !== 'active') {
            // Reactivate existing enrollment
            $existingEnrollment->update(['status' => 'active']);
        }
    }

    /**
     * Deactivate enrollment after refund.
     */
    private function deactivateEnrollment(): void
    {
        Enrollment::where('user_id', $this->user_id)
            ->where('course_id', $this->course_id)
            ->update(['status' => 'refunded']);
    }

    /**
     * Generate PayPal payment URL.
     */
    public function generatePayPalUrl(): string
    {
        return route('paypal.create-payment', [
            'course' => $this->course_id,
            'payment' => $this->id
        ]);
    }

    /**
     * Get payment receipt URL.
     */
    public function getReceiptUrl(): string
    {
        return route('payments.receipt', [
            'payment' => $this->id,
            'token' => hash('sha256', $this->id . $this->user_id . $this->transaction_id)
        ]);
    }
}
