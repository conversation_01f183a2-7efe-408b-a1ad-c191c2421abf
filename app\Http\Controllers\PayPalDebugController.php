<?php

namespace App\Http\Controllers;

use App\Services\PayPalDebugService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PayPalDebugController extends Controller
{
    private $debugService;

    public function __construct(PayPalDebugService $debugService)
    {
        $this->debugService = $debugService;
    }

    /**
     * Show PayPal debug dashboard
     */
    public function dashboard()
    {
        $config = $this->debugService->getConfigurationInfo();
        
        return view('paypal.debug.dashboard', compact('config'));
    }

    /**
     * Create a test order for debugging
     */
    public function createTestOrder()
    {
        $result = $this->debugService->createTestOrder();
        
        return response()->json($result);
    }

    /**
     * Debug capture request with different approaches
     */
    public function debugCapture(Request $request)
    {
        $orderId = $request->get('order_id');
        
        if (!$orderId) {
            return response()->json([
                'error' => 'Order ID is required'
            ], 400);
        }

        $results = $this->debugService->debugCaptureRequest($orderId);
        
        return response()->json([
            'order_id' => $orderId,
            'debug_results' => $results,
            'summary' => $this->generateDebugSummary($results)
        ]);
    }

    /**
     * Generate a summary of debug results
     */
    private function generateDebugSummary(array $results): array
    {
        $summary = [
            'total_tests' => count($results) - 1, // Exclude access_token result
            'successful_tests' => 0,
            'failed_tests' => 0,
            'malformed_json_errors' => 0,
            'recommended_approach' => null,
            'issues_found' => []
        ];

        foreach ($results as $key => $result) {
            if ($key === 'access_token') continue;

            if (is_array($result) && isset($result['successful'])) {
                if ($result['successful']) {
                    $summary['successful_tests']++;
                    if (strpos($key, 'test_3') !== false) {
                        $summary['recommended_approach'] = $result['test_name'];
                    }
                } else {
                    $summary['failed_tests']++;
                    
                    if (isset($result['error_details'])) {
                        foreach ($result['error_details'] as $detail) {
                            if (isset($detail['issue']) && $detail['issue'] === 'MALFORMED_REQUEST_JSON') {
                                $summary['malformed_json_errors']++;
                                $summary['issues_found'][] = [
                                    'test' => $result['test_name'],
                                    'issue' => 'MALFORMED_REQUEST_JSON',
                                    'description' => $detail['description'] ?? 'JSON formatting issue'
                                ];
                            }
                        }
                    }
                }
            }
        }

        return $summary;
    }

    /**
     * Test the current PayPal service implementation
     */
    public function testCurrentImplementation(Request $request)
    {
        $orderId = $request->get('order_id');
        
        if (!$orderId) {
            return response()->json([
                'error' => 'Order ID is required'
            ], 400);
        }

        try {
            $paypalService = app(\App\Services\PayPalService::class);
            $result = $paypalService->captureOrder($orderId);
            
            return response()->json([
                'test_type' => 'Current PayPal Service Implementation',
                'order_id' => $orderId,
                'result' => $result,
                'success' => $result['success'] ?? false
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'test_type' => 'Current PayPal Service Implementation',
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'success' => false
            ]);
        }
    }

    /**
     * Show detailed logs for PayPal requests
     */
    public function showLogs()
    {
        // Get recent PayPal-related log entries
        $logFile = storage_path('logs/laravel.log');
        $logs = [];

        if (file_exists($logFile)) {
            $content = file_get_contents($logFile);
            $lines = explode("\n", $content);
            
            // Get last 100 lines that contain PayPal-related content
            $paypalLines = array_filter($lines, function($line) {
                return stripos($line, 'paypal') !== false || 
                       stripos($line, 'capture') !== false ||
                       stripos($line, 'MALFORMED_REQUEST_JSON') !== false;
            });
            
            $logs = array_slice(array_reverse($paypalLines), 0, 50);
        }

        return response()->json([
            'logs' => $logs,
            'log_file' => $logFile,
            'total_entries' => count($logs)
        ]);
    }

    /**
     * Clear PayPal-related logs
     */
    public function clearLogs()
    {
        try {
            // Create a backup of current logs
            $logFile = storage_path('logs/laravel.log');
            if (file_exists($logFile)) {
                $backupFile = storage_path('logs/laravel_backup_' . date('Y-m-d_H-i-s') . '.log');
                copy($logFile, $backupFile);
                
                // Clear the log file
                file_put_contents($logFile, '');
                
                Log::info('PayPal debug logs cleared', [
                    'backup_file' => $backupFile,
                    'cleared_at' => now()->toISOString()
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Logs cleared successfully',
                'backup_created' => isset($backupFile) ? basename($backupFile) : null
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
