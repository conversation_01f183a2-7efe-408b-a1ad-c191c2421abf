@extends('layouts.app')

@section('title', 'Edit Chapter - ' . $chapter->title)

@section('content')
<div class="min-h-screen bg-black">
    <!-- Header -->
    <div class="bg-gradient-to-br from-black via-gray-900 to-black border-b border-gray-800">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-4">
                        <a href="{{ route('instructor.courses.show', $course) }}" 
                           class="text-gray-400 hover:text-red-500 transition-colors">
                            <i class="fas fa-arrow-left"></i>
                        </a>
                        <div>
                            <h1 class="text-3xl font-bold text-white">Edit <span class="text-red-500">Chapter</span></h1>
                            <p class="text-gray-400 mt-1">{{ $course->title }}</p>
                        </div>
                    </div>
                </div>

                <!-- Breadcrumb -->
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <div class="flex items-center">
                                <a href="{{ route('instructor.courses.show', $course) }}" class="text-sm font-medium text-gray-400 hover:text-red-500">
                                    Course Details
                                </a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-gray-600 mr-4"></i>
                                <span class="text-sm font-medium text-red-500">Edit Chapter</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-gray-900 rounded-xl border border-gray-800 overflow-hidden">
            <div class="p-6">
                <form action="{{ route('instructor.courses.chapters.update', [$course, $chapter]) }}" method="POST">
                    @csrf
                    @method('PUT')

                    <!-- Chapter Title -->
                    <div class="mb-6">
                        <label for="title" class="block text-sm font-medium text-gray-300 mb-2">
                            Chapter Title <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="title" 
                               name="title" 
                               value="{{ old('title', $chapter->title) }}"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                               placeholder="Enter chapter title"
                               required>
                        @error('title')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Chapter Description -->
                    <div class="mb-6">
                        <label for="description" class="block text-sm font-medium text-gray-300 mb-2">
                            Chapter Description
                        </label>
                        <textarea id="description" 
                                  name="description" 
                                  rows="4"
                                  class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                  placeholder="Describe what students will learn in this chapter">{{ old('description', $chapter->description) }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Learning Objectives -->
                    <div class="mb-6">
                        <label for="learning_objectives" class="block text-sm font-medium text-gray-300 mb-2">
                            Learning Objectives
                        </label>
                        <div id="objectives-container">
                            @if($chapter->learning_objectives && count($chapter->learning_objectives) > 0)
                                @foreach($chapter->learning_objectives as $index => $objective)
                                    <div class="objective-item flex items-center mb-2">
                                        <input type="text" 
                                               name="learning_objectives[]" 
                                               value="{{ old('learning_objectives.' . $index, $objective) }}"
                                               class="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                               placeholder="Enter learning objective">
                                        <button type="button" onclick="removeObjective(this)" class="ml-2 text-red-500 hover:text-red-400">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                @endforeach
                            @else
                                <div class="objective-item flex items-center mb-2">
                                    <input type="text" 
                                           name="learning_objectives[]" 
                                           value="{{ old('learning_objectives.0') }}"
                                           class="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                           placeholder="Enter learning objective">
                                    <button type="button" onclick="removeObjective(this)" class="ml-2 text-red-500 hover:text-red-400">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            @endif
                        </div>
                        <button type="button" onclick="addObjective()" class="mt-2 text-red-500 hover:text-red-400 text-sm">
                            <i class="fas fa-plus mr-1"></i>Add Learning Objective
                        </button>
                        @error('learning_objectives')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Publication Status -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            Publication Status
                        </label>
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center">
                                <input type="radio" 
                                       name="is_published" 
                                       value="1" 
                                       {{ old('is_published', $chapter->is_published) ? 'checked' : '' }}
                                       class="text-red-500 focus:ring-red-500 focus:ring-2">
                                <span class="ml-2 text-gray-300">Published</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" 
                                       name="is_published" 
                                       value="0" 
                                       {{ !old('is_published', $chapter->is_published) ? 'checked' : '' }}
                                       class="text-red-500 focus:ring-red-500 focus:ring-2">
                                <span class="ml-2 text-gray-300">Draft</span>
                            </label>
                        </div>
                        @error('is_published')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Free Preview -->
                    <div class="mb-8">
                        <label class="flex items-center">
                            <input type="checkbox" 
                                   name="is_free_preview" 
                                   value="1" 
                                   {{ old('is_free_preview', $chapter->is_free_preview) ? 'checked' : '' }}
                                   class="text-red-500 focus:ring-red-500 focus:ring-2">
                            <span class="ml-2 text-gray-300">Allow free preview of this chapter</span>
                        </label>
                        @error('is_free_preview')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center justify-between pt-6 border-t border-gray-800">
                        <a href="{{ route('instructor.courses.show', $course) }}" 
                           class="px-6 py-3 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                            <i class="fas fa-save mr-2"></i>Update Chapter
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function addObjective() {
    const container = document.getElementById('objectives-container');
    const div = document.createElement('div');
    div.className = 'objective-item flex items-center mb-2';
    div.innerHTML = `
        <input type="text" 
               name="learning_objectives[]" 
               class="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
               placeholder="Enter learning objective">
        <button type="button" onclick="removeObjective(this)" class="ml-2 text-red-500 hover:text-red-400">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(div);
}

function removeObjective(button) {
    const container = document.getElementById('objectives-container');
    if (container.children.length > 1) {
        button.closest('.objective-item').remove();
    }
}
</script>
@endpush
@endsection
