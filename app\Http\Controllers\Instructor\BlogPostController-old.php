<?php

namespace App\Http\Controllers\Instructor;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreBlogPostRequest;
use App\Models\BlogPost;
use App\Models\Course;
use App\Services\ContentSanitizer;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class BlogPostController extends Controller
{
    /**
     * Display a listing of blog posts.
     */
    public function index(Request $request)
    {
        $instructor = auth()->user();
        
        $query = $instructor->blogPosts()->with('course');

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('course_id')) {
            $query->where('course_id', $request->course_id);
        }

        if ($request->filled('search')) {
            $query->search($request->search);
        }

        $posts = $query->orderBy('created_at', 'desc')
                      ->paginate(15)
                      ->withQueryString();

        // Get categories and courses for filters
        $categories = $instructor->blogPosts()
            ->whereNotNull('category')
            ->distinct()
            ->pluck('category');
            
        $courses = $instructor->courses()->select('id', 'title')->get();

        return view('instructor.blog-posts.index', compact('posts', 'categories', 'courses'));
    }

    /**
     * Show the form for creating a new blog post.
     */
    public function create(Request $request)
    {
        $instructor = auth()->user();
        $courses = $instructor->courses()->select('id', 'title')->get();

        // Pre-select course if provided in URL
        $selectedCourseId = $request->get('course_id');
        $selectedCourse = null;

        if ($selectedCourseId) {
            $selectedCourse = $courses->firstWhere('id', $selectedCourseId);
        }

        return view('instructor.blog-posts.create', compact('courses', 'selectedCourse'));
    }

    /**
     * Store a newly created blog post.
     */
    public function store(StoreBlogPostRequest $request, ContentSanitizer $sanitizer)
    {
        $instructor = auth()->user();

        // Check authorization
        $this->authorize('create', BlogPost::class);

        $data = $request->validated();
        $data['instructor_id'] = $instructor->id;

        // Sanitize content
        $data['title'] = $sanitizer->sanitizeText($data['title']);
        $data['content'] = $sanitizer->sanitizeHtml($data['content']);

        if (isset($data['excerpt'])) {
            $data['excerpt'] = $sanitizer->sanitizeText($data['excerpt']);
        }

        if (isset($data['category'])) {
            $data['category'] = $sanitizer->sanitizeCategory($data['category']);
        }

        // Process tags
        if ($request->filled('tags')) {
            $tags = array_map('trim', explode(',', $request->tags));
            $data['tags'] = $sanitizer->sanitizeTags($tags);
        }

        // Sanitize SEO meta
        if (isset($data['seo_meta'])) {
            $data['seo_meta'] = $sanitizer->sanitizeSeoMeta($data['seo_meta']);
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $file = $request->file('featured_image');
            $filename = $sanitizer->sanitizeFileName($file->getClientOriginalName());
            $path = $file->storeAs('blog-images', $filename, 'public');
            $data['featured_image'] = $path;
        }

        // Set published_at if publishing
        if ($data['status'] === 'published') {
            $data['published_at'] = now();
        }

        $post = BlogPost::create($data);

        return redirect()->route('instructor.blog-posts.show', $post)
            ->with('success', 'Blog post created successfully.');
    }

    /**
     * Display the specified blog post.
     */
    public function show(BlogPost $blogPost)
    {
        // Check ownership
        if ($blogPost->instructor_id !== auth()->id()) {
            abort(403);
        }

        return view('instructor.blog-posts.show', compact('blogPost'));
    }

    /**
     * Show the form for editing the specified blog post.
     */
    public function edit(BlogPost $blogPost)
    {
        // Check ownership
        if ($blogPost->instructor_id !== auth()->id()) {
            abort(403);
        }

        $instructor = auth()->user();
        $courses = $instructor->courses()->select('id', 'title')->get();

        return view('instructor.blog-posts.edit', compact('blogPost', 'courses'));
    }

    /**
     * Update the specified blog post.
     */
    public function update(Request $request, BlogPost $blogPost)
    {
        // Check ownership
        if ($blogPost->instructor_id !== auth()->id()) {
            abort(403);
        }

        $instructor = auth()->user();
        
        $request->validate([
            'title' => 'required|string|max:255',
            'slug' => [
                'nullable',
                'string',
                'max:255',
                Rule::unique('blog_posts', 'slug')->ignore($blogPost->id)
            ],
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|image|max:2048',
            'course_id' => 'nullable|exists:courses,id',
            'category' => 'nullable|string|max:100',
            'tags' => 'nullable|string',
            'status' => 'required|in:draft,published,archived',
            'allow_comments' => 'boolean',
            'seo_meta' => 'nullable|array',
        ]);

        // Verify course ownership if provided
        if ($request->course_id) {
            Course::where('id', $request->course_id)
                ->where('instructor_id', $instructor->id)
                ->firstOrFail();
        }

        $data = $request->only([
            'title', 'slug', 'excerpt', 'content', 'course_id', 
            'category', 'status', 'allow_comments', 'seo_meta'
        ]);
        
        $data['slug'] = $data['slug'] ?: Str::slug($data['title']);
        
        // Process tags
        if ($request->filled('tags')) {
            $data['tags'] = array_map('trim', explode(',', $request->tags));
        } else {
            $data['tags'] = null;
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image if exists
            if ($blogPost->featured_image) {
                \Storage::disk('public')->delete($blogPost->featured_image);
            }
            
            $path = $request->file('featured_image')->store('blog-images', 'public');
            $data['featured_image'] = $path;
        }

        // Set published_at if publishing for the first time
        if ($data['status'] === 'published' && !$blogPost->published_at) {
            $data['published_at'] = now();
        } elseif ($data['status'] !== 'published') {
            $data['published_at'] = null;
        }

        $blogPost->update($data);

        return redirect()->route('instructor.blog-posts.show', $blogPost)
            ->with('success', 'Blog post updated successfully.');
    }

    /**
     * Remove the specified blog post.
     */
    public function destroy(BlogPost $blogPost)
    {
        // Check ownership
        if ($blogPost->instructor_id !== auth()->id()) {
            abort(403);
        }

        // Delete featured image if exists
        if ($blogPost->featured_image) {
            \Storage::disk('public')->delete($blogPost->featured_image);
        }

        $blogPost->delete();

        return redirect()->route('instructor.blog-posts.index')
            ->with('success', 'Blog post deleted successfully.');
    }

    /**
     * Toggle post status (publish/unpublish).
     */
    public function toggleStatus(BlogPost $blogPost)
    {
        // Check ownership
        if ($blogPost->instructor_id !== auth()->id()) {
            abort(403);
        }

        if ($blogPost->isPublished()) {
            $blogPost->unpublish();
            $message = 'Blog post unpublished successfully.';
        } else {
            $blogPost->publish();
            $message = 'Blog post published successfully.';
        }

        return back()->with('success', $message);
    }
}
