/**
 * Authentication Forms CSS
 * Consistent styling for all authentication forms
 */

/* Password Field Container */
.password-field-container {
    position: relative;
    margin-top: 0.25rem;
}

/* Password Input Field Styling */
.password-input-field {
    width: 100%;
    padding: 0.75rem 3rem 0.75rem 1rem;
    background-color: #1f2937;
    border: 1px solid #374151;
    border-radius: 0.5rem;
    color: #ffffff;
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
}

.password-input-field::placeholder {
    color: #9ca3af;
}

.password-input-field:focus {
    outline: none;
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.password-input-field.error {
    border-color: #ef4444;
}

/* Password Toggle Button Styling */
.password-toggle-btn {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    padding-right: 0.75rem;
    display: flex;
    align-items: center;
    cursor: pointer;
    background: none;
    border: none;
    transition: all 0.2s ease-in-out;
}

.password-toggle-btn:hover {
    transform: scale(1.05);
}

.password-toggle-btn:focus {
    outline: 2px solid #ef4444;
    outline-offset: 2px;
    border-radius: 4px;
}

/* Eye Icon Styling */
.password-eye-icon {
    width: 1.25rem;
    height: 1.25rem;
    color: #9ca3af;
    transition: color 0.2s ease-in-out;
}

.password-eye-icon:hover {
    color: #d1d5db;
}

.password-eye-icon.hidden {
    display: none;
}

/* Form Field Styling */
.auth-form-field {
    margin-bottom: 1rem;
}

.auth-form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #d1d5db;
    margin-bottom: 0.5rem;
}

.auth-form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: #1f2937;
    border: 1px solid #374151;
    border-radius: 0.5rem;
    color: #ffffff;
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
}

.auth-form-input::placeholder {
    color: #9ca3af;
}

.auth-form-input:focus {
    outline: none;
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.auth-form-input.error {
    border-color: #ef4444;
}

.auth-form-error {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #ef4444;
}

.auth-form-success {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #10b981;
}

/* Button Styling */
.auth-form-button {
    width: 100%;
    background-color: #dc2626;
    color: #ffffff;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}

.auth-form-button:hover {
    background-color: #b91c1c;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.auth-form-button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.3);
}

.auth-form-button:active {
    transform: translateY(0);
}

.auth-form-button:disabled {
    background-color: #4b5563;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Secondary Button */
.auth-form-button-secondary {
    width: 100%;
    background-color: #374151;
    color: #ffffff;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}

.auth-form-button-secondary:hover {
    background-color: #4b5563;
}

.auth-form-button-secondary:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(107, 114, 128, 0.3);
}

/* Link Styling */
.auth-form-link {
    color: #f87171;
    transition: color 0.2s ease-in-out;
    text-decoration: none;
}

.auth-form-link:hover {
    color: #fca5a5;
    text-decoration: underline;
}

/* Container Styling */
.auth-form-container {
    background-color: #111827;
    border: 1px solid #1f2937;
    border-radius: 0.5rem;
    padding: 1.5rem;
}

/* Enhanced Form Styling */
.password-field-container .password-input-field {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

/* Better focus states */
.password-input-field:focus,
.auth-form-input:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Improved button interactions */
.password-toggle-btn:active {
    transform: scale(0.95);
}

/* Responsive Design */
@media (max-width: 640px) {
    .password-toggle-btn {
        padding-right: 0.5rem;
    }
    
    .password-input-field {
        padding-right: 2.5rem;
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    .password-eye-icon {
        width: 1rem;
        height: 1rem;
    }
    
    .auth-form-input {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}

/* Dark Theme Enhancements */
@media (prefers-color-scheme: dark) {
    .auth-form-container {
        background: linear-gradient(135deg, rgba(17, 24, 39, 0.95) 0%, rgba(31, 41, 55, 0.95) 100%);
        backdrop-filter: blur(10px);
        border-color: rgba(55, 65, 81, 0.8);
    }
}

/* Animation for password visibility toggle */
@keyframes eyeToggle {
    0% { 
        opacity: 0; 
        transform: scale(0.8) rotate(-10deg); 
    }
    100% { 
        opacity: 1; 
        transform: scale(1) rotate(0deg); 
    }
}

.password-eye-icon:not(.hidden) {
    animation: eyeToggle 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Loading state for forms */
.auth-form-loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Better error states */
.password-input-field.error,
.auth-form-input.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    animation: shake 0.3s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-4px); }
    75% { transform: translateX(4px); }
}