@extends('layouts.app')

@section('title', 'Two-Factor Authentication - Escape Matrix Academy')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-black via-gray-900 to-black py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-white">
                Two-Factor Authentication
            </h2>
            <p class="mt-2 text-center text-sm text-gray-400">
                Please confirm access to your account by entering the authentication code provided by your authenticator application.
            </p>
        </div>

        @if ($errors->any())
            <div class="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded relative" role="alert">
                <ul class="list-disc list-inside">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <div x-data="{ recovery: false }">
            <form class="mt-8 space-y-6" method="POST" action="{{ route('two-factor.login') }}">
                @csrf
                
                <div x-show="! recovery">
                    <label for="code" class="block text-sm font-medium text-gray-300">Authentication Code</label>
                    <input id="code" name="code" type="text" autocomplete="one-time-code" 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-600 placeholder-gray-400 text-white bg-gray-800 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 focus:z-10 sm:text-sm" 
                           placeholder="Enter your authentication code"
                           x-ref="code"
                           autofocus>
                </div>

                <div x-show="recovery" style="display: none;">
                    <label for="recovery_code" class="block text-sm font-medium text-gray-300">Recovery Code</label>
                    <input id="recovery_code" name="recovery_code" type="text" autocomplete="one-time-code" 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-600 placeholder-gray-400 text-white bg-gray-800 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 focus:z-10 sm:text-sm" 
                           placeholder="Enter your recovery code"
                           x-ref="recovery_code">
                </div>

                <div>
                    <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors">
                        <span x-show="! recovery">Verify</span>
                        <span x-show="recovery" style="display: none;">Use Recovery Code</span>
                    </button>
                </div>
                
                <div class="text-center">
                    <button type="button" 
                            class="text-sm text-gray-400 hover:text-white transition-colors"
                            x-show="! recovery"
                            x-on:click="
                                recovery = true;
                                $nextTick(() => { $refs.recovery_code.focus() })
                            ">
                        Use a recovery code
                    </button>

                    <button type="button" 
                            class="text-sm text-gray-400 hover:text-white transition-colors"
                            x-show="recovery"
                            x-on:click="
                                recovery = false;
                                $nextTick(() => { $refs.code.focus() })
                            "
                            style="display: none;">
                        Use an authentication code
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
@endsection
