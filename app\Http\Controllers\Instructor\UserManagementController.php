<?php

namespace App\Http\Controllers\Instructor;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Enrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserManagementController extends Controller
{
    /**
     * Display a listing of users enrolled in instructor's courses.
     */
    public function index(Request $request)
    {
        $instructor = Auth::user();
        
        // Get all enrollments for instructor's courses
        $enrollments = Enrollment::whereHas('course', function ($query) use ($instructor) {
            $query->where('instructor_id', $instructor->id);
        })
        ->with(['user', 'course'])
        ->when($request->search, function ($query, $search) {
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        })
        ->when($request->course_id, function ($query, $courseId) {
            $query->where('course_id', $courseId);
        })
        ->when($request->status, function ($query, $status) {
            $query->where('status', $status);
        })
        ->orderBy('enrolled_at', 'desc')
        ->paginate(20);

        // Get instructor's courses for filter dropdown
        $courses = $instructor->courses()->select('id', 'title')->get();

        return view('instructor.users.index', compact('enrollments', 'courses'));
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        $instructor = Auth::user();
        
        // Check if user is enrolled in any of instructor's courses
        $enrollments = Enrollment::where('user_id', $user->id)
            ->whereHas('course', function ($query) use ($instructor) {
                $query->where('instructor_id', $instructor->id);
            })
            ->with(['course', 'lectureProgress'])
            ->get();

        if ($enrollments->isEmpty()) {
            abort(403, 'You can only view students enrolled in your courses.');
        }

        return view('instructor.users.show', compact('user', 'enrollments'));
    }

    /**
     * Update enrollment status.
     */
    public function updateEnrollmentStatus(Request $request, User $user, Enrollment $enrollment)
    {
        $instructor = Auth::user();
        
        // Verify instructor owns the course
        if ($enrollment->course->instructor_id !== $instructor->id) {
            abort(403, 'You can only manage enrollments for your own courses.');
        }

        $request->validate([
            'status' => 'required|in:active,inactive,suspended,completed,refunded'
        ]);

        $enrollment->update([
            'status' => $request->status
        ]);

        return redirect()->back()->with('success', 'Enrollment status updated successfully.');
    }

    /**
     * Export users data.
     */
    public function export(Request $request)
    {
        $instructor = Auth::user();
        
        $enrollments = Enrollment::whereHas('course', function ($query) use ($instructor) {
            $query->where('instructor_id', $instructor->id);
        })
        ->with(['user', 'course'])
        ->when($request->course_id, function ($query, $courseId) {
            $query->where('course_id', $courseId);
        })
        ->when($request->status, function ($query, $status) {
            $query->where('status', $status);
        })
        ->get();

        $csvData = [];
        $csvData[] = ['Name', 'Email', 'Course', 'Enrolled Date', 'Status', 'Progress', 'Last Accessed'];

        foreach ($enrollments as $enrollment) {
            $csvData[] = [
                $enrollment->user->name,
                $enrollment->user->email,
                $enrollment->course->title,
                $enrollment->enrolled_at->format('Y-m-d'),
                ucfirst($enrollment->status),
                $enrollment->progress_percentage . '%',
                $enrollment->last_accessed_at ? $enrollment->last_accessed_at->format('Y-m-d H:i') : 'Never'
            ];
        }

        $filename = 'students_' . date('Y-m-d') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
