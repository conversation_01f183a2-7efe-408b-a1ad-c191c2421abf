<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use App\Models\CourseCategory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🌱 Starting Laravel LMS Database Seeding...');

        // Create roles
        $this->createRoles();

        // Create permissions
        $this->createPermissions();

        // Create course categories
        $this->createCourseCategories();

        // Create test users
        $this->createTestUsers();

        // Create sample courses with full content
        $this->call(CourseSeeder::class);

        // Create additional students and enrollments
        $this->createAdditionalStudents();

        // Create sample enrollments and payments
        $this->createSampleEnrollments();

        $this->command->info('✅ Database seeding completed!');
        $this->command->info('🎓 Your Laravel LMS is ready for fresh setup!');
    }

    private function createRoles()
    {
        $this->command->info('Creating roles...');

        $roles = [
            [
                'name' => 'student',
                'display_name' => 'Student',
                'description' => 'Students can enroll in courses and access learning materials',
                'priority' => 1
            ],
            [
                'name' => 'instructor',
                'display_name' => 'Instructor',
                'description' => 'Instructors can create and manage courses',
                'priority' => 2
            ],
            [
                'name' => 'admin',
                'display_name' => 'Administrator',
                'description' => 'Administrators can manage users and moderate content',
                'priority' => 3
            ],
            [
                'name' => 'superadmin',
                'display_name' => 'Super Administrator',
                'description' => 'Super administrators have full system access',
                'priority' => 4
            ]
        ];

        foreach ($roles as $roleData) {
            Role::firstOrCreate(
                ['name' => $roleData['name']],
                $roleData
            );
        }
    }

    private function createPermissions()
    {
        $this->command->info('Creating permissions...');

        $permissions = [
            // Course permissions
            ['name' => 'courses.view', 'display_name' => 'View Courses', 'description' => 'View course listings'],
            ['name' => 'courses.create', 'display_name' => 'Create Courses', 'description' => 'Create new courses'],
            ['name' => 'courses.edit', 'display_name' => 'Edit Courses', 'description' => 'Edit existing courses'],
            ['name' => 'courses.delete', 'display_name' => 'Delete Courses', 'description' => 'Delete courses'],
            ['name' => 'courses.publish', 'display_name' => 'Publish Courses', 'description' => 'Publish courses'],

            // User management permissions
            ['name' => 'users.view', 'display_name' => 'View Users', 'description' => 'View user listings'],
            ['name' => 'users.create', 'display_name' => 'Create Users', 'description' => 'Create new users'],
            ['name' => 'users.edit', 'display_name' => 'Edit Users', 'description' => 'Edit user profiles'],
            ['name' => 'users.delete', 'display_name' => 'Delete Users', 'description' => 'Delete users'],

            // Payment permissions
            ['name' => 'payments.view', 'display_name' => 'View Payments', 'description' => 'View payment records'],
            ['name' => 'payments.manage', 'display_name' => 'Manage Payments', 'description' => 'Manage payment settings'],

            // System permissions
            ['name' => 'system.admin', 'display_name' => 'System Administration', 'description' => 'Full system access'],
        ];

        foreach ($permissions as $permissionData) {
            Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                $permissionData
            );
        }

        // Assign permissions to roles
        $this->assignPermissionsToRoles();
    }

    private function assignPermissionsToRoles()
    {
        $this->command->info('Assigning permissions to roles...');

        $student = Role::where('name', 'student')->first();
        $instructor = Role::where('name', 'instructor')->first();
        $admin = Role::where('name', 'admin')->first();
        $superadmin = Role::where('name', 'superadmin')->first();

        // Student permissions
        $student->permissions()->sync(
            Permission::whereIn('name', ['courses.view'])->pluck('id')
        );

        // Instructor permissions
        $instructor->permissions()->sync(
            Permission::whereIn('name', [
                'courses.view', 'courses.create', 'courses.edit', 'courses.publish',
                'payments.view'
            ])->pluck('id')
        );

        // Admin permissions
        $admin->permissions()->sync(
            Permission::whereIn('name', [
                'courses.view', 'courses.create', 'courses.edit', 'courses.delete', 'courses.publish',
                'users.view', 'users.create', 'users.edit',
                'payments.view', 'payments.manage'
            ])->pluck('id')
        );

        // Super admin permissions (all)
        $superadmin->permissions()->sync(Permission::all()->pluck('id'));
    }

    private function createCourseCategories()
    {
        $this->command->info('Creating course categories...');

        $categories = [
            [
                'name' => 'Development',
                'slug' => 'development',
                'description' => 'Programming and software development courses',
                'icon' => 'fas fa-code',
                'color' => '#3B82F6',
                'is_featured' => true,
                'subcategories' => [
                    'Web Development',
                    'Mobile Development',
                    'Game Development',
                    'Data Science',
                    'DevOps'
                ]
            ],
            [
                'name' => 'Business',
                'slug' => 'business',
                'description' => 'Business and entrepreneurship courses',
                'icon' => 'fas fa-briefcase',
                'color' => '#10B981',
                'is_featured' => true,
                'subcategories' => [
                    'Entrepreneurship',
                    'Management',
                    'Marketing',
                    'Finance',
                    'Sales'
                ]
            ],
            [
                'name' => 'Design',
                'slug' => 'design',
                'description' => 'Design and creative courses',
                'icon' => 'fas fa-palette',
                'color' => '#F59E0B',
                'is_featured' => true,
                'subcategories' => [
                    'Graphic Design',
                    'UI/UX Design',
                    'Web Design',
                    'Interior Design',
                    'Fashion Design'
                ]
            ],
            [
                'name' => 'Marketing',
                'slug' => 'marketing',
                'description' => 'Digital marketing and advertising courses',
                'icon' => 'fas fa-bullhorn',
                'color' => '#EF4444',
                'is_featured' => false,
                'subcategories' => [
                    'Digital Marketing',
                    'Social Media Marketing',
                    'Content Marketing',
                    'Email Marketing',
                    'SEO'
                ]
            ]
        ];

        foreach ($categories as $categoryData) {
            $subcategories = $categoryData['subcategories'];
            unset($categoryData['subcategories']);

            $category = CourseCategory::firstOrCreate(
                ['slug' => $categoryData['slug']],
                $categoryData
            );

            // Create subcategories
            foreach ($subcategories as $subName) {
                CourseCategory::firstOrCreate(
                    ['slug' => Str::slug($subName), 'parent_id' => $category->id],
                    [
                        'name' => $subName,
                        'slug' => Str::slug($subName),
                        'description' => $subName . ' courses',
                        'parent_id' => $category->id,
                        'is_active' => true
                    ]
                );
            }
        }
    }

    private function createTestUsers()
    {
        $this->command->info('Creating test users...');

        // Create super admin
        $superadmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'role' => 'superadmin'
            ]
        );
        $superadmin->roles()->sync([Role::where('name', 'superadmin')->first()->id]);

        // Create admin
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'role' => 'admin'
            ]
        );
        $admin->roles()->sync([Role::where('name', 'admin')->first()->id]);

        // Create instructor
        $instructor = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'John Instructor',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'role' => 'instructor',
                'bio' => 'Experienced web developer and instructor with 10+ years in the industry.'
            ]
        );
        $instructor->roles()->sync([Role::where('name', 'instructor')->first()->id]);

        // Create student
        $student = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Jane Student',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'role' => 'student'
            ]
        );
        $student->roles()->sync([Role::where('name', 'student')->first()->id]);

        $this->command->info('Test users created:');
        $this->command->info('- <EMAIL> (password: password)');
        $this->command->info('- <EMAIL> (password: password)');
        $this->command->info('- <EMAIL> (password: password)');
        $this->command->info('- <EMAIL> (password: password)');
    }

    private function createAdditionalStudents()
    {
        $this->command->info('Creating additional student accounts...');

        $students = [
            [
                'name' => 'Alice Johnson',
                'email' => '<EMAIL>',
                'bio' => 'Marketing professional looking to learn web development'
            ],
            [
                'name' => 'Bob Wilson',
                'email' => '<EMAIL>',
                'bio' => 'Recent graduate interested in full-stack development'
            ],
            [
                'name' => 'Carol Davis',
                'email' => '<EMAIL>',
                'bio' => 'Career changer transitioning from finance to tech'
            ],
            [
                'name' => 'David Brown',
                'email' => '<EMAIL>',
                'bio' => 'Entrepreneur building his own startup'
            ],
            [
                'name' => 'Emma Garcia',
                'email' => '<EMAIL>',
                'bio' => 'Designer wanting to learn coding skills'
            ]
        ];

        foreach ($students as $studentData) {
            $student = User::firstOrCreate(
                ['email' => $studentData['email']],
                array_merge($studentData, [
                    'password' => Hash::make('password'),
                    'email_verified_at' => now(),
                    'role' => 'student'
                ])
            );
            $student->roles()->sync([Role::where('name', 'student')->first()->id]);
        }

        $this->command->info('Additional student accounts created.');
    }

    private function createSampleEnrollments()
    {
        $this->command->info('Creating sample enrollments and payments...');

        // Get all students and courses
        $students = User::where('role', 'student')->get();
        $courses = \App\Models\Course::where('status', 'published')->get();

        if ($students->isEmpty() || $courses->isEmpty()) {
            $this->command->warn('No students or courses found. Skipping enrollment creation.');
            return;
        }

        // Create enrollments for each student
        foreach ($students as $student) {
            // Each student enrolls in 1-3 random courses
            $coursesToEnroll = $courses->random(rand(1, min(3, $courses->count())));

            foreach ($coursesToEnroll as $course) {
                // Create payment record
                $paidAt = now()->subDays(rand(1, 30));
                $payment = \App\Models\Payment::create([
                    'user_id' => $student->id,
                    'course_id' => $course->id,
                    'instructor_id' => $course->instructor_id,
                    'amount' => $course->price,
                    'instructor_amount' => $course->price * 0.85, // 85% to instructor, 15% platform fee
                    'platform_fee' => $course->price * 0.15,
                    'processing_fee' => $course->price * 0.03, // 3% processing fee
                    'currency' => $course->currency,
                    'payment_method' => 'paypal',
                    'status' => 'completed',
                    'transaction_id' => 'SAMPLE_' . strtoupper(Str::random(10)),
                    'payment_provider_id' => 'ORDER_' . strtoupper(Str::random(15)),
                    'payment_details' => [
                        'payer_email' => $student->email,
                        'payer_name' => $student->name,
                        'payment_date' => $paidAt->toISOString()
                    ],
                    'paid_at' => $paidAt
                ]);

                // Create enrollment
                $completedLectureIds = $this->generateCompletedLectures($course);
                $enrollment = \App\Models\Enrollment::create([
                    'user_id' => $student->id,
                    'course_id' => $course->id,
                    'instructor_id' => $course->instructor_id,
                    'enrolled_at' => $payment->paid_at,
                    'status' => 'active',
                    'progress_percentage' => rand(0, 100),
                    'completed_lectures' => count($completedLectureIds),
                    'total_lectures' => $course->lectures()->count(),
                    'last_accessed_at' => now()->subDays(rand(0, 7)),
                    'completed_lecture_ids' => $completedLectureIds,
                    'current_lecture_id' => $this->getCurrentLecture($course)
                ]);

                // Update course statistics
                $course->increment('total_students');
            }
        }

        // Update all course statistics
        foreach ($courses as $course) {
            $course->updateStatistics();
        }

        $this->command->info('Sample enrollments and payments created.');
    }

    private function generateCompletedLectures(\App\Models\Course $course)
    {
        $lectures = $course->lectures()->pluck('lectures.id')->toArray();
        if (empty($lectures)) {
            return [];
        }

        // Randomly complete 0-80% of lectures
        $completionRate = rand(0, 80) / 100;
        $lectureCount = count($lectures);
        $completedCount = (int) ($lectureCount * $completionRate);

        return array_slice($lectures, 0, $completedCount);
    }

    private function getCurrentLecture(\App\Models\Course $course)
    {
        $lectures = $course->lectures()->orderBy('lectures.sort_order')->pluck('lectures.id')->toArray();
        if (empty($lectures)) {
            return null;
        }

        // Return a random lecture as current
        return $lectures[rand(0, count($lectures) - 1)];
    }
}
