<?php

namespace App\Http\Controllers\Instructor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ContentFileController extends Controller
{
    public function index()
    {
        return view('instructor.content-files.index');
    }

    public function create()
    {
        return view('instructor.content-files.create');
    }

    public function store(Request $request)
    {
        return redirect()->route('instructor.content-files.index')
            ->with('success', 'Content file created successfully.');
    }

    public function show(string $id)
    {
        return view('instructor.content-files.show');
    }

    public function edit(string $id)
    {
        return view('instructor.content-files.edit');
    }

    public function update(Request $request, string $id)
    {
        return redirect()->route('instructor.content-files.index')
            ->with('success', 'Content file updated successfully.');
    }

    public function destroy(string $id)
    {
        return redirect()->route('instructor.content-files.index')
            ->with('success', 'Content file deleted successfully.');
    }
}
