<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CourseManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $instructor;
    protected $student;
    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed RBAC system
        $this->artisan('db:seed', ['--class' => 'CompleteRbacSeeder']);
        
        // Create test users
        $this->instructor = User::where('email', '<EMAIL>')->first();
        $this->student = User::where('email', '<EMAIL>')->first();
        $this->admin = User::where('email', '<EMAIL>')->first();
    }

    /** @test */
    public function instructor_can_view_course_index()
    {
        $response = $this->actingAs($this->instructor)
            ->get(route('instructor.courses.index'));

        $response->assertStatus(200);
        $response->assertViewIs('instructor.courses.index');
    }

    /** @test */
    public function instructor_can_create_course()
    {
        $courseData = [
            'title' => 'Test Course',
            'description' => 'This is a test course description.',
            'category' => 'Programming',
            'price' => 99.99,
            'level' => 'beginner',
            'duration' => '8 weeks',
            'status' => 'draft',
        ];

        $response = $this->actingAs($this->instructor)
            ->post(route('instructor.courses.store'), $courseData);

        $response->assertRedirect(route('instructor.courses.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('courses', [
            'title' => 'Test Course',
            'instructor_id' => $this->instructor->id,
            'status' => 'draft',
        ]);
    }

    /** @test */
    public function instructor_can_edit_own_course()
    {
        $course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
        ]);

        $response = $this->actingAs($this->instructor)
            ->get(route('instructor.courses.edit', $course));

        $response->assertStatus(200);
        $response->assertViewIs('instructor.courses.edit');
        $response->assertViewHas('course', $course);
    }

    /** @test */
    public function instructor_cannot_edit_other_instructor_course()
    {
        $otherInstructor = User::factory()->create();
        $otherInstructor->assignRole('instructor');
        
        $course = Course::factory()->create([
            'instructor_id' => $otherInstructor->id,
        ]);

        $response = $this->actingAs($this->instructor)
            ->get(route('instructor.courses.edit', $course));

        $response->assertStatus(403);
    }

    /** @test */
    public function instructor_can_update_own_course()
    {
        $course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'title' => 'Original Title',
        ]);

        $updateData = [
            'title' => 'Updated Course Title',
            'description' => $course->description,
            'category' => $course->category,
            'price' => $course->price,
            'level' => $course->level,
            'duration' => $course->duration,
            'status' => $course->status,
        ];

        $response = $this->actingAs($this->instructor)
            ->put(route('instructor.courses.update', $course), $updateData);

        $response->assertRedirect(route('instructor.courses.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('courses', [
            'id' => $course->id,
            'title' => 'Updated Course Title',
        ]);
    }

    /** @test */
    public function instructor_cannot_update_other_instructor_course()
    {
        $otherInstructor = User::factory()->create();
        $otherInstructor->assignRole('instructor');
        
        $course = Course::factory()->create([
            'instructor_id' => $otherInstructor->id,
        ]);

        $updateData = [
            'title' => 'Hacked Title',
            'description' => $course->description,
            'category' => $course->category,
            'price' => $course->price,
            'level' => $course->level,
            'duration' => $course->duration,
            'status' => $course->status,
        ];

        $response = $this->actingAs($this->instructor)
            ->put(route('instructor.courses.update', $course), $updateData);

        $response->assertStatus(403);
        
        // Ensure course wasn't updated
        $this->assertDatabaseMissing('courses', [
            'id' => $course->id,
            'title' => 'Hacked Title',
        ]);
    }

    /** @test */
    public function instructor_can_delete_own_course()
    {
        $course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
        ]);

        $response = $this->actingAs($this->instructor)
            ->delete(route('instructor.courses.destroy', $course));

        $response->assertRedirect(route('instructor.courses.index'));
        $response->assertSessionHas('success');

        $this->assertSoftDeleted('courses', [
            'id' => $course->id,
        ]);
    }

    /** @test */
    public function instructor_cannot_delete_other_instructor_course()
    {
        $otherInstructor = User::factory()->create();
        $otherInstructor->assignRole('instructor');
        
        $course = Course::factory()->create([
            'instructor_id' => $otherInstructor->id,
        ]);

        $response = $this->actingAs($this->instructor)
            ->delete(route('instructor.courses.destroy', $course));

        $response->assertStatus(403);
        
        // Ensure course wasn't deleted
        $this->assertDatabaseHas('courses', [
            'id' => $course->id,
        ]);
    }

    /** @test */
    public function course_creation_requires_valid_data()
    {
        $response = $this->actingAs($this->instructor)
            ->post(route('instructor.courses.store'), []);

        $response->assertSessionHasErrors(['title', 'description']);
    }

    /** @test */
    public function course_price_must_be_numeric()
    {
        $courseData = [
            'title' => 'Test Course',
            'description' => 'This is a test course description.',
            'category' => 'Programming',
            'price' => 'invalid-price',
            'level' => 'beginner',
            'duration' => '8 weeks',
            'status' => 'draft',
        ];

        $response = $this->actingAs($this->instructor)
            ->post(route('instructor.courses.store'), $courseData);

        $response->assertSessionHasErrors(['price']);
    }

    /** @test */
    public function student_cannot_access_course_management()
    {
        $response = $this->actingAs($this->student)
            ->get(route('instructor.courses.index'));

        $response->assertStatus(403);
    }

    /** @test */
    public function guest_cannot_access_course_management()
    {
        $response = $this->get(route('instructor.courses.index'));

        $response->assertRedirect(route('login'));
    }

    /** @test */
    public function instructor_can_toggle_course_status()
    {
        $course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'draft',
        ]);

        $response = $this->actingAs($this->instructor)
            ->patch(route('instructor.courses.toggle-status', $course));

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $course->refresh();
        $this->assertEquals('published', $course->status);
    }

    /** @test */
    public function instructor_can_duplicate_course()
    {
        $course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'title' => 'Original Course',
        ]);

        $response = $this->actingAs($this->instructor)
            ->post(route('instructor.courses.duplicate', $course));

        $response->assertRedirect(route('instructor.courses.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('courses', [
            'instructor_id' => $this->instructor->id,
            'title' => 'Original Course (Copy)',
            'status' => 'draft',
        ]);
    }

    /** @test */
    public function instructor_can_toggle_featured_status()
    {
        $course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'featured' => false,
        ]);

        $response = $this->actingAs($this->instructor)
            ->patch(route('instructor.courses.toggle-featured', $course));

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $course->refresh();
        $this->assertTrue($course->featured);
    }

    /** @test */
    public function admin_can_access_all_courses()
    {
        $course1 = Course::factory()->create(['instructor_id' => $this->instructor->id]);
        $course2 = Course::factory()->create(['instructor_id' => User::factory()->create()->id]);

        $response = $this->actingAs($this->admin)
            ->get(route('instructor.courses.index'));

        $response->assertStatus(200);
        // Admin should see all courses, not just their own
    }
}
