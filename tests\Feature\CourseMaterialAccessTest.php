<?php

namespace Tests\Feature;

use App\Models\Course;
use App\Models\Enrollment;
use App\Models\LearningMaterial;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class CourseMaterialAccessTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $student;
    protected $instructor;
    protected $admin;
    protected $superadmin;
    protected $course;
    protected $material;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed RBAC system
        $this->artisan('db:seed', ['--class' => 'CompleteRbacSeeder']);
        
        // Create test users
        $this->student = User::where('email', '<EMAIL>')->first();
        $this->instructor = User::where('email', '<EMAIL>')->first();
        $this->admin = User::where('email', '<EMAIL>')->first();
        $this->superadmin = User::where('email', '<EMAIL>')->first();
        
        // Create test course
        $this->course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'price' => 99.99,
            'status' => 'published',
        ]);

        // Create test learning material
        Storage::fake('private');
        $file = UploadedFile::fake()->create('test-material.pdf', 1024, 'application/pdf');
        
        $this->material = LearningMaterial::factory()->create([
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'is_published' => true,
            'file_path' => 'private/' . $this->instructor->id . '/' . $this->course->id . '/materials/learning-materials/test-material.pdf',
            'file_name' => 'test-material.pdf',
            'file_size' => 1024 * 1024,
            'mime_type' => 'application/pdf',
        ]);

        // Store the fake file
        Storage::disk('private')->put($this->material->file_path, $file->getContent());
    }

    /** @test */
    public function unauthenticated_users_cannot_access_course_materials()
    {
        $response = $this->get(route('courses.materials', $this->course));
        
        $response->assertRedirect(route('login'));
        $response->assertSessionHas('error', 'Please log in to access course materials.');
    }

    /** @test */
    public function unauthenticated_users_cannot_access_specific_materials()
    {
        $response = $this->get(route('courses.materials.view', [$this->course, $this->material]));
        
        $response->assertRedirect(route('login'));
    }

    /** @test */
    public function unauthenticated_users_cannot_download_materials()
    {
        $response = $this->get(route('courses.materials.download', [$this->course, $this->material]));
        
        $response->assertRedirect(route('login'));
    }

    /** @test */
    public function unpaid_students_cannot_access_paid_course_materials()
    {
        $this->actingAs($this->student);
        
        $response = $this->get(route('courses.materials', $this->course));
        
        $response->assertStatus(403);
    }

    /** @test */
    public function students_without_enrollment_cannot_access_materials()
    {
        $this->actingAs($this->student);
        
        $response = $this->get(route('courses.materials', $this->course));
        
        $response->assertStatus(403);
    }

    /** @test */
    public function enrolled_students_with_valid_payment_can_access_materials()
    {
        // Create enrollment
        Enrollment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'enrolled_at' => now(),
            'status' => 'active',
            'progress' => 0,
            'hours_completed' => 0,
            'last_activity_at' => now()
        ]);

        // Create valid payment
        Payment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'payment_method' => 'paypal',
            'amount' => $this->course->price,
            'instructor_amount' => $this->course->price * 0.8,
            'platform_fee' => $this->course->price * 0.2,
            'currency' => 'USD',
            'status' => Payment::STATUS_COMPLETED,
            'type' => Payment::TYPE_COURSE_PURCHASE,
            'paid_at' => now(),
        ]);

        $this->actingAs($this->student);
        
        $response = $this->get(route('courses.materials', $this->course));
        
        $response->assertStatus(200);
        $response->assertSee($this->material->title);
    }

    /** @test */
    public function enrolled_students_can_view_specific_materials()
    {
        $this->createValidEnrollmentAndPayment();
        
        $this->actingAs($this->student);
        
        $response = $this->get(route('courses.materials.view', [$this->course, $this->material]));
        
        $response->assertStatus(200);
        $response->assertSee($this->material->title);
        $response->assertSee($this->material->description);
    }

    /** @test */
    public function enrolled_students_can_download_materials()
    {
        $this->createValidEnrollmentAndPayment();
        
        $this->actingAs($this->student);
        
        $response = $this->get(route('courses.materials.download', [$this->course, $this->material]));
        
        $response->assertStatus(200);
        $response->assertHeader('content-disposition', 'attachment; filename=test-material.pdf');
    }

    /** @test */
    public function students_with_inactive_enrollment_cannot_access_materials()
    {
        // Create inactive enrollment
        Enrollment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'enrolled_at' => now(),
            'status' => 'inactive',
            'progress' => 0,
            'hours_completed' => 0,
            'last_activity_at' => now()
        ]);

        $this->actingAs($this->student);
        
        $response = $this->get(route('courses.materials', $this->course));
        
        $response->assertStatus(403);
    }

    /** @test */
    public function students_with_failed_payment_cannot_access_materials()
    {
        // Create enrollment
        Enrollment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'enrolled_at' => now(),
            'status' => 'active',
            'progress' => 0,
            'hours_completed' => 0,
            'last_activity_at' => now()
        ]);

        // Create failed payment
        Payment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'payment_method' => 'paypal',
            'amount' => $this->course->price,
            'instructor_amount' => $this->course->price * 0.8,
            'platform_fee' => $this->course->price * 0.2,
            'currency' => 'USD',
            'status' => Payment::STATUS_FAILED,
            'type' => Payment::TYPE_COURSE_PURCHASE,
        ]);

        $this->actingAs($this->student);
        
        $response = $this->get(route('courses.materials', $this->course));
        
        $response->assertStatus(403);
    }

    /** @test */
    public function course_instructors_can_access_their_own_course_materials()
    {
        $this->actingAs($this->instructor);
        
        $response = $this->get(route('courses.materials', $this->course));
        
        $response->assertStatus(200);
        $response->assertSee($this->material->title);
    }

    /** @test */
    public function instructors_cannot_access_other_instructors_course_materials()
    {
        $otherInstructor = User::factory()->create();
        $otherInstructor->assignRole('instructor');
        
        $this->actingAs($otherInstructor);
        
        $response = $this->get(route('courses.materials', $this->course));
        
        $response->assertStatus(403);
    }

    /** @test */
    public function admins_can_access_all_course_materials()
    {
        $this->actingAs($this->admin);
        
        $response = $this->get(route('courses.materials', $this->course));
        
        $response->assertStatus(200);
        $response->assertSee($this->material->title);
    }

    /** @test */
    public function superadmins_can_access_all_course_materials()
    {
        $this->actingAs($this->superadmin);
        
        $response = $this->get(route('courses.materials', $this->course));
        
        $response->assertStatus(200);
        $response->assertSee($this->material->title);
    }

    /** @test */
    public function users_cannot_access_unpublished_materials()
    {
        $this->createValidEnrollmentAndPayment();
        
        // Make material unpublished
        $this->material->update(['is_published' => false]);
        
        $this->actingAs($this->student);
        
        $response = $this->get(route('courses.materials.view', [$this->course, $this->material]));
        
        $response->assertStatus(404);
    }

    /** @test */
    public function users_cannot_access_materials_from_unpublished_courses()
    {
        $this->createValidEnrollmentAndPayment();
        
        // Make course unpublished
        $this->course->update(['status' => 'draft']);
        
        $this->actingAs($this->student);
        
        $response = $this->get(route('courses.materials', $this->course));
        
        $response->assertStatus(403);
    }

    /** @test */
    public function free_course_enrolled_students_can_access_materials()
    {
        // Create free course
        $freeCourse = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'price' => 0,
            'status' => 'published',
        ]);

        $freeMaterial = LearningMaterial::factory()->create([
            'course_id' => $freeCourse->id,
            'instructor_id' => $this->instructor->id,
            'is_published' => true,
        ]);

        // Create enrollment (no payment needed for free course)
        Enrollment::create([
            'user_id' => $this->student->id,
            'course_id' => $freeCourse->id,
            'enrolled_at' => now(),
            'status' => 'active',
            'progress' => 0,
            'hours_completed' => 0,
            'last_activity_at' => now()
        ]);

        $this->actingAs($this->student);
        
        $response = $this->get(route('courses.materials', $freeCourse));
        
        $response->assertStatus(200);
        $response->assertSee($freeMaterial->title);
    }

    /** @test */
    public function secure_file_access_blocks_unauthorized_users()
    {
        $this->actingAs($this->student);

        $response = $this->get(route('secure.files.serve', ['filePath' => $this->material->file_path]));

        $response->assertStatus(403);
    }

    /** @test */
    public function secure_file_access_allows_authorized_users()
    {
        $this->createValidEnrollmentAndPayment();

        $this->actingAs($this->student);

        $response = $this->get(route('secure.files.serve', ['filePath' => $this->material->file_path]));

        $response->assertStatus(200);
    }

    /** @test */
    public function secure_file_download_blocks_unauthorized_users()
    {
        $this->actingAs($this->student);

        $response = $this->get(route('secure.files.download', ['filePath' => $this->material->file_path]));

        $response->assertStatus(403);
    }

    /** @test */
    public function secure_file_download_allows_authorized_users()
    {
        $this->createValidEnrollmentAndPayment();

        $this->actingAs($this->student);

        $response = $this->get(route('secure.files.download', ['filePath' => $this->material->file_path]));

        $response->assertStatus(200);
        $response->assertHeader('content-disposition');
    }

    /** @test */
    public function api_requests_return_json_error_responses()
    {
        $response = $this->getJson(route('courses.materials', $this->course));

        $response->assertStatus(401);
        $response->assertJson(['message' => 'Authentication required to access course materials.']);
    }

    /** @test */
    public function api_requests_return_detailed_access_denied_responses()
    {
        $this->actingAs($this->student);

        $response = $this->getJson(route('courses.materials', $this->course));

        $response->assertStatus(403);
        $response->assertJsonStructure([
            'message',
            'reason',
            'requires_payment'
        ]);
        $response->assertJson(['requires_payment' => true]);
    }

    /** @test */
    public function material_access_logs_are_created_for_rbac_users()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('courses.materials', $this->course));

        $response->assertStatus(200);

        // Check that access was logged (you might need to implement log checking)
        $this->assertTrue(true); // Placeholder for actual log verification
    }

    protected function createValidEnrollmentAndPayment()
    {
        // Create enrollment
        Enrollment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'enrolled_at' => now(),
            'status' => 'active',
            'progress' => 0,
            'hours_completed' => 0,
            'last_activity_at' => now()
        ]);

        // Create valid payment
        Payment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'payment_method' => 'paypal',
            'amount' => $this->course->price,
            'instructor_amount' => $this->course->price * 0.8,
            'platform_fee' => $this->course->price * 0.2,
            'currency' => 'USD',
            'status' => Payment::STATUS_COMPLETED,
            'type' => Payment::TYPE_COURSE_PURCHASE,
            'paid_at' => now(),
        ]);
    }
}
