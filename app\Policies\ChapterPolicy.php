<?php

namespace App\Policies;

use App\Models\Chapter;
use App\Models\Course;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ChapterPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any chapters.
     */
    public function viewAny(User $user, Course $course): bool
    {
        return $user->can('view', $course);
    }

    /**
     * Determine whether the user can view the chapter.
     */
    public function view(User $user, Chapter $chapter): bool
    {
        return $user->can('view', $chapter->course);
    }

    /**
     * Determine whether the user can create chapters.
     */
    public function create(User $user, Course $course): bool
    {
        return $user->can('manageContent', $course);
    }

    /**
     * Determine whether the user can update the chapter.
     */
    public function update(User $user, Chapter $chapter): bool
    {
        return $user->can('manageContent', $chapter->course);
    }

    /**
     * Determine whether the user can delete the chapter.
     */
    public function delete(User $user, Chapter $chapter): bool
    {
        // Can only delete if they can manage content and chapter has no lectures
        return $user->can('manageContent', $chapter->course) && 
               $chapter->lectures()->count() === 0;
    }

    /**
     * Determine whether the user can restore the chapter.
     */
    public function restore(User $user, Chapter $chapter): bool
    {
        return $user->can('manageContent', $chapter->course);
    }

    /**
     * Determine whether the user can permanently delete the chapter.
     */
    public function forceDelete(User $user, Chapter $chapter): bool
    {
        return $user->isSuperAdmin();
    }

    /**
     * Determine whether the user can reorder chapters.
     */
    public function reorder(User $user, Course $course): bool
    {
        return $user->can('manageContent', $course);
    }

    /**
     * Determine whether the user can publish/unpublish the chapter.
     */
    public function publish(User $user, Chapter $chapter): bool
    {
        return $user->can('manageContent', $chapter->course);
    }

    /**
     * Determine whether the user can duplicate the chapter.
     */
    public function duplicate(User $user, Chapter $chapter): bool
    {
        return $user->can('manageContent', $chapter->course);
    }
}
