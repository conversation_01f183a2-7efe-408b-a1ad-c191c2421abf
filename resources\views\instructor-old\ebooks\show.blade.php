@extends('instructor.layouts.app')

@section('title', $ebook->title . ' - Ebooks')

@section('content')
<div class="py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('instructor.ebooks.index') }}" class="text-gray-400 hover:text-white transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <h1 class="text-3xl font-bold text-white">{{ $ebook->title }}</h1>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{{ $ebook->file_url }}" target="_blank" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        View
                    </a>
                    <a href="{{ route('instructor.ebooks.edit', $ebook) }}" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit
                    </a>
                    <form method="POST" action="{{ route('instructor.ebooks.destroy', $ebook) }}" class="inline-block" onsubmit="return confirm('Are you sure you want to delete this ebook?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Status and Meta Info -->
            <div class="flex items-center space-x-6 text-sm text-gray-400">
                <div class="flex items-center space-x-2">
                    <span>Status:</span>
                    @if($ebook->is_published)
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Published
                        </span>
                    @else
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            Draft
                        </span>
                    @endif
                </div>
                <div class="flex items-center space-x-2">
                    <span>Download:</span>
                    @if($ebook->is_downloadable)
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Allowed
                        </span>
                    @else
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            View Only
                        </span>
                    @endif
                </div>
                @if($ebook->author)
                    <div>Author: <span class="text-white">{{ $ebook->author }}</span></div>
                @endif
                <div>Updated: {{ $ebook->updated_at->diffForHumans() }}</div>
            </div>
        </div>

        <!-- Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    @if($ebook->description)
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-white mb-3">Description</h3>
                            <div class="prose prose-invert max-w-none">
                                <p class="text-gray-300">{{ $ebook->description }}</p>
                            </div>
                        </div>
                    @endif

                    <!-- File Preview -->
                    <div>
                        <h3 class="text-lg font-semibold text-white mb-3">File Information</h3>
                        <div class="bg-gray-800 rounded-lg p-4">
                            <div class="flex items-center space-x-4">
                                <div class="flex-shrink-0">
                                    <svg class="w-12 h-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="text-white font-medium">{{ $ebook->file_name }}</h4>
                                    <p class="text-gray-400 text-sm">{{ $ebook->formatted_file_size }} • {{ strtoupper(pathinfo($ebook->file_name, PATHINFO_EXTENSION)) }}</p>
                                </div>
                                <div class="flex space-x-2">
                                    <a href="{{ $ebook->file_url }}" target="_blank" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                        View
                                    </a>
                                    @if($ebook->is_downloadable)
                                        <a href="{{ $ebook->file_url }}" download class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                            Download
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Cover Image -->
                @if($ebook->cover_image_url)
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">Cover</h3>
                        <img src="{{ $ebook->cover_image_url }}" alt="{{ $ebook->title }}" class="w-full rounded-lg">
                    </div>
                @endif

                <!-- Ebook Details -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Details</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Slug:</span>
                            <span class="text-white">{{ $ebook->slug }}</span>
                        </div>
                        @if($ebook->isbn)
                            <div class="flex justify-between">
                                <span class="text-gray-400">ISBN:</span>
                                <span class="text-white">{{ $ebook->isbn }}</span>
                            </div>
                        @endif
                        @if($ebook->publication_date)
                            <div class="flex justify-between">
                                <span class="text-gray-400">Published:</span>
                                <span class="text-white">{{ $ebook->publication_date->format('M j, Y') }}</span>
                            </div>
                        @endif
                        <div class="flex justify-between">
                            <span class="text-gray-400">Created:</span>
                            <span class="text-white">{{ $ebook->created_at->format('M j, Y') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Last Updated:</span>
                            <span class="text-white">{{ $ebook->updated_at->format('M j, Y') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Course Information -->
                @if($ebook->course)
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">Associated Course</h3>
                        <div class="space-y-3">
                            <h4 class="text-white font-medium">{{ $ebook->course->title }}</h4>
                            <p class="text-gray-400 text-sm">{{ Str::limit($ebook->course->description, 100) }}</p>
                            <div class="flex items-center space-x-4 text-sm text-gray-400">
                                <span>{{ ucfirst($ebook->course->level) }}</span>
                                <span>{{ $ebook->course->category }}</span>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
