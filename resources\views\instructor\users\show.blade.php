@extends('layouts.app')

@section('title', 'Student Details - ' . $user->name)

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <a href="{{ route('instructor.users.index') }}" 
                       class="text-blue-600 hover:text-blue-800 mb-2 inline-flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Students
                    </a>
                    <h1 class="text-3xl font-bold text-gray-900">{{ $user->name }}</h1>
                    <p class="text-gray-600 mt-2">{{ $user->email }}</p>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Student Info -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">Student Information</h2>
                    
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-16 w-16">
                                <div class="h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center">
                                    <span class="text-xl font-medium text-gray-700">
                                        {{ substr($user->name, 0, 2) }}
                                    </span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-lg font-medium text-gray-900">{{ $user->name }}</div>
                                <div class="text-sm text-gray-500">{{ $user->email }}</div>
                            </div>
                        </div>
                        
                        <div class="border-t pt-4">
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Member Since</dt>
                                    <dd class="text-sm text-gray-900">{{ $user->created_at->format('M j, Y') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Total Enrollments</dt>
                                    <dd class="text-sm text-gray-900">{{ $enrollments->count() }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Email Verified</dt>
                                    <dd class="text-sm text-gray-900">
                                        @if($user->email_verified_at)
                                            <span class="text-green-600">✓ Verified</span>
                                        @else
                                            <span class="text-red-600">✗ Not Verified</span>
                                        @endif
                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Course Enrollments -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900">Course Enrollments</h2>
                    </div>
                    
                    <div class="divide-y divide-gray-200">
                        @foreach($enrollments as $enrollment)
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">{{ $enrollment->course->title }}</h3>
                                        <p class="text-sm text-gray-500">Enrolled on {{ $enrollment->enrolled_at->format('M j, Y') }}</p>
                                    </div>
                                    <div class="flex items-center space-x-4">
                                        <span class="px-3 py-1 text-sm font-medium rounded-full
                                            @if($enrollment->status === 'active') bg-green-100 text-green-800
                                            @elseif($enrollment->status === 'completed') bg-blue-100 text-blue-800
                                            @elseif($enrollment->status === 'suspended') bg-red-100 text-red-800
                                            @else bg-gray-100 text-gray-800 @endif">
                                            {{ ucfirst($enrollment->status) }}
                                        </span>
                                        
                                        <!-- Status Change Form -->
                                        <form method="POST" action="{{ route('instructor.users.enrollment.status', [$user, $enrollment]) }}" class="inline">
                                            @csrf
                                            @method('PATCH')
                                            <select name="status" onchange="this.form.submit()" 
                                                    class="text-sm border-gray-300 rounded-md">
                                                <option value="active" {{ $enrollment->status === 'active' ? 'selected' : '' }}>Active</option>
                                                <option value="inactive" {{ $enrollment->status === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                                <option value="suspended" {{ $enrollment->status === 'suspended' ? 'selected' : '' }}>Suspended</option>
                                                <option value="completed" {{ $enrollment->status === 'completed' ? 'selected' : '' }}>Completed</option>
                                            </select>
                                        </form>
                                    </div>
                                </div>
                                
                                <!-- Progress -->
                                <div class="mb-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm font-medium text-gray-700">Progress</span>
                                        <span class="text-sm text-gray-500">{{ $enrollment->progress_percentage }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $enrollment->progress_percentage }}%"></div>
                                    </div>
                                </div>
                                
                                <!-- Stats -->
                                <div class="grid grid-cols-3 gap-4 text-center">
                                    <div>
                                        <div class="text-lg font-semibold text-gray-900">{{ $enrollment->completed_lectures }}</div>
                                        <div class="text-sm text-gray-500">Completed Lectures</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-semibold text-gray-900">{{ $enrollment->total_lectures }}</div>
                                        <div class="text-sm text-gray-500">Total Lectures</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-semibold text-gray-900">
                                            {{ $enrollment->last_accessed_at ? $enrollment->last_accessed_at->diffForHumans() : 'Never' }}
                                        </div>
                                        <div class="text-sm text-gray-500">Last Active</div>
                                    </div>
                                </div>
                                
                                @if($enrollment->completed_at)
                                    <div class="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                                        <div class="flex items-center">
                                            <i class="fas fa-check-circle text-green-600 mr-2"></i>
                                            <span class="text-sm text-green-800">
                                                Completed on {{ $enrollment->completed_at->format('M j, Y') }}
                                            </span>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
