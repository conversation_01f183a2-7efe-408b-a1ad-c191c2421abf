@extends('layouts.app')

@section('title', $material->title . ' - ' . $course->title)

@section('content')
<div class="min-h-screen bg-black">
    <!-- Header -->
    <div class="bg-gray-900 border-b border-gray-800">
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center space-x-4 mb-4">
                <a href="{{ route('courses.materials', $course) }}"
                   class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Materials
                </a>
            </div>

            <div class="flex items-start justify-between">
                <div class="flex items-start space-x-4 flex-1">
                    <!-- Material Type Icon -->
                    <div class="flex-shrink-0">
                        @switch($material->type)
                            @case('document')
                                <div class="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center">
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                @break
                            @case('video')
                                <div class="w-16 h-16 bg-red-600 rounded-lg flex items-center justify-center">
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                @break
                            @default
                                <div class="w-16 h-16 bg-gray-600 rounded-lg flex items-center justify-center">
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                </div>
                        @endswitch
                    </div>

                    <div class="flex-1">
                        <h1 class="text-3xl font-bold text-white mb-2">{{ $material->title }}</h1>
                        @if($material->description)
                            <p class="text-gray-300 mb-4">{{ $material->description }}</p>
                        @endif

                        <div class="flex items-center space-x-4 text-sm">
                            <span class="bg-red-600 text-white px-3 py-1 rounded-full">
                                {{ ucfirst($material->type) }}
                            </span>
                            @if($enrollment)
                                <span class="bg-green-600 text-white px-3 py-1 rounded-full">
                                    Enrolled
                                </span>
                                <span class="text-gray-400">
                                    Progress: {{ $enrollment->progress }}%
                                </span>
                            @endif
                            @if($material->hasFile() && $material->formatted_file_size)
                                <span class="text-gray-400">
                                    Size: {{ $material->formatted_file_size }}
                                </span>
                            @endif
                        </div>
                    </div>
                </div>

                @if($material->hasFile())
                    <div class="flex-shrink-0">
                        <a href="{{ route('courses.materials.download', [$course, $material]) }}"
                           class="inline-flex items-center px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m-6 4h8"></path>
                            </svg>
                            Download
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Material Header -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex items-start justify-between">
            <div class="flex-1">
                <div class="flex items-center space-x-3 mb-4">
                    <!-- Material Type Icon -->
                    @switch($material->type)
                        @case('document')
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            @break
                        @case('video')
                            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1M9 10v5a2 2 0 002 2h2a2 2 0 002-2v-5"></path>
                                </svg>
                            </div>
                            @break
                        @case('audio')
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"></path>
                                </svg>
                            </div>
                            @break
                        @default
                            <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                                <svg class="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                    @endswitch

                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ $material->title }}</h1>
                        <div class="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                            <span class="capitalize">{{ $material->type }}</span>
                            @if($material->file_size)
                                <span>{{ number_format($material->file_size / 1024 / 1024, 2) }} MB</span>
                            @endif
                            @if($material->file_name)
                                <span>{{ $material->file_name }}</span>
                            @endif
                        </div>
                    </div>
                </div>

                @if($material->description)
                    <div class="prose max-w-none mb-6">
                        <p class="text-gray-700">{{ $material->description }}</p>
                    </div>
                @endif
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center space-x-3 ml-6">
                @if($material->hasFile())
                    <a href="{{ route('courses.materials.download', [$course, $material]) }}" 
                       class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Download
                    </a>
                @endif

                <a href="{{ route('courses.materials', $course) }}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Materials
                </a>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    @if($material->content)
                        <div class="mb-6">
                            <h3 class="text-xl font-bold text-white mb-4">Content</h3>
                            <div class="prose prose-invert max-w-none">
                                <div class="text-gray-300 whitespace-pre-wrap">{{ $material->content }}</div>
                            </div>
                        </div>
                    @endif

                    @if($material->hasFile())
                        <div class="mb-6">
                            <h3 class="text-xl font-bold text-white mb-4">File Preview</h3>

                            @if($material->type === 'video' && in_array($material->mime_type, ['video/mp4', 'video/webm', 'video/ogg']))
                                <!-- Video Player -->
                                <div class="bg-black rounded-lg overflow-hidden">
                                    <video controls class="w-full h-auto max-h-96" preload="metadata">
                                        <source src="{{ route('secure.files.stream', ['filePath' => $material->file_path]) }}" type="{{ $material->mime_type }}">
                                        Your browser does not support the video tag.
                                    </video>
                                </div>
                            @elseif($material->type === 'audio' && in_array($material->mime_type, ['audio/mp3', 'audio/wav', 'audio/ogg']))
                                <!-- Audio Player -->
                                <div class="bg-gray-800 rounded-lg p-4">
                                    <audio controls class="w-full">
                                        <source src="{{ $material->file_url }}" type="{{ $material->mime_type }}">
                                        Your browser does not support the audio tag.
                                    </audio>
                                </div>
                            @elseif(in_array($material->mime_type, ['image/jpeg', 'image/png', 'image/gif', 'image/webp']))
                                <!-- Image Preview -->
                                <div class="text-center bg-gray-800 rounded-lg p-4">
                                    <img src="{{ $material->file_url }}" alt="{{ $material->title }}" class="max-w-full h-auto rounded-lg shadow-lg mx-auto">
                                </div>
                            @elseif($material->mime_type === 'application/pdf')
                                <!-- PDF Viewer -->
                                <div class="bg-gray-800 rounded-lg p-4">
                                    <iframe src="{{ $material->file_url }}"
                                            class="w-full h-96 rounded border border-gray-700"
                                            title="{{ $material->title }}">
                                        <p class="text-gray-400">Your browser doesn't support PDF viewing.
                                           <a href="{{ $material->download_url }}" class="text-red-500 hover:text-red-400">Download the PDF</a> instead.
                                        </p>
                                    </iframe>
                                </div>
                            @else
                                <!-- Generic File -->
                                <div class="bg-gray-800 rounded-lg p-8 text-center">
                                    <div class="text-6xl mb-4">
                                        @switch(strtolower(pathinfo($material->file_name ?? '', PATHINFO_EXTENSION)))
                                            @case('doc')
                                            @case('docx')
                                                📝
                                                @break
                                            @case('xls')
                                            @case('xlsx')
                                                📊
                                                @break
                                            @case('ppt')
                                            @case('pptx')
                                                📋
                                                @break
                                            @case('zip')
                                            @case('rar')
                                                📦
                                                @break
                                            @default
                                                📎
                                        @endswitch
                                    </div>
                                    <h4 class="text-lg font-medium text-white mb-2">{{ $material->file_name ?? 'File' }}</h4>
                                    <p class="text-gray-400 mb-4">{{ $material->formatted_file_size ?? 'File' }}</p>
                                    <p class="text-gray-500 text-sm mb-4">Preview not available for this file type</p>
                                    <a href="{{ $material->file_url }}" target="_blank"
                                       class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                        </svg>
                                        Open File
                                    </a>
                                </div>
                            @endif
                        </div>
                    @endif
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Material Details -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-6">
                    <h3 class="text-xl font-bold text-white mb-4">Material Details</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Type</span>
                            <span class="text-white">{{ ucfirst($material->type) }}</span>
                        </div>
                        @if($material->hasFile())
                            @if($material->file_name)
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Filename</span>
                                    <span class="text-white text-sm">{{ $material->file_name }}</span>
                                </div>
                            @endif
                            @if($material->formatted_file_size)
                                <div class="flex justify-between">
                                    <span class="text-gray-400">File Size</span>
                                    <span class="text-white">{{ $material->formatted_file_size }}</span>
                                </div>
                            @endif
                            @if($material->mime_type)
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Format</span>
                                    <span class="text-white">{{ strtoupper(pathinfo($material->file_name ?? '', PATHINFO_EXTENSION)) ?: 'File' }}</span>
                                </div>
                            @endif
                        @endif
                    </div>
                </div>

                <!-- Course Info -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    <h3 class="text-xl font-bold text-white mb-4">Course</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Title</span>
                            <span class="text-white">{{ $course->title }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Instructor</span>
                            <span class="text-white">{{ $course->instructor->name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Category</span>
                            <span class="text-white">{{ $course->category }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
