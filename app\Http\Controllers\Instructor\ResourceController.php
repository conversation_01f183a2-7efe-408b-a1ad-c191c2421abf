<?php

namespace App\Http\Controllers\Instructor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ResourceController extends Controller
{
    public function index()
    {
        return view('instructor.resources.index');
    }

    public function create()
    {
        return view('instructor.resources.create');
    }

    public function store(Request $request)
    {
        return redirect()->route('instructor.resources.index')
            ->with('success', 'Resource created successfully.');
    }

    public function show(string $id)
    {
        return view('instructor.resources.show');
    }

    public function edit(string $id)
    {
        return view('instructor.resources.edit');
    }

    public function update(Request $request, string $id)
    {
        return redirect()->route('instructor.resources.index')
            ->with('success', 'Resource updated successfully.');
    }

    public function destroy(string $id)
    {
        return redirect()->route('instructor.resources.index')
            ->with('success', 'Resource deleted successfully.');
    }
}
