@extends('instructor.layouts.app')

@section('title', 'Blog Posts - Instructor Dashboard')

@section('content')
<div class="py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">Blog Posts</h1>
                <p class="text-gray-400">Manage your blog content and share insights with your audience</p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="{{ route('instructor.blog-posts.create') }}" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Create New Post
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-8">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-300 mb-2">Search</label>
                    <input type="text" name="search" id="search" value="{{ request('search') }}" 
                           class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                           placeholder="Search posts...">
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                    <select name="status" id="status" 
                            class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
                        <option value="">All Statuses</option>
                        <option value="1" {{ request('status') === '1' ? 'selected' : '' }}>Published</option>
                        <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>Draft</option>
                    </select>
                </div>

                <!-- Category Filter -->
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-300 mb-2">Category</label>
                    <select name="category" id="category" 
                            class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
                        <option value="">All Categories</option>
                        @foreach($categories as $cat)
                            <option value="{{ $cat }}" {{ request('category') === $cat ? 'selected' : '' }}>{{ ucfirst($cat) }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Filter Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Filter
                    </button>
                </div>
            </form>
        </div>

        <!-- Blog Posts Grid -->
        @if($posts->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($posts as $post)
                    <div class="bg-gray-900 border border-gray-800 rounded-lg overflow-hidden hover:border-red-500 transition-all duration-300">
                        <div class="relative">
                            @if($post->featured_image_url)
                                <img src="{{ $post->featured_image_url }}" alt="{{ $post->title }}" class="w-full h-48 object-cover">
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            <!-- Status Badge -->
                            <div class="absolute top-4 left-4">
                                @if($post->is_published)
                                    <span class="bg-green-600 text-white px-2 py-1 rounded text-sm">Published</span>
                                @else
                                    <span class="bg-yellow-600 text-white px-2 py-1 rounded text-sm">Draft</span>
                                @endif
                            </div>

                            <!-- Category Badge -->
                            @if($post->category)
                                <div class="absolute top-4 right-4">
                                    <span class="bg-blue-600 text-white px-2 py-1 rounded text-sm">{{ ucfirst($post->category) }}</span>
                                </div>
                            @endif
                        </div>

                        <div class="p-6">
                            <h3 class="text-xl font-bold text-white mb-2 line-clamp-2">{{ $post->title }}</h3>
                            @if($post->excerpt)
                                <p class="text-gray-400 mb-4 line-clamp-3">{{ $post->excerpt }}</p>
                            @endif

                            <!-- Post Meta -->
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <span>{{ $post->created_at->format('M d, Y') }}</span>
                                @if($post->course)
                                    <span class="text-blue-400">{{ $post->course->title }}</span>
                                @endif
                            </div>

                            <!-- Tags -->
                            @if($post->tags && count($post->tags) > 0)
                                <div class="flex flex-wrap gap-1 mb-4">
                                    @foreach(array_slice($post->tags, 0, 3) as $tag)
                                        <span class="bg-gray-700 text-gray-300 px-2 py-1 rounded text-xs">{{ $tag }}</span>
                                    @endforeach
                                    @if(count($post->tags) > 3)
                                        <span class="text-gray-500 text-xs">+{{ count($post->tags) - 3 }} more</span>
                                    @endif
                                </div>
                            @endif

                            <!-- Actions -->
                            <div class="flex space-x-2">
                                <a href="{{ route('instructor.blog-posts.show', $post) }}" 
                                   class="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg text-center transition-colors">
                                    View
                                </a>
                                <a href="{{ route('instructor.blog-posts.edit', $post) }}" 
                                   class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                                    Edit
                                </a>
                                <form method="POST" action="{{ route('instructor.blog-posts.toggle-status', $post) }}" class="inline">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit" 
                                            class="{{ $post->is_published ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-green-600 hover:bg-green-700' }} text-white py-2 px-4 rounded-lg transition-colors">
                                        {{ $post->is_published ? 'Unpublish' : 'Publish' }}
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            @if($posts->hasPages())
                <div class="mt-8 flex justify-center">
                    {{ $posts->links() }}
                </div>
            @endif
        @else
            <!-- No Posts -->
            <div class="text-center py-16">
                <div class="text-6xl mb-6">📝</div>
                <h3 class="text-2xl font-bold text-white mb-4">No Blog Posts Found</h3>
                <p class="text-gray-400 mb-8 max-w-md mx-auto">
                    @if(request()->hasAny(['search', 'status', 'category']))
                        No posts match your current filters. Try adjusting your search criteria.
                    @else
                        You haven't created any blog posts yet. Start sharing your knowledge and insights with your audience.
                    @endif
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('instructor.blog-posts.create') }}" class="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                        Create Your First Post
                    </a>
                    @if(request()->hasAny(['search', 'status', 'category']))
                        <a href="{{ route('instructor.blog-posts.index') }}" class="border border-gray-600 text-gray-300 hover:bg-gray-800 px-8 py-3 rounded-lg font-semibold transition-colors">
                            Clear Filters
                        </a>
                    @endif
                </div>
            </div>
        @endif
    </div>
</div>

@push('styles')
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endpush
@endsection
