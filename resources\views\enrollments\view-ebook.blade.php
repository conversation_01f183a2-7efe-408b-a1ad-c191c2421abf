@extends('layouts.app')

@section('title', $ebook->title . ' - ' . $course->title)

@section('content')
<div class="min-h-screen bg-black">
    <!-- Header -->
    <div class="bg-gray-900 border-b border-gray-800">
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center space-x-4 mb-4">
                <a href="{{ route('my-courses.view', $course) }}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Course
                </a>
            </div>
            
            <div class="flex items-start space-x-6">
                @if($ebook->cover_image_url)
                    <div class="flex-shrink-0">
                        <img src="{{ $ebook->cover_image_url }}" alt="{{ $ebook->title }}" 
                             class="w-32 h-40 object-cover rounded-lg shadow-lg">
                    </div>
                @endif
                
                <div class="flex-1">
                    <h1 class="text-3xl font-bold text-white mb-2">{{ $ebook->title }}</h1>
                    @if($ebook->author)
                        <p class="text-gray-400 mb-2">by {{ $ebook->author }}</p>
                    @endif
                    @if($ebook->description)
                        <p class="text-gray-300 mb-4">{{ $ebook->description }}</p>
                    @endif
                    
                    <div class="flex items-center space-x-4 text-sm text-gray-400">
                        @if($ebook->publication_date)
                            <span>Published: {{ $ebook->publication_date->format('M Y') }}</span>
                        @endif
                        @if($ebook->isbn)
                            <span>ISBN: {{ $ebook->isbn }}</span>
                        @endif
                        @if($ebook->formatted_file_size)
                            <span>Size: {{ $ebook->formatted_file_size }}</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    @if($ebook->file_path && str_contains($ebook->mime_type, 'pdf'))
                        <!-- PDF Viewer -->
                        <div class="mb-6">
                            <h3 class="text-xl font-bold text-white mb-4">Read Online</h3>
                            <div class="bg-gray-800 rounded-lg p-4">
                                <iframe src="{{ $ebook->file_url }}" 
                                        class="w-full h-96 rounded border border-gray-700"
                                        title="{{ $ebook->title }}">
                                    <p class="text-gray-400">Your browser doesn't support PDF viewing. 
                                       <a href="{{ $ebook->download_url }}" class="text-red-500 hover:text-red-400">Download the PDF</a> instead.
                                    </p>
                                </iframe>
                            </div>
                        </div>
                    @endif

                    @if($ebook->description)
                        <div class="mb-6">
                            <h3 class="text-xl font-bold text-white mb-4">About This Book</h3>
                            <div class="prose prose-invert max-w-none">
                                <p class="text-gray-300">{{ $ebook->description }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Download Options -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-6">
                    <h3 class="text-xl font-bold text-white mb-4">Download Options</h3>
                    <div class="space-y-3">
                        @if($ebook->is_downloadable && $ebook->file_path)
                            <a href="{{ route('courses.ebooks.download', [$course, $ebook]) }}" 
                               class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition-colors flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m-6 4h8"></path>
                                </svg>
                                Download PDF
                            </a>
                        @else
                            <div class="w-full bg-gray-700 text-gray-400 py-2 px-4 rounded-md text-center">
                                Download not available
                            </div>
                        @endif
                        
                        @if($ebook->file_path)
                            <a href="{{ $ebook->file_url }}" target="_blank"
                               class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                Open in New Tab
                            </a>
                        @endif
                    </div>
                </div>

                <!-- Book Details -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-6">
                    <h3 class="text-xl font-bold text-white mb-4">Book Details</h3>
                    <div class="space-y-3">
                        @if($ebook->author)
                            <div class="flex justify-between">
                                <span class="text-gray-400">Author</span>
                                <span class="text-white">{{ $ebook->author }}</span>
                            </div>
                        @endif
                        @if($ebook->publication_date)
                            <div class="flex justify-between">
                                <span class="text-gray-400">Published</span>
                                <span class="text-white">{{ $ebook->publication_date->format('M d, Y') }}</span>
                            </div>
                        @endif
                        @if($ebook->isbn)
                            <div class="flex justify-between">
                                <span class="text-gray-400">ISBN</span>
                                <span class="text-white">{{ $ebook->isbn }}</span>
                            </div>
                        @endif
                        @if($ebook->formatted_file_size)
                            <div class="flex justify-between">
                                <span class="text-gray-400">File Size</span>
                                <span class="text-white">{{ $ebook->formatted_file_size }}</span>
                            </div>
                        @endif
                        <div class="flex justify-between">
                            <span class="text-gray-400">Format</span>
                            <span class="text-white">{{ strtoupper(pathinfo($ebook->file_name ?? '', PATHINFO_EXTENSION)) ?: 'PDF' }}</span>
                        </div>
                    </div>
                </div>

                <!-- Course Info -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    <h3 class="text-xl font-bold text-white mb-4">Course</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Title</span>
                            <span class="text-white">{{ $course->title }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Instructor</span>
                            <span class="text-white">{{ $course->instructor->name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Category</span>
                            <span class="text-white">{{ $course->category }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
