<?php

namespace Database\Factories;

use App\Models\Course;
use App\Models\LearningMaterial;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LearningMaterial>
 */
class LearningMaterialFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = LearningMaterial::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->sentence(3);
        $types = ['document', 'video', 'audio', 'text'];
        $type = $this->faker->randomElement($types);
        
        return [
            'title' => $title,
            'slug' => Str::slug($title),
            'description' => $this->faker->paragraph(),
            'content' => $this->faker->paragraphs(3, true),
            'type' => $type,
            'file_path' => null,
            'file_name' => null,
            'file_size' => null,
            'mime_type' => null,
            'course_id' => Course::factory(),
            'instructor_id' => User::factory(),
            'sort_order' => $this->faker->numberBetween(1, 100),
            'is_published' => true,
        ];
    }

    /**
     * Indicate that the learning material has a file.
     */
    public function withFile(): static
    {
        return $this->state(function (array $attributes) {
            $fileName = $this->faker->word() . '.pdf';
            $filePath = 'private/' . $attributes['instructor_id'] . '/' . $attributes['course_id'] . '/materials/learning-materials/' . $fileName;
            
            return [
                'file_path' => $filePath,
                'file_name' => $fileName,
                'file_size' => $this->faker->numberBetween(1024, 10485760), // 1KB to 10MB
                'mime_type' => 'application/pdf',
            ];
        });
    }

    /**
     * Indicate that the learning material is unpublished.
     */
    public function unpublished(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_published' => false,
        ]);
    }

    /**
     * Indicate that the learning material is a video.
     */
    public function video(): static
    {
        return $this->state(function (array $attributes) {
            $fileName = $this->faker->word() . '.mp4';
            $filePath = 'private/' . $attributes['instructor_id'] . '/' . $attributes['course_id'] . '/materials/videos/' . $fileName;
            
            return [
                'type' => 'video',
                'file_path' => $filePath,
                'file_name' => $fileName,
                'file_size' => $this->faker->numberBetween(10485760, 104857600), // 10MB to 100MB
                'mime_type' => 'video/mp4',
            ];
        });
    }

    /**
     * Indicate that the learning material is an audio file.
     */
    public function audio(): static
    {
        return $this->state(function (array $attributes) {
            $fileName = $this->faker->word() . '.mp3';
            $filePath = 'private/' . $attributes['instructor_id'] . '/' . $attributes['course_id'] . '/materials/audio/' . $fileName;
            
            return [
                'type' => 'audio',
                'file_path' => $filePath,
                'file_name' => $fileName,
                'file_size' => $this->faker->numberBetween(1048576, 52428800), // 1MB to 50MB
                'mime_type' => 'audio/mp3',
            ];
        });
    }

    /**
     * Indicate that the learning material is a document.
     */
    public function document(): static
    {
        return $this->state(function (array $attributes) {
            $fileName = $this->faker->word() . '.pdf';
            $filePath = 'private/' . $attributes['instructor_id'] . '/' . $attributes['course_id'] . '/materials/documents/' . $fileName;
            
            return [
                'type' => 'document',
                'file_path' => $filePath,
                'file_name' => $fileName,
                'file_size' => $this->faker->numberBetween(1024, 10485760), // 1KB to 10MB
                'mime_type' => 'application/pdf',
            ];
        });
    }

    /**
     * Indicate that the learning material is text-only (no file).
     */
    public function textOnly(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'text',
            'file_path' => null,
            'file_name' => null,
            'file_size' => null,
            'mime_type' => null,
        ]);
    }
}
