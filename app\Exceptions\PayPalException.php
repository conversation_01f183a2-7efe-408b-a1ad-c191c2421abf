<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Support\Facades\Log;

class PayPalException extends Exception
{
    protected $paypalError;
    protected $context;

    public function __construct(string $message, array $paypalError = [], array $context = [], int $code = 0, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
        
        $this->paypalError = $paypalError;
        $this->context = $context;
    }

    /**
     * Get PayPal specific error details
     */
    public function getPayPalError(): array
    {
        return $this->paypalError;
    }

    /**
     * Get additional context
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Log the exception with PayPal specific details
     */
    public function logError(): void
    {
        Log::error('PayPal Exception: ' . $this->getMessage(), [
            'paypal_error' => $this->paypalError,
            'context' => $this->context,
            'trace' => $this->getTraceAsString()
        ]);
    }

    /**
     * Get user-friendly error message
     */
    public function getUserMessage(): string
    {
        // Map PayPal errors to user-friendly messages
        $errorMappings = [
            'INVALID_REQUEST' => 'There was an issue with your payment request. Please try again.',
            'AUTHENTICATION_FAILURE' => 'Payment service temporarily unavailable. Please try again later.',
            'INSUFFICIENT_FUNDS' => 'Insufficient funds in your account. Please check your payment method.',
            'PAYMENT_DENIED' => 'Your payment was declined. Please try a different payment method.',
            'PAYMENT_ALREADY_DONE' => 'This payment has already been processed.',
            'DUPLICATE_INVOICE_ID' => 'This transaction has already been processed.',
            'INSTRUMENT_DECLINED' => 'Your payment method was declined. Please try a different payment method.',
            'PAYER_ACCOUNT_RESTRICTED' => 'Your PayPal account has restrictions. Please contact PayPal support.',
            'PAYEE_ACCOUNT_RESTRICTED' => 'Payment cannot be processed at this time. Please contact support.',
            'TRANSACTION_LIMIT_EXCEEDED' => 'Transaction limit exceeded. Please try a smaller amount.',
        ];

        // Check if we have a specific PayPal error
        if (!empty($this->paypalError['name'])) {
            $errorName = $this->paypalError['name'];
            if (isset($errorMappings[$errorName])) {
                return $errorMappings[$errorName];
            }
        }

        // Check for common error patterns in the message
        $message = strtolower($this->getMessage());
        
        if (str_contains($message, 'network') || str_contains($message, 'timeout')) {
            return 'Network connection issue. Please check your internet connection and try again.';
        }
        
        if (str_contains($message, 'unauthorized') || str_contains($message, 'authentication')) {
            return 'Payment service temporarily unavailable. Please try again later.';
        }
        
        if (str_contains($message, 'invalid') || str_contains($message, 'malformed')) {
            return 'There was an issue processing your payment. Please try again.';
        }

        // Default user-friendly message
        return 'We encountered an issue processing your payment. Please try again or contact support if the problem persists.';
    }

    /**
     * Create exception from PayPal API response
     */
    public static function fromApiResponse(array $response, array $context = []): self
    {
        $message = 'PayPal API Error';
        $paypalError = [];

        // Extract error information from different PayPal response formats
        if (isset($response['error'])) {
            $error = $response['error'];
            $message = $error['message'] ?? $message;
            $paypalError = $error;
        } elseif (isset($response['details'])) {
            $details = $response['details'][0] ?? [];
            $message = $details['description'] ?? $message;
            $paypalError = $details;
        } elseif (isset($response['message'])) {
            $message = $response['message'];
            $paypalError = $response;
        }

        return new self($message, $paypalError, $context);
    }

    /**
     * Create exception for network/connection errors
     */
    public static function networkError(string $details, array $context = []): self
    {
        return new self(
            'PayPal network error: ' . $details,
            ['name' => 'NETWORK_ERROR', 'message' => $details],
            $context
        );
    }

    /**
     * Create exception for timeout errors
     */
    public static function timeoutError(array $context = []): self
    {
        return new self(
            'PayPal request timeout',
            ['name' => 'TIMEOUT_ERROR', 'message' => 'Request timed out'],
            $context
        );
    }

    /**
     * Create exception for configuration errors
     */
    public static function configurationError(string $details, array $context = []): self
    {
        return new self(
            'PayPal configuration error: ' . $details,
            ['name' => 'CONFIGURATION_ERROR', 'message' => $details],
            $context
        );
    }

    /**
     * Create exception for webhook verification errors
     */
    public static function webhookVerificationError(string $details, array $context = []): self
    {
        return new self(
            'PayPal webhook verification failed: ' . $details,
            ['name' => 'WEBHOOK_VERIFICATION_ERROR', 'message' => $details],
            $context
        );
    }

    /**
     * Check if error is retryable
     */
    public function isRetryable(): bool
    {
        $retryableErrors = [
            'NETWORK_ERROR',
            'TIMEOUT_ERROR',
            'INTERNAL_SERVICE_ERROR',
            'SERVICE_UNAVAILABLE',
            'RATE_LIMIT_REACHED'
        ];

        $errorName = $this->paypalError['name'] ?? '';
        return in_array($errorName, $retryableErrors);
    }

    /**
     * Get recommended retry delay in seconds
     */
    public function getRetryDelay(): int
    {
        $errorName = $this->paypalError['name'] ?? '';
        
        switch ($errorName) {
            case 'RATE_LIMIT_REACHED':
                return 60; // 1 minute
            case 'SERVICE_UNAVAILABLE':
                return 30; // 30 seconds
            case 'TIMEOUT_ERROR':
            case 'NETWORK_ERROR':
                return 5; // 5 seconds
            default:
                return 10; // 10 seconds default
        }
    }

    /**
     * Convert to array for API responses
     */
    public function toArray(): array
    {
        return [
            'error' => true,
            'message' => $this->getUserMessage(),
            'code' => $this->getCode(),
            'paypal_error' => $this->paypalError,
            'retryable' => $this->isRetryable(),
            'retry_delay' => $this->isRetryable() ? $this->getRetryDelay() : null,
            'timestamp' => now()->toISOString()
        ];
    }
}
