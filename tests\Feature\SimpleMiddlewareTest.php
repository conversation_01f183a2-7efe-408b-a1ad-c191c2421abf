<?php

namespace Tests\Feature;

use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class SimpleMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    protected User $student;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        $studentRole = Role::create([
            'name' => 'student',
            'display_name' => 'Student',
            'description' => 'Student',
            'priority' => 1,
            'is_active' => true,
        ]);

        // Create users
        $this->student = User::factory()->create();
        $this->student->roles()->attach($studentRole);

        // Set up storage disks
        Storage::fake('private');
    }

    /** @test */
    public function middleware_denies_access_to_non_existent_files()
    {
        $this->actingAs($this->student);

        $response = $this->get(route('secure.files.serve', ['filePath' => 'private/123/456/course-images/nonexistent.jpg']));
        
        // Should get 404 for non-existent file
        $response->assertStatus(404);
    }

    /** @test */
    public function middleware_denies_access_to_unauthorized_files()
    {
        $this->actingAs($this->student);

        // Create a file that the student shouldn't have access to
        $filePath = 'private/999/888/materials/secret.pdf';
        Storage::disk('private')->put($filePath, 'secret content');

        $response = $this->get(route('secure.files.serve', ['filePath' => $filePath]));
        
        // Should get 403 for unauthorized access
        $response->assertStatus(403);
    }

    /** @test */
    public function middleware_allows_access_to_authorized_files()
    {
        $this->actingAs($this->student);

        // Create a file in the student's directory
        $filePath = "private/{$this->student->id}/123/materials/allowed.pdf";
        Storage::disk('private')->put($filePath, 'allowed content');

        $response = $this->get(route('secure.files.serve', ['filePath' => $filePath]));
        
        // Should get 200 for authorized access
        $response->assertStatus(200);
    }
}
