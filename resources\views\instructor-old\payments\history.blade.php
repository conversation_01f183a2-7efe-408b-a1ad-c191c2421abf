@extends('instructor.layouts.app')

@section('title', 'Payment History - Instructor Dashboard')

@section('content')
<div class="py-12">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">Payment History</h1>
                <p class="text-gray-400">Detailed view of all your payment transactions</p>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-4">
                <a href="{{ route('instructor.payments.index') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                </a>
                <a href="{{ route('instructor.payments.export', request()->query()) }}" 
                   class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-download mr-2"></i>Export CSV
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-8">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                    <select name="status" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500">
                        <option value="">All Statuses</option>
                        <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Completed</option>
                        <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="failed" {{ request('status') === 'failed' ? 'selected' : '' }}>Failed</option>
                        <option value="refunded" {{ request('status') === 'refunded' ? 'selected' : '' }}>Refunded</option>
                        <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Course</label>
                    <select name="course_id" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500">
                        <option value="">All Courses</option>
                        @foreach($courses as $course)
                            <option value="{{ $course->id }}" {{ request('course_id') === $course->id ? 'selected' : '' }}>
                                {{ $course->title }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">From Date</label>
                    <input type="date" name="date_from" value="{{ request('date_from') }}" 
                           class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">To Date</label>
                    <input type="date" name="date_to" value="{{ request('date_to') }}" 
                           class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500">
                </div>

                <div class="flex items-end">
                    <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-filter mr-2"></i>Filter
                    </button>
                </div>
            </form>

            @if(request()->hasAny(['status', 'course_id', 'date_from', 'date_to', 'search']))
                <div class="mt-4 pt-4 border-t border-gray-800">
                    <div class="flex items-center justify-between">
                        <div class="flex flex-wrap gap-2">
                            @if(request('status'))
                                <span class="bg-blue-900 text-blue-300 px-2 py-1 rounded text-sm">
                                    Status: {{ ucfirst(request('status')) }}
                                </span>
                            @endif
                            @if(request('course_id'))
                                <span class="bg-purple-900 text-purple-300 px-2 py-1 rounded text-sm">
                                    Course: {{ $courses->firstWhere('id', request('course_id'))->title ?? 'Unknown' }}
                                </span>
                            @endif
                            @if(request('date_from'))
                                <span class="bg-green-900 text-green-300 px-2 py-1 rounded text-sm">
                                    From: {{ request('date_from') }}
                                </span>
                            @endif
                            @if(request('date_to'))
                                <span class="bg-green-900 text-green-300 px-2 py-1 rounded text-sm">
                                    To: {{ request('date_to') }}
                                </span>
                            @endif
                        </div>
                        <a href="{{ route('instructor.payments.history') }}" class="text-red-400 hover:text-red-300 text-sm">
                            Clear Filters
                        </a>
                    </div>
                </div>
            @endif
        </div>

        <!-- Payments Table -->
        @if($payments->count() > 0)
            <div class="bg-gray-900 border border-gray-800 rounded-lg overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-800">
                            <tr>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Student</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Course</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Method</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Transaction ID</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-800">
                            @foreach($payments as $payment)
                                <tr class="hover:bg-gray-800 transition-colors">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div>
                                            <div class="text-sm font-medium text-white">{{ $payment->user->name }}</div>
                                            <div class="text-sm text-gray-400">{{ $payment->user->email }}</div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-white">{{ $payment->course->title }}</div>
                                        <div class="text-sm text-gray-400">{{ $payment->course->category }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-white">${{ number_format($payment->instructor_amount, 2) }}</div>
                                        <div class="text-xs text-gray-400">
                                            Total: ${{ number_format($payment->amount, 2) }}
                                            @if($payment->platform_fee > 0)
                                                <br>Fee: ${{ number_format($payment->platform_fee, 2) }}
                                            @endif
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center space-x-2">
                                            @if($payment->payment_method === 'paypal')
                                                <svg class="w-5 h-5 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.028-.026.056-.052.08-.65 3.85-3.197 5.341-6.957 5.341h-2.504c-.524 0-.968.382-1.05.9L8.937 19.9c-.013.06-.004.119.021.176.067.153.211.261.379.261h2.94c.458 0 .848-.334.922-.788l.04-.207.738-4.68.047-.257c.075-.453.465-.788.922-.788h.58c3.57 0 6.36-1.45 7.17-5.64.34-1.75.17-3.21-.72-4.25-.27-.31-.61-.56-1.01-.72z"/>
                                                </svg>
                                            @endif
                                            <span class="text-sm text-white">{{ ucfirst($payment->payment_method ?? 'PayPal') }}</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-medium rounded-full
                                            @if($payment->status === 'completed') bg-green-900 text-green-300
                                            @elseif($payment->status === 'pending') bg-yellow-900 text-yellow-300
                                            @elseif($payment->status === 'failed') bg-red-900 text-red-300
                                            @elseif($payment->status === 'refunded') bg-blue-900 text-blue-300
                                            @else bg-gray-900 text-gray-300 @endif">
                                            {{ ucfirst($payment->status) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-white">{{ $payment->created_at->format('M j, Y') }}</div>
                                        <div class="text-sm text-gray-400">{{ $payment->created_at->format('g:i A') }}</div>
                                        @if($payment->paid_at && $payment->status === 'completed')
                                            <div class="text-xs text-green-400">
                                                Paid: {{ $payment->paid_at->format('M j, g:i A') }}
                                            </div>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @if($payment->payment_id)
                                            <div class="text-sm text-white font-mono">{{ Str::limit($payment->payment_id, 20) }}</div>
                                            @if($payment->metadata && isset($payment->metadata['paypal_capture_id']))
                                                <div class="text-xs text-gray-400">
                                                    Capture: {{ Str::limit($payment->metadata['paypal_capture_id'], 15) }}
                                                </div>
                                            @endif
                                        @else
                                            <span class="text-gray-500 text-sm">N/A</span>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $payments->withQueryString()->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-12 text-center">
                <div class="mb-6">
                    <i class="fas fa-receipt text-6xl text-gray-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">No payments found</h3>
                <p class="text-gray-400 mb-6">
                    @if(request()->hasAny(['status', 'course_id', 'date_from', 'date_to']))
                        No payments match your current filters. Try adjusting your search criteria.
                    @else
                        You haven't received any payments yet. Start promoting your courses to get your first sale!
                    @endif
                </p>
                @if(request()->hasAny(['status', 'course_id', 'date_from', 'date_to']))
                    <a href="{{ route('instructor.payments.history') }}" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        Clear Filters
                    </a>
                @endif
            </div>
        @endif
    </div>
</div>
@endsection

@push('styles')
<style>
.pagination {
    @apply flex justify-center space-x-1;
}

.pagination .page-link {
    @apply px-3 py-2 text-gray-400 bg-gray-800 border border-gray-700 hover:bg-gray-700 hover:text-white transition-colors;
}

.pagination .page-item.active .page-link {
    @apply bg-red-600 text-white border-red-600;
}

.pagination .page-item.disabled .page-link {
    @apply text-gray-600 cursor-not-allowed;
}
</style>
@endpush
