<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->isAdmin() && !auth()->user()->isSuperAdmin()) {
                abort(403, 'Access denied. Administrator privileges required.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of users.
     */
    public function index(Request $request)
    {
        $query = User::with(['activeRoles']);

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('role')) {
            $query->whereHas('activeRoles', function ($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        if ($request->filled('status')) {
            if ($request->status === 'verified') {
                $query->whereNotNull('email_verified_at');
            } elseif ($request->status === 'unverified') {
                $query->whereNull('email_verified_at');
            }
        }

        $users = $query->orderBy('created_at', 'desc')
                      ->paginate(20)
                      ->withQueryString();

        $roles = Role::active()->orderBy('priority', 'desc')->get();

        return view('admin.users.index', compact('users', 'roles'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        $roles = Role::active()->orderBy('priority', 'desc')->get();
        return view('admin.users.create', compact('roles'));
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'roles' => 'required|array|min:1',
            'roles.*' => 'exists:roles,id',
            'bio' => 'nullable|string|max:1000',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'bio' => $request->bio,
            'email_verified_at' => now(), // Auto-verify admin-created users
        ]);

        // Assign roles
        foreach ($request->roles as $roleId) {
            $role = Role::find($roleId);
            if ($role) {
                $user->roles()->attach($roleId, [
                    'assigned_at' => now(),
                    'assigned_by' => auth()->id(),
                    'notes' => 'Assigned by administrator during user creation',
                ]);
            }
        }

        // Set legacy role field for backward compatibility
        $primaryRole = Role::whereIn('id', $request->roles)->orderBy('priority', 'desc')->first();
        if ($primaryRole) {
            $user->update(['role' => $primaryRole->name]);
        }

        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        $user->load(['activeRoles', 'courses', 'enrollments.course']);
        
        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user)
    {
        // Prevent non-superadmin from editing superadmin users
        if (!auth()->user()->isSuperAdmin() && $user->isSuperAdmin()) {
            abort(403, 'Only super administrators can edit super admin users.');
        }

        $user->load(['activeRoles']);
        $roles = Role::active()->orderBy('priority', 'desc')->get();
        $userRoleIds = $user->activeRoles->pluck('id')->toArray();

        return view('admin.users.edit', compact('user', 'roles', 'userRoleIds'));
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, User $user)
    {
        // Prevent non-superadmin from editing superadmin users
        if (!auth()->user()->isSuperAdmin() && $user->isSuperAdmin()) {
            abort(403, 'Only super administrators can edit super admin users.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'roles' => 'required|array|min:1',
            'roles.*' => 'exists:roles,id',
            'bio' => 'nullable|string|max:1000',
        ]);

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'bio' => $request->bio,
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        // Sync roles
        $user->roles()->detach(); // Remove all current roles
        foreach ($request->roles as $roleId) {
            $role = Role::find($roleId);
            if ($role) {
                $user->roles()->attach($roleId, [
                    'assigned_at' => now(),
                    'assigned_by' => auth()->id(),
                    'notes' => 'Updated by administrator',
                ]);
            }
        }

        // Update legacy role field for backward compatibility
        $primaryRole = Role::whereIn('id', $request->roles)->orderBy('priority', 'desc')->first();
        if ($primaryRole) {
            $user->update(['role' => $primaryRole->name]);
        }

        return redirect()->route('admin.users.index')
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(User $user)
    {
        // Prevent deletion of superadmin users by non-superadmin
        if (!auth()->user()->isSuperAdmin() && $user->isSuperAdmin()) {
            abort(403, 'Only super administrators can delete super admin users.');
        }

        // Prevent users from deleting themselves
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users.index')
                ->with('error', 'You cannot delete your own account.');
        }

        // Check if user has courses or enrollments
        if ($user->courses()->count() > 0) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Cannot delete user who has created courses. Transfer or delete courses first.');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Assign role to user.
     */
    public function assignRole(Request $request, User $user)
    {
        $request->validate([
            'role_id' => 'required|exists:roles,id',
            'notes' => 'nullable|string|max:500',
        ]);

        $role = Role::find($request->role_id);
        
        if ($user->roles()->where('role_id', $role->id)->exists()) {
            return redirect()->route('admin.users.show', $user)
                ->with('error', 'User already has this role.');
        }

        $user->roles()->attach($role->id, [
            'assigned_at' => now(),
            'assigned_by' => auth()->id(),
            'notes' => $request->notes ?? 'Assigned by administrator',
        ]);

        return redirect()->route('admin.users.show', $user)
            ->with('success', 'Role assigned successfully.');
    }

    /**
     * Remove role from user.
     */
    public function removeRole(User $user, Role $role)
    {
        // Prevent removing the last role
        if ($user->activeRoles()->count() <= 1) {
            return redirect()->route('admin.users.show', $user)
                ->with('error', 'Cannot remove the last role from user.');
        }

        $user->roles()->detach($role->id);

        return redirect()->route('admin.users.show', $user)
            ->with('success', 'Role removed successfully.');
    }

    /**
     * Get user statistics.
     */
    public function statistics()
    {
        $stats = [
            'total_users' => User::count(),
            'verified_users' => User::whereNotNull('email_verified_at')->count(),
            'students' => User::whereHas('activeRoles', function ($q) {
                $q->where('name', Role::STUDENT);
            })->count(),
            'instructors' => User::whereHas('activeRoles', function ($q) {
                $q->where('name', Role::INSTRUCTOR);
            })->count(),
            'admins' => User::whereHas('activeRoles', function ($q) {
                $q->whereIn('name', [Role::ADMIN, Role::SUPERADMIN]);
            })->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Export users data.
     */
    public function export(Request $request)
    {
        $query = User::with(['activeRoles']);

        // Apply same filters as index
        if ($request->filled('role')) {
            $query->whereHas('activeRoles', function ($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        $users = $query->orderBy('created_at', 'desc')->get();

        $filename = 'users_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($users) {
            $file = fopen('php://output', 'w');
            
            fputcsv($file, [
                'ID', 'Name', 'Email', 'Roles', 'Email Verified', 'Created At', 'Last Login'
            ]);

            foreach ($users as $user) {
                fputcsv($file, [
                    $user->id,
                    $user->name,
                    $user->email,
                    $user->activeRoles->pluck('display_name')->implode(', '),
                    $user->email_verified_at ? 'Yes' : 'No',
                    $user->created_at->format('Y-m-d H:i:s'),
                    $user->last_login_at ? $user->last_login_at->format('Y-m-d H:i:s') : 'Never',
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
