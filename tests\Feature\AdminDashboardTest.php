<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use App\Models\Course;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AdminDashboardTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $superadmin;
    protected $instructor;
    protected $student;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed RBAC system
        $this->artisan('db:seed', ['--class' => 'CompleteRbacSeeder']);
        
        // Get test users
        $this->admin = User::where('email', '<EMAIL>')->first();
        $this->superadmin = User::where('email', '<EMAIL>')->first();
        $this->instructor = User::where('email', '<EMAIL>')->first();
        $this->student = User::where('email', '<EMAIL>')->first();
    }

    /** @test */
    public function admin_can_access_dashboard()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.dashboard');
    }

    /** @test */
    public function superadmin_can_access_dashboard()
    {
        $response = $this->actingAs($this->superadmin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.dashboard');
    }

    /** @test */
    public function instructor_cannot_access_admin_dashboard()
    {
        $response = $this->actingAs($this->instructor)
            ->get(route('admin.dashboard'));

        $response->assertStatus(403);
    }

    /** @test */
    public function student_cannot_access_admin_dashboard()
    {
        $response = $this->actingAs($this->student)
            ->get(route('admin.dashboard'));

        $response->assertStatus(403);
    }

    /** @test */
    public function admin_can_view_roles_index()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.roles.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.roles.index');
        $response->assertViewHas('roles');
    }

    /** @test */
    public function admin_can_create_new_role()
    {
        $roleData = [
            'name' => 'test_role',
            'display_name' => 'Test Role',
            'description' => 'A test role for testing purposes',
            'priority' => 50,
            'is_active' => true,
            'permissions' => [Permission::first()->id],
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.roles.store'), $roleData);

        $response->assertRedirect(route('admin.roles.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('roles', [
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);
    }

    /** @test */
    public function admin_can_edit_role()
    {
        $role = Role::where('name', 'instructor')->first();

        $response = $this->actingAs($this->admin)
            ->get(route('admin.roles.edit', $role));

        $response->assertStatus(200);
        $response->assertViewIs('admin.roles.edit');
        $response->assertViewHas('role', $role);
    }

    /** @test */
    public function admin_cannot_edit_superadmin_role()
    {
        $superadminRole = Role::where('name', 'superadmin')->first();

        $response = $this->actingAs($this->admin)
            ->get(route('admin.roles.edit', $superadminRole));

        $response->assertStatus(403);
    }

    /** @test */
    public function superadmin_can_edit_any_role()
    {
        $superadminRole = Role::where('name', 'superadmin')->first();

        $response = $this->actingAs($this->superadmin)
            ->get(route('admin.roles.edit', $superadminRole));

        $response->assertStatus(200);
    }

    /** @test */
    public function admin_can_view_users_index()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.users.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.users.index');
        $response->assertViewHas('users');
    }

    /** @test */
    public function admin_can_create_new_user()
    {
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
            'roles' => [Role::where('name', 'student')->first()->id],
            'bio' => 'Test user bio',
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.users.store'), $userData);

        $response->assertRedirect(route('admin.users.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('users', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertTrue($user->hasRole('student'));
    }

    /** @test */
    public function admin_can_edit_user()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.users.edit', $this->student));

        $response->assertStatus(200);
        $response->assertViewIs('admin.users.edit');
        $response->assertViewHas('user', $this->student);
    }

    /** @test */
    public function admin_cannot_edit_superadmin_user()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.users.edit', $this->superadmin));

        $response->assertStatus(403);
    }

    /** @test */
    public function superadmin_can_edit_any_user()
    {
        $response = $this->actingAs($this->superadmin)
            ->get(route('admin.users.edit', $this->superadmin));

        $response->assertStatus(200);
    }

    /** @test */
    public function admin_can_assign_role_to_user()
    {
        $user = User::factory()->create();
        $user->assignRole('student');
        
        $instructorRole = Role::where('name', 'instructor')->first();

        $response = $this->actingAs($this->admin)
            ->post(route('admin.users.assign-role', $user), [
                'role_id' => $instructorRole->id,
                'notes' => 'Promoted to instructor',
            ]);

        $response->assertRedirect(route('admin.users.show', $user));
        $response->assertSessionHas('success');

        $this->assertTrue($user->fresh()->hasRole('instructor'));
    }

    /** @test */
    public function admin_can_remove_role_from_user()
    {
        $user = User::factory()->create();
        $user->assignRole('student');
        $user->assignRole('instructor');
        
        $instructorRole = Role::where('name', 'instructor')->first();

        $response = $this->actingAs($this->admin)
            ->delete(route('admin.users.remove-role', [$user, $instructorRole]));

        $response->assertRedirect(route('admin.users.show', $user));
        $response->assertSessionHas('success');

        $user->refresh();
        $this->assertFalse($user->hasRole('instructor'));
        $this->assertTrue($user->hasRole('student')); // Should still have student role
    }

    /** @test */
    public function admin_cannot_remove_last_role_from_user()
    {
        $user = User::factory()->create();
        $user->assignRole('student');
        
        $studentRole = Role::where('name', 'student')->first();

        $response = $this->actingAs($this->admin)
            ->delete(route('admin.users.remove-role', [$user, $studentRole]));

        $response->assertRedirect(route('admin.users.show', $user));
        $response->assertSessionHas('error');

        $this->assertTrue($user->fresh()->hasRole('student'));
    }

    /** @test */
    public function admin_can_delete_user()
    {
        $user = User::factory()->create();
        $user->assignRole('student');

        $response = $this->actingAs($this->admin)
            ->delete(route('admin.users.destroy', $user));

        $response->assertRedirect(route('admin.users.index'));
        $response->assertSessionHas('success');

        $this->assertSoftDeleted('users', [
            'id' => $user->id,
        ]);
    }

    /** @test */
    public function admin_cannot_delete_user_with_courses()
    {
        $instructor = $this->instructor;
        Course::factory()->create(['instructor_id' => $instructor->id]);

        $response = $this->actingAs($this->admin)
            ->delete(route('admin.users.destroy', $instructor));

        $response->assertRedirect(route('admin.users.index'));
        $response->assertSessionHas('error');

        $this->assertDatabaseHas('users', [
            'id' => $instructor->id,
            'deleted_at' => null,
        ]);
    }

    /** @test */
    public function admin_can_export_users()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.users.export'));

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
    }

    /** @test */
    public function admin_can_view_system_health()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.system-health'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'database',
            'storage',
            'cache',
            'rbac',
        ]);
    }

    /** @test */
    public function admin_can_toggle_role_status()
    {
        $role = Role::factory()->create(['is_active' => true]);

        $response = $this->actingAs($this->admin)
            ->patch(route('admin.roles.toggle-status', $role));

        $response->assertRedirect(route('admin.roles.index'));
        $response->assertSessionHas('success');

        $this->assertFalse($role->fresh()->is_active);
    }

    /** @test */
    public function admin_cannot_disable_system_roles()
    {
        $studentRole = Role::where('name', 'student')->first();

        $response = $this->actingAs($this->admin)
            ->patch(route('admin.roles.toggle-status', $studentRole));

        $response->assertRedirect(route('admin.roles.index'));
        $response->assertSessionHas('error');

        $this->assertTrue($studentRole->fresh()->is_active);
    }

    /** @test */
    public function admin_cannot_delete_system_roles()
    {
        $studentRole = Role::where('name', 'student')->first();

        $response = $this->actingAs($this->admin)
            ->delete(route('admin.roles.destroy', $studentRole));

        $response->assertRedirect(route('admin.roles.index'));
        $response->assertSessionHas('error');

        $this->assertDatabaseHas('roles', [
            'id' => $studentRole->id,
        ]);
    }
}
