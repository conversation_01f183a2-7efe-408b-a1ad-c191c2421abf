<?php

namespace Tests\Feature;

use App\Models\Course;
use App\Models\Role;
use App\Models\User;
use App\Services\PrivateStorageService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class CourseImageAndStatusTest extends TestCase
{
    use RefreshDatabase;

    protected User $instructor;
    protected User $student;
    protected User $admin;
    protected PrivateStorageService $storageService;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        $instructorRole = Role::create([
            'name' => 'instructor',
            'display_name' => 'Instructor',
            'description' => 'Course instructor',
            'priority' => 3,
            'is_active' => true,
        ]);

        $studentRole = Role::create([
            'name' => 'student',
            'display_name' => 'Student',
            'description' => 'Student',
            'priority' => 1,
            'is_active' => true,
        ]);

        $adminRole = Role::create([
            'name' => 'admin',
            'display_name' => 'Admin',
            'description' => 'Administrator',
            'priority' => 4,
            'is_active' => true,
        ]);

        // Create users
        $this->instructor = User::factory()->create();
        $this->instructor->roles()->attach($instructorRole);

        $this->student = User::factory()->create();
        $this->student->roles()->attach($studentRole);

        $this->admin = User::factory()->create();
        $this->admin->roles()->attach($adminRole);

        $this->storageService = app(PrivateStorageService::class);

        // Set up storage disks
        Storage::fake('private');
        Storage::fake('public');
    }

    /** @test */
    public function instructor_can_create_course_with_image_in_private_storage()
    {
        $this->actingAs($this->instructor);

        $file = UploadedFile::fake()->create('course-image.jpg', 1000, 'image/jpeg');

        $response = $this->post(route('instructor.courses.store'), [
            'title' => 'Test Course',
            'description' => 'A test course description',
            'category' => 'Programming',
            'price' => 99.99,
            'level' => 'beginner',
            'duration' => '4 weeks',
            'status' => 'published',
            'featured' => false,
            'image' => $file,
        ]);

        $response->assertRedirect();

        $course = Course::where('title', 'Test Course')->first();
        $this->assertNotNull($course);
        $this->assertEquals('published', $course->status);
        $this->assertNotNull($course->image);
        $this->assertTrue(str_starts_with($course->image, 'private/'));

        // Verify image exists in private storage
        $this->assertTrue(Storage::disk('private')->exists($course->image));
    }

    /** @test */
    public function course_image_url_returns_secure_route_for_private_images()
    {
        $course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'image' => 'private/123/456/course-images/test.jpg',
            'status' => 'published'
        ]);

        $imageUrl = $course->image_url;
        $this->assertStringContainsString('secure/files', $imageUrl);
    }

    /** @test */
    public function course_image_url_returns_asset_url_for_legacy_public_images()
    {
        $course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'image' => 'courses/legacy-image.jpg',
            'status' => 'published'
        ]);

        $imageUrl = $course->image_url;
        $this->assertStringContainsString('storage/courses/legacy-image.jpg', $imageUrl);
    }

    /** @test */
    public function only_published_courses_appear_on_homepage()
    {
        // Create courses with different statuses
        $publishedCourse = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'published',
            'featured' => true
        ]);

        $draftCourse = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'draft',
            'featured' => true
        ]);

        $archivedCourse = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'archived',
            'featured' => true
        ]);

        $response = $this->get(route('home'));
        $response->assertStatus(200);
        
        $featuredCourses = $response->viewData('featuredCourses');
        $this->assertCount(1, $featuredCourses);
        $this->assertEquals($publishedCourse->id, $featuredCourses->first()->id);
    }

    /** @test */
    public function students_can_only_see_published_courses()
    {
        $this->actingAs($this->student);

        // Create courses with different statuses
        Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'published'
        ]);

        Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'draft'
        ]);

        Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'archived'
        ]);

        $response = $this->get(route('courses.index'));
        $response->assertStatus(200);
        
        $courses = $response->viewData('courses');
        $this->assertCount(1, $courses);
        $this->assertEquals('published', $courses->first()->status);
    }

    /** @test */
    public function instructors_can_see_all_their_courses_regardless_of_status()
    {
        $this->actingAs($this->instructor);

        // Create courses with different statuses
        Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'published'
        ]);

        Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'draft'
        ]);

        Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'archived'
        ]);

        $response = $this->get(route('instructor.courses.index'));
        $response->assertStatus(200);
        
        $courses = $response->viewData('courses');
        $this->assertCount(3, $courses);
    }

    /** @test */
    public function course_status_toggle_works_correctly()
    {
        $this->actingAs($this->instructor);

        $course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'draft'
        ]);

        // Toggle to published
        $response = $this->patch(route('instructor.courses.toggle-status', $course));
        $response->assertRedirect();
        
        $course->refresh();
        $this->assertEquals('published', $course->status);

        // Toggle back to draft
        $response = $this->patch(route('instructor.courses.toggle-status', $course));
        $response->assertRedirect();
        
        $course->refresh();
        $this->assertEquals('draft', $course->status);
    }

    /** @test */
    public function course_image_access_control_works_correctly()
    {
        // Create a course with private image
        $course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'published'
        ]);

        $imagePath = "private/{$this->instructor->id}/{$course->id}/course-images/test.jpg";
        $course->update(['image' => $imagePath]);

        // Create the actual file in storage
        Storage::disk('private')->put($imagePath, 'fake image content');

        // Published course images should be accessible to authenticated users
        $this->actingAs($this->student);
        $response = $this->get(route('secure.files.serve', ['filePath' => $imagePath]));
        $response->assertStatus(200);

        // Draft course images should only be accessible to the instructor
        $course->update(['status' => 'draft']);

        $response = $this->get(route('secure.files.serve', ['filePath' => $imagePath]));
        $response->assertStatus(403);

        // But instructor should still have access
        $this->actingAs($this->instructor);
        $response = $this->get(route('secure.files.serve', ['filePath' => $imagePath]));
        $response->assertStatus(200);
    }
}
