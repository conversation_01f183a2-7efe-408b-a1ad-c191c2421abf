<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PayPalDebugService
{
    private $config;
    private $baseUrl;

    public function __construct()
    {
        $this->config = config('services.paypal');
        $this->baseUrl = $this->config['mode'] === 'live'
            ? 'https://api.paypal.com'
            : 'https://api.sandbox.paypal.com';
    }

    /**
     * Test different HTTP request approaches to identify the issue
     */
    public function debugCaptureRequest(string $orderId): array
    {
        $results = [];
        
        // Get access token first
        try {
            $accessToken = $this->getAccessToken();
            $results['access_token'] = 'SUCCESS';
        } catch (\Exception $e) {
            $results['access_token'] = 'FAILED: ' . $e->getMessage();
            return $results;
        }

        $captureUrl = $this->baseUrl . "/v2/checkout/orders/{$orderId}/capture";

        // Test 1: Original problematic approach (empty array body)
        $results['test_1_empty_array'] = $this->testRequest(
            'Empty Array Body',
            function() use ($captureUrl, $accessToken) {
                return Http::withToken($accessToken)
                    ->withHeaders(['Content-Type' => 'application/json'])
                    ->post($captureUrl, []);
            }
        );

        // Test 2: Content-Type header with no body
        $results['test_2_content_type_no_body'] = $this->testRequest(
            'Content-Type Header, No Body',
            function() use ($captureUrl, $accessToken) {
                return Http::withToken($accessToken)
                    ->withHeaders(['Content-Type' => 'application/json'])
                    ->post($captureUrl);
            }
        );

        // Test 3: No Content-Type header, no body (OLD approach)
        $results['test_3_no_content_type_no_body'] = $this->testRequest(
            'No Content-Type, No Body (OLD)',
            function() use ($captureUrl, $accessToken) {
                return Http::withToken($accessToken)
                    ->post($captureUrl);
            }
        );

        // Test 6: PayPal Official Documentation approach (NEW CORRECT)
        $results['test_6_official_documentation'] = $this->testRequest(
            'Official PayPal Documentation (CORRECT)',
            function() use ($captureUrl, $accessToken) {
                return Http::withToken($accessToken)
                    ->withHeaders(['Content-Type' => 'application/json'])
                    ->post($captureUrl, (object)[]);
            }
        );

        // Test 4: Empty JSON object body
        $results['test_4_empty_json_object'] = $this->testRequest(
            'Empty JSON Object Body',
            function() use ($captureUrl, $accessToken) {
                return Http::withToken($accessToken)
                    ->withHeaders(['Content-Type' => 'application/json'])
                    ->post($captureUrl, (object)[]);
            }
        );

        // Test 5: Explicit empty JSON string
        $results['test_5_empty_json_string'] = $this->testRequest(
            'Empty JSON String',
            function() use ($captureUrl, $accessToken) {
                return Http::withToken($accessToken)
                    ->withHeaders(['Content-Type' => 'application/json'])
                    ->withBody('{}')
                    ->post($captureUrl);
            }
        );

        return $results;
    }

    /**
     * Test a specific request approach
     */
    private function testRequest(string $testName, callable $requestFunction): array
    {
        try {
            Log::info("PayPal Debug Test: {$testName}");
            
            $response = $requestFunction();
            
            $result = [
                'test_name' => $testName,
                'status_code' => $response->status(),
                'successful' => $response->successful(),
                'response_body' => $response->body(),
                'headers' => $response->headers()
            ];

            if (!$response->successful()) {
                $responseData = $response->json();
                $result['error_name'] = $responseData['name'] ?? 'UNKNOWN';
                $result['error_message'] = $responseData['message'] ?? 'Unknown error';
                $result['error_details'] = $responseData['details'] ?? [];
                $result['debug_id'] = $responseData['debug_id'] ?? null;
            }

            Log::info("PayPal Debug Result: {$testName}", $result);
            
            return $result;

        } catch (\Exception $e) {
            $result = [
                'test_name' => $testName,
                'exception' => $e->getMessage(),
                'successful' => false
            ];

            Log::error("PayPal Debug Exception: {$testName}", $result);
            
            return $result;
        }
    }

    /**
     * Get PayPal access token
     */
    private function getAccessToken(): string
    {
        $response = Http::withBasicAuth(
            $this->config['client_id'],
            $this->config['client_secret']
        )->asForm()->post($this->baseUrl . '/v1/oauth2/token', [
            'grant_type' => 'client_credentials'
        ]);

        if ($response->successful()) {
            return $response->json()['access_token'];
        }

        throw new \Exception('Failed to get access token: ' . $response->body());
    }

    /**
     * Create a test order for debugging purposes
     */
    public function createTestOrder(): array
    {
        try {
            $accessToken = $this->getAccessToken();

            $orderData = [
                'intent' => 'CAPTURE',
                'purchase_units' => [
                    [
                        'reference_id' => 'debug_test_' . time(),
                        'amount' => [
                            'currency_code' => 'USD',
                            'value' => '10.00'
                        ],
                        'description' => 'Debug Test Order',
                        'custom_id' => 'debug_test_' . time(),
                        'invoice_id' => 'DEBUG-' . time()
                    ]
                ],
                'application_context' => [
                    'brand_name' => 'Debug Test',
                    'locale' => 'en-US',
                    'landing_page' => 'BILLING',
                    'shipping_preference' => 'NO_SHIPPING',
                    'user_action' => 'PAY_NOW',
                    'return_url' => url('/debug/paypal/success'),
                    'cancel_url' => url('/debug/paypal/cancel')
                ]
            ];

            $response = Http::withToken($accessToken)
                ->post($this->baseUrl . '/v2/checkout/orders', $orderData);

            if ($response->successful()) {
                $orderResult = $response->json();
                
                Log::info('PayPal debug test order created', [
                    'order_id' => $orderResult['id'],
                    'status' => $orderResult['status']
                ]);

                return [
                    'success' => true,
                    'order_id' => $orderResult['id'],
                    'status' => $orderResult['status'],
                    'links' => $orderResult['links']
                ];
            } else {
                throw new \Exception('Order creation failed: ' . $response->body());
            }

        } catch (\Exception $e) {
            Log::error('PayPal debug test order creation failed', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get detailed information about the current PayPal configuration
     */
    public function getConfigurationInfo(): array
    {
        return [
            'mode' => $this->config['mode'],
            'base_url' => $this->baseUrl,
            'client_id_configured' => !empty($this->config['client_id']),
            'client_secret_configured' => !empty($this->config['client_secret']),
            'currency' => $this->config['currency'],
            'platform_fee_percentage' => $this->config['platform_fee_percentage'],
            'webhook_id_configured' => !empty($this->config['webhook_id']),
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'http_client' => 'Laravel HTTP Client (Guzzle)'
        ];
    }
}
