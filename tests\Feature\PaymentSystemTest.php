<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\Payment;
use App\Models\Enrollment;
use App\Models\InstructorRevenue;
use App\Services\PayPalService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Mockery;

class PaymentSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $student;
    protected $instructor;
    protected $course;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed RBAC system
        $this->artisan('db:seed', ['--class' => 'CompleteRbacSeeder']);
        
        // Create test users
        $this->student = User::where('email', '<EMAIL>')->first();
        $this->instructor = User::where('email', '<EMAIL>')->first();
        
        // Create test course
        $this->course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'price' => 99.99,
            'status' => 'published',
        ]);
    }

    /** @test */
    public function student_can_initiate_course_payment()
    {
        $response = $this->actingAs($this->student)
            ->post(route('paypal.create-order'), [
                'course_id' => $this->course->id,
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'order_id',
            'approval_url',
        ]);
    }

    /** @test */
    public function payment_creates_enrollment_record()
    {
        // Mock PayPal service
        $mockPayPalService = Mockery::mock(PayPalService::class);
        $mockPayPalService->shouldReceive('createOrder')
            ->once()
            ->andReturn([
                'id' => 'PAYPAL_ORDER_ID',
                'status' => 'CREATED',
                'links' => [
                    ['rel' => 'approve', 'href' => 'https://paypal.com/approve']
                ]
            ]);

        $this->app->instance(PayPalService::class, $mockPayPalService);

        $response = $this->actingAs($this->student)
            ->post(route('paypal.create-order'), [
                'course_id' => $this->course->id,
            ]);

        $response->assertStatus(200);

        // Check that enrollment was created
        $this->assertDatabaseHas('enrollments', [
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'status' => 'pending',
        ]);
    }

    /** @test */
    public function successful_payment_creates_payment_record()
    {
        $paymentData = [
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'payment_method' => 'paypal',
            'payment_id' => 'PAYPAL_PAYMENT_ID',
            'amount' => $this->course->price,
            'instructor_amount' => $this->course->price * 0.85,
            'platform_fee' => $this->course->price * 0.15,
            'currency' => 'USD',
            'status' => Payment::STATUS_COMPLETED,
            'type' => Payment::TYPE_COURSE_PURCHASE,
            'paid_at' => now(),
        ];

        $payment = Payment::create($paymentData);

        $this->assertDatabaseHas('payments', [
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'status' => Payment::STATUS_COMPLETED,
        ]);
    }

    /** @test */
    public function successful_payment_creates_instructor_revenue_record()
    {
        $payment = Payment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'payment_method' => 'paypal',
            'payment_id' => 'PAYPAL_PAYMENT_ID',
            'amount' => $this->course->price,
            'instructor_amount' => $this->course->price * 0.85,
            'platform_fee' => $this->course->price * 0.15,
            'currency' => 'USD',
            'status' => Payment::STATUS_COMPLETED,
            'type' => Payment::TYPE_COURSE_PURCHASE,
            'paid_at' => now(),
        ]);

        // Simulate the payment completion process
        InstructorRevenue::create([
            'instructor_id' => $this->instructor->id,
            'payment_id' => $payment->id,
            'course_id' => $this->course->id,
            'amount' => $payment->instructor_amount,
            'platform_fee' => $payment->platform_fee,
            'status' => 'pending',
            'earned_at' => now(),
        ]);

        $this->assertDatabaseHas('instructor_revenue', [
            'instructor_id' => $this->instructor->id,
            'payment_id' => $payment->id,
            'amount' => $this->course->price * 0.85,
        ]);
    }

    /** @test */
    public function instructor_can_view_payment_analytics()
    {
        // Create some test payments
        Payment::factory()->count(3)->create([
            'instructor_id' => $this->instructor->id,
            'status' => Payment::STATUS_COMPLETED,
        ]);

        $response = $this->actingAs($this->instructor)
            ->get(route('instructor.payments.analytics'));

        $response->assertStatus(200);
        $response->assertViewIs('instructor.payments.analytics');
    }

    /** @test */
    public function instructor_can_view_payment_history()
    {
        // Create test payments
        Payment::factory()->count(5)->create([
            'instructor_id' => $this->instructor->id,
        ]);

        $response = $this->actingAs($this->instructor)
            ->get(route('instructor.payments.index'));

        $response->assertStatus(200);
        $response->assertViewIs('instructor.payments.index');
    }

    /** @test */
    public function instructor_only_sees_own_payments()
    {
        $otherInstructor = User::factory()->create();
        $otherInstructor->assignRole('instructor');

        // Create payments for current instructor
        $myPayments = Payment::factory()->count(3)->create([
            'instructor_id' => $this->instructor->id,
        ]);

        // Create payments for other instructor
        $otherPayments = Payment::factory()->count(2)->create([
            'instructor_id' => $otherInstructor->id,
        ]);

        $response = $this->actingAs($this->instructor)
            ->get(route('instructor.payments.index'));

        $response->assertStatus(200);
        
        // Check that only own payments are visible
        $viewData = $response->viewData('payments');
        $this->assertEquals(3, $viewData->count());
        
        foreach ($viewData as $payment) {
            $this->assertEquals($this->instructor->id, $payment->instructor_id);
        }
    }

    /** @test */
    public function payment_webhook_updates_payment_status()
    {
        $payment = Payment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'payment_method' => 'paypal',
            'payment_id' => 'PAYPAL_PAYMENT_ID',
            'amount' => $this->course->price,
            'instructor_amount' => $this->course->price * 0.85,
            'platform_fee' => $this->course->price * 0.15,
            'currency' => 'USD',
            'status' => Payment::STATUS_PENDING,
            'type' => Payment::TYPE_COURSE_PURCHASE,
        ]);

        $webhookData = [
            'event_type' => 'PAYMENT.CAPTURE.COMPLETED',
            'resource' => [
                'id' => 'PAYPAL_PAYMENT_ID',
                'status' => 'COMPLETED',
                'amount' => [
                    'value' => '99.99',
                    'currency_code' => 'USD'
                ]
            ]
        ];

        $response = $this->post(route('paypal.webhook'), $webhookData);

        $response->assertStatus(200);

        $payment->refresh();
        $this->assertEquals(Payment::STATUS_COMPLETED, $payment->status);
    }

    /** @test */
    public function failed_payment_updates_enrollment_status()
    {
        $enrollment = Enrollment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'status' => 'pending',
            'enrolled_at' => now(),
        ]);

        $payment = Payment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'payment_method' => 'paypal',
            'payment_id' => 'PAYPAL_PAYMENT_ID',
            'amount' => $this->course->price,
            'instructor_amount' => $this->course->price * 0.85,
            'platform_fee' => $this->course->price * 0.15,
            'currency' => 'USD',
            'status' => Payment::STATUS_FAILED,
            'type' => Payment::TYPE_COURSE_PURCHASE,
        ]);

        // Simulate payment failure processing
        $enrollment->update(['status' => 'failed']);

        $enrollment->refresh();
        $this->assertEquals('failed', $enrollment->status);
    }

    /** @test */
    public function instructor_can_export_payment_data()
    {
        Payment::factory()->count(3)->create([
            'instructor_id' => $this->instructor->id,
            'status' => Payment::STATUS_COMPLETED,
        ]);

        $response = $this->actingAs($this->instructor)
            ->get(route('instructor.payments.export', ['format' => 'csv']));

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
    }

    /** @test */
    public function student_cannot_access_instructor_payment_routes()
    {
        $response = $this->actingAs($this->student)
            ->get(route('instructor.payments.index'));

        $response->assertStatus(403);
    }

    /** @test */
    public function guest_cannot_access_payment_routes()
    {
        $response = $this->get(route('instructor.payments.index'));

        $response->assertRedirect(route('login'));
    }

    /** @test */
    public function payment_amount_calculation_is_correct()
    {
        $coursePrice = 100.00;
        $expectedInstructorAmount = $coursePrice * 0.85; // 85%
        $expectedPlatformFee = $coursePrice * 0.15; // 15%

        $payment = Payment::create([
            'user_id' => $this->student->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'payment_method' => 'paypal',
            'payment_id' => 'PAYPAL_PAYMENT_ID',
            'amount' => $coursePrice,
            'instructor_amount' => $expectedInstructorAmount,
            'platform_fee' => $expectedPlatformFee,
            'currency' => 'USD',
            'status' => Payment::STATUS_COMPLETED,
            'type' => Payment::TYPE_COURSE_PURCHASE,
        ]);

        $this->assertEquals($expectedInstructorAmount, $payment->instructor_amount);
        $this->assertEquals($expectedPlatformFee, $payment->platform_fee);
        $this->assertEquals($coursePrice, $payment->instructor_amount + $payment->platform_fee);
    }
}
