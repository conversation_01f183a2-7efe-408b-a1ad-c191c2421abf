<?php

namespace Tests\Feature;

use App\Http\Middleware\SecureFileAccess;
use App\Models\Course;
use App\Models\Role;
use App\Models\User;
use App\Services\PrivateStorageService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class MiddlewareDebugTest extends TestCase
{
    use RefreshDatabase;

    protected User $instructor;
    protected User $student;
    protected PrivateStorageService $storageService;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        $instructorRole = Role::create([
            'name' => 'instructor',
            'display_name' => 'Instructor',
            'description' => 'Course instructor',
            'priority' => 3,
            'is_active' => true,
        ]);

        $studentRole = Role::create([
            'name' => 'student',
            'display_name' => 'Student',
            'description' => 'Student',
            'priority' => 1,
            'is_active' => true,
        ]);

        // Create users
        $this->instructor = User::factory()->create();
        $this->instructor->roles()->attach($instructorRole);

        $this->student = User::factory()->create();
        $this->student->roles()->attach($studentRole);

        $this->storageService = app(PrivateStorageService::class);

        // Set up storage disks
        Storage::fake('private');
    }

    /** @test */
    public function middleware_logic_works_correctly()
    {
        // Create a course with private image
        $course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'published'
        ]);

        $imagePath = "private/{$this->instructor->id}/{$course->id}/course-images/test.jpg";
        $course->update(['image' => $imagePath]);

        // Create the actual file in storage
        Storage::disk('private')->put($imagePath, 'fake image content');

        // Test the middleware logic directly
        $middleware = new SecureFileAccess($this->storageService);

        // Create a mock request
        $request = Request::create('/secure/files/' . $imagePath);
        $request->setRouteResolver(function () use ($imagePath) {
            $route = new \Illuminate\Routing\Route(['GET'], '/secure/files/{filePath}', []);
            $route->bind($request = Request::create('/secure/files/' . $imagePath));
            $route->setParameter('filePath', $imagePath);
            return $route;
        });

        // Test 1: Student accessing published course image should work
        $this->actingAs($this->student);
        
        try {
            $response = $middleware->handle($request, function ($req) {
                return response('OK', 200);
            });
            $this->assertEquals(200, $response->getStatusCode());
            dump('Test 1 PASSED: Student can access published course image');
        } catch (\Exception $e) {
            dump('Test 1 FAILED: ' . $e->getMessage());
            $this->fail('Student should be able to access published course image');
        }

        // Test 2: Change course to draft - student should be denied
        $course->update(['status' => 'draft']);
        $course->refresh();

        // Debug: Check course status
        dump('Course status after update: ' . $course->status);
        dump('Course ID: ' . $course->id);
        dump('Instructor ID: ' . $course->instructor_id);
        dump('Student ID: ' . $this->student->id);
        dump('Image path: ' . $imagePath);

        try {
            $response = $middleware->handle($request, function ($req) {
                return response('OK', 200);
            });
            dump('Test 2 FAILED: Student accessed draft course image (status: ' . $response->getStatusCode() . ')');
            $this->fail('Student should NOT be able to access draft course image');
        } catch (\Exception $e) {
            if ($e->getCode() === 403) {
                dump('Test 2 PASSED: Student correctly denied access to draft course image');
                $this->assertTrue(true);
            } else {
                dump('Test 2 FAILED: Unexpected error: ' . $e->getMessage());
                $this->fail('Unexpected error: ' . $e->getMessage());
            }
        }

        // Test 3: Instructor should have access to their own draft course
        $this->actingAs($this->instructor);
        
        try {
            $response = $middleware->handle($request, function ($req) {
                return response('OK', 200);
            });
            $this->assertEquals(200, $response->getStatusCode());
            dump('Test 3 PASSED: Instructor can access their own draft course image');
        } catch (\Exception $e) {
            dump('Test 3 FAILED: ' . $e->getMessage());
            $this->fail('Instructor should be able to access their own draft course image');
        }
    }
}
