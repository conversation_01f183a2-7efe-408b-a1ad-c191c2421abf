<?php

/**
 * Manual validation script for course management enhancements
 * Run this script to verify the implementation works correctly
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Services\PrivateStorageService;
use Illuminate\Support\Facades\Storage;

echo "=== Course Management System Enhancement Validation ===\n\n";

// Test 1: PrivateStorageService instantiation
echo "1. Testing PrivateStorageService instantiation...\n";
try {
    $app = require __DIR__ . '/../bootstrap/app.php';
    $app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();
    
    $storageService = $app->make(PrivateStorageService::class);
    echo "   ✓ PrivateStorageService instantiated successfully\n";
} catch (Exception $e) {
    echo "   ✗ Failed to instantiate PrivateStorageService: " . $e->getMessage() . "\n";
}

// Test 2: Material types configuration
echo "\n2. Testing material types configuration...\n";
try {
    $materialTypes = PrivateStorageService::getMaterialTypes();
    $expectedTypes = ['learning-materials', 'ebooks', 'videos', 'resources', 'content-files'];
    
    foreach ($expectedTypes as $type) {
        if (array_key_exists($type, $materialTypes)) {
            echo "   ✓ Material type '{$type}' configured\n";
        } else {
            echo "   ✗ Material type '{$type}' missing\n";
        }
    }
} catch (Exception $e) {
    echo "   ✗ Failed to get material types: " . $e->getMessage() . "\n";
}

// Test 3: Directory path generation
echo "\n3. Testing directory path generation...\n";
try {
    $userId = '123';
    $courseId = '456';
    $materialType = 'learning-materials';
    
    $expectedPath = "private/{$userId}/{$courseId}/materials/{$materialType}";
    $actualPath = $storageService->getPrivateDirectory($userId, $courseId, $materialType);
    
    if ($actualPath === $expectedPath) {
        echo "   ✓ Directory path generated correctly: {$actualPath}\n";
    } else {
        echo "   ✗ Directory path mismatch. Expected: {$expectedPath}, Got: {$actualPath}\n";
    }
} catch (Exception $e) {
    echo "   ✗ Failed to generate directory path: " . $e->getMessage() . "\n";
}

// Test 4: File access verification
echo "\n4. Testing file access verification...\n";
try {
    $userFilePath = "private/123/course-1/materials/test.pdf";
    $otherUserFilePath = "private/456/course-2/materials/test.pdf";
    
    $hasAccess = $storageService->verifyFileAccess($userFilePath, '123');
    $noAccess = $storageService->verifyFileAccess($otherUserFilePath, '123');
    
    if ($hasAccess && !$noAccess) {
        echo "   ✓ File access verification working correctly\n";
    } else {
        echo "   ✗ File access verification failed\n";
    }
} catch (Exception $e) {
    echo "   ✗ Failed to verify file access: " . $e->getMessage() . "\n";
}

// Test 5: Route registration
echo "\n5. Testing secure file routes registration...\n";
try {
    $routes = app('router')->getRoutes();
    $secureRoutes = ['secure.files.serve', 'secure.files.download', 'secure.files.stream'];
    
    foreach ($secureRoutes as $routeName) {
        if ($routes->hasNamedRoute($routeName)) {
            echo "   ✓ Route '{$routeName}' registered\n";
        } else {
            echo "   ✗ Route '{$routeName}' missing\n";
        }
    }
} catch (Exception $e) {
    echo "   ✗ Failed to check routes: " . $e->getMessage() . "\n";
}

// Test 6: Middleware registration
echo "\n6. Testing middleware registration...\n";
try {
    $middleware = app('router')->getMiddleware();
    
    if (array_key_exists('secure.file', $middleware)) {
        echo "   ✓ SecureFileAccess middleware registered\n";
    } else {
        echo "   ✗ SecureFileAccess middleware missing\n";
    }
} catch (Exception $e) {
    echo "   ✗ Failed to check middleware: " . $e->getMessage() . "\n";
}

// Test 7: Storage disk configuration
echo "\n7. Testing storage disk configuration...\n";
try {
    $privateConfig = config('filesystems.disks.private');
    
    if ($privateConfig && $privateConfig['driver'] === 'local') {
        echo "   ✓ Private storage disk configured\n";
    } else {
        echo "   ✗ Private storage disk not configured properly\n";
    }
} catch (Exception $e) {
    echo "   ✗ Failed to check storage configuration: " . $e->getMessage() . "\n";
}

echo "\n=== Validation Complete ===\n";
echo "\nTo test the UI enhancements:\n";
echo "1. Visit: http://127.0.0.1:8000/instructor/courses/create\n";
echo "2. Check for enhanced publication status dropdown\n";
echo "3. Check for image preview functionality\n";
echo "4. Visit: http://127.0.0.1:8000/instructor/courses (any course)\n";
echo "5. Check for tabbed interface in course detail view\n";
echo "\nTo test file operations:\n";
echo "1. Create a course\n";
echo "2. Add learning materials with file uploads\n";
echo "3. Verify files are stored in private directories\n";
echo "4. Verify secure file serving works\n";
