<?php

namespace Tests\Feature;

use App\Models\Course;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class CourseManagementEnhancementsTest extends TestCase
{
    use RefreshDatabase;

    protected $instructor;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->instructor = User::factory()->create([
            'role' => 'instructor'
        ]);
        
        Storage::fake('public');
    }

    /** @test */
    public function it_stores_course_images_in_user_specific_directories()
    {
        $this->actingAs($this->instructor);
        
        $image = UploadedFile::fake()->image('course-image.jpg', 1200, 630);
        
        $courseData = [
            'title' => 'Test Course with Image',
            'description' => 'Test Description',
            'category' => 'Technology',
            'price' => 99.99,
            'level' => 'beginner',
            'duration' => '10 hours',
            'status' => 'draft',
            'image' => $image
        ];
        
        $response = $this->post(route('instructor.courses.store'), $courseData);
        
        $course = Course::where('title', 'Test Course with Image')->first();
        $this->assertNotNull($course);
        
        // Verify image is stored in user-specific directory
        $expectedPath = "{$this->instructor->id}/course-images";
        $this->assertStringContains($expectedPath, $course->image);
        
        // Verify file exists
        $this->assertTrue(Storage::disk('public')->exists($course->image));
    }

    /** @test */
    public function it_displays_course_create_form_with_enhanced_ui()
    {
        $this->actingAs($this->instructor);
        
        $response = $this->get(route('instructor.courses.create'));
        
        $response->assertStatus(200);
        
        // Check for enhanced publication status dropdown
        $response->assertSee('Publication Status');
        $response->assertSee('📝 Draft - Not visible to students');
        $response->assertSee('🌟 Published - Live and available to students');
        $response->assertSee('📦 Archived - Hidden from public but accessible to enrolled students');
        
        // Check for image preview functionality
        $response->assertSee('previewImage');
        $response->assertSee('image-preview');
        $response->assertSee('PNG, JPG, GIF, WEBP up to 5MB');
    }

    /** @test */
    public function it_displays_course_edit_form_with_enhanced_ui()
    {
        $this->actingAs($this->instructor);
        
        $course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'published'
        ]);
        
        $response = $this->get(route('instructor.courses.edit', $course));
        
        $response->assertStatus(200);
        
        // Check for enhanced publication status dropdown with current value selected
        $response->assertSee('Publication Status');
        $response->assertSee('selected', false); // Should have a selected option
        
        // Check for image preview functionality
        $response->assertSee('previewImage');
        $response->assertSee('image-preview');
    }

    /** @test */
    public function it_validates_publication_status_correctly()
    {
        $this->actingAs($this->instructor);
        
        // Test with invalid status
        $courseData = [
            'title' => 'Test Course',
            'description' => 'Test Description',
            'category' => 'Technology',
            'price' => 99.99,
            'level' => 'beginner',
            'duration' => '10 hours',
            'status' => 'invalid_status'
        ];
        
        $response = $this->post(route('instructor.courses.store'), $courseData);
        $response->assertSessionHasErrors('status');
        
        // Test with valid statuses
        $validStatuses = ['draft', 'published', 'archived'];
        
        foreach ($validStatuses as $status) {
            $courseData['status'] = $status;
            $courseData['title'] = "Test Course {$status}";
            
            $response = $this->post(route('instructor.courses.store'), $courseData);
            $response->assertSessionDoesntHaveErrors('status');
            
            $course = Course::where('title', "Test Course {$status}")->first();
            $this->assertEquals($status, $course->status);
        }
    }

    /** @test */
    public function instructor_course_show_redirects_to_course_builder()
    {
        $this->actingAs($this->instructor);

        $course = Course::factory()->create([
            'instructor_id' => $this->instructor->id
        ]);

        $response = $this->get(route('instructor.courses.show', $course));

        // Should redirect to the new course builder interface
        $response->assertRedirect(route('instructor.course-builder.show', $course));
        $response->assertSessionHas('info', 'Redirected to the new course builder interface for better course management.');
    }

    /** @test */
    public function it_validates_image_uploads_correctly()
    {
        $this->actingAs($this->instructor);
        
        // Test with oversized image
        $largeImage = UploadedFile::fake()->create('large-image.jpg', 6000); // 6MB
        
        $courseData = [
            'title' => 'Test Course',
            'description' => 'Test Description',
            'category' => 'Technology',
            'price' => 99.99,
            'level' => 'beginner',
            'duration' => '10 hours',
            'status' => 'draft',
            'image' => $largeImage
        ];
        
        $response = $this->post(route('instructor.courses.store'), $courseData);
        $response->assertSessionHasErrors('image');
        
        // Test with invalid file type
        $invalidFile = UploadedFile::fake()->create('document.txt', 100);
        $courseData['image'] = $invalidFile;
        
        $response = $this->post(route('instructor.courses.store'), $courseData);
        $response->assertSessionHasErrors('image');
        
        // Test with valid image
        $validImage = UploadedFile::fake()->image('valid-image.jpg', 1200, 630);
        $courseData['image'] = $validImage;
        $courseData['title'] = 'Valid Course';
        
        $response = $this->post(route('instructor.courses.store'), $courseData);
        $response->assertSessionDoesntHaveErrors('image');
    }

    /** @test */
    public function it_updates_course_images_correctly()
    {
        $this->actingAs($this->instructor);
        
        // Create course with initial image
        $initialImage = UploadedFile::fake()->image('initial-image.jpg');
        $course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'image' => 'test-path/initial-image.jpg'
        ]);
        
        // Update with new image
        $newImage = UploadedFile::fake()->image('new-image.jpg');
        
        $updateData = [
            'title' => $course->title,
            'description' => $course->description,
            'category' => $course->category,
            'price' => $course->price,
            'level' => $course->level,
            'duration' => $course->duration,
            'status' => $course->status,
            'image' => $newImage
        ];
        
        $response = $this->put(route('instructor.courses.update', $course), $updateData);
        
        $course->refresh();
        
        // Verify new image path contains user directory
        $expectedPath = "{$this->instructor->id}/course-images";
        $this->assertStringContains($expectedPath, $course->image);
        
        // Verify new image exists
        $this->assertTrue(Storage::disk('public')->exists($course->image));
    }

    /** @test */
    public function it_maintains_proper_rbac_for_course_operations()
    {
        $otherInstructor = User::factory()->create(['role' => 'instructor']);
        
        $course = Course::factory()->create([
            'instructor_id' => $this->instructor->id
        ]);
        
        // Other instructor should not be able to access course
        $this->actingAs($otherInstructor);

        // The show route now redirects to course builder, which will then check authorization
        $response = $this->get(route('instructor.courses.show', $course));
        $response->assertRedirect(route('instructor.course-builder.show', $course));
        
        $response = $this->get(route('instructor.courses.edit', $course));
        $response->assertStatus(403);
        
        $response = $this->put(route('instructor.courses.update', $course), []);
        $response->assertStatus(403);
        
        $response = $this->delete(route('instructor.courses.destroy', $course));
        $response->assertStatus(403);
    }

    /** @test */
    public function it_creates_course_with_pre_selected_values_from_url()
    {
        $this->actingAs($this->instructor);
        
        // Test content creation with course_id parameter
        $course = Course::factory()->create([
            'instructor_id' => $this->instructor->id
        ]);
        
        $response = $this->get(route('instructor.learning-materials.create', ['course_id' => $course->id]));
        $response->assertStatus(200);
        $response->assertSee($course->title);
        
        $response = $this->get(route('instructor.content-files.create', ['course_id' => $course->id]));
        $response->assertStatus(200);
        $response->assertSee($course->title);
    }

    protected function tearDown(): void
    {
        Storage::fake('public');
        parent::tearDown();
    }
}
