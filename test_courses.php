<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Courses in database:\n";
foreach(App\Models\Course::select('title', 'slug')->get() as $course) {
    echo $course->title . ' -> ' . $course->slug . "\n";
}

echo "\nSlug for 'React.js Complete Guide': " . \Illuminate\Support\Str::slug('React.js Complete Guide') . "\n";

echo "\<NAME_EMAIL>:\n";
$student = App\Models\User::where('email', '<EMAIL>')->first();
if ($student) {
    foreach($student->enrollments()->with('course')->get() as $enrollment) {
        echo $enrollment->course->title . ' -> ' . $enrollment->course->slug . "\n";
    }
} else {
    echo "No <EMAIL> found\n";
}
