<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Permission extends Model
{
    use HasFactory;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'category',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the roles that have this permission.
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'role_permissions')
            ->withPivot(['granted_at', 'granted_by', 'is_active'])
            ->withTimestamps();
    }

    /**
     * Get the active roles that have this permission.
     */
    public function activeRoles(): BelongsToMany
    {
        return $this->roles()->wherePivot('is_active', true);
    }

    /**
     * Get the role permission assignments for this permission.
     */
    public function rolePermissions(): HasMany
    {
        return $this->hasMany(RolePermission::class);
    }

    /**
     * Scope to get only active permissions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by category.
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get permission by name.
     */
    public static function findByName(string $name): ?self
    {
        return static::where('name', $name)->first();
    }

    /**
     * Get permissions grouped by category.
     */
    public static function getGroupedByCategory(): array
    {
        return static::active()
            ->orderBy('category')
            ->orderBy('display_name')
            ->get()
            ->groupBy('category')
            ->toArray();
    }

    /**
     * Check if this permission belongs to a specific category.
     */
    public function belongsToCategory(string $category): bool
    {
        return $this->category === $category;
    }

    /**
     * Get all users who have this permission (through roles).
     */
    public function users()
    {
        return User::whereHas('activeRoles.activePermissions', function ($query) {
            $query->where('permissions.id', $this->id);
        });
    }
}
