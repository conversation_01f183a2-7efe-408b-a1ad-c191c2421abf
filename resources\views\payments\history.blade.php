@extends('layouts.app')

@section('title', 'Payment History - Escape Matrix Academy')

@section('content')
<div class="py-12 bg-black min-h-screen">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-white mb-2">Payment History</h1>
            <p class="text-gray-400">View all your course purchases and payment details</p>
        </div>

        @if($payments->count() > 0)
            <!-- Payments List -->
            <div class="space-y-6">
                @foreach($payments as $payment)
                    <div class="bg-gray-800 border border-gray-700 rounded-lg p-6 hover:border-gray-600 transition-colors">
                        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                            <!-- Payment Info -->
                            <div class="flex-1">
                                <div class="flex items-start space-x-4">
                                    <!-- Status Icon -->
                                    <div class="flex-shrink-0 mt-1">
                                        @if($payment->status === 'completed')
                                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        @elseif($payment->status === 'pending')
                                            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                        @elseif($payment->status === 'failed')
                                            <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                        @elseif($payment->status === 'refunded')
                                            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                        @else
                                            <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                                        @endif
                                    </div>

                                    <!-- Course and Payment Details -->
                                    <div class="flex-1">
                                        <h3 class="text-lg font-semibold text-white mb-1">
                                            <a href="{{ route('courses.show', $payment->course) }}" class="hover:text-red-400 transition-colors">
                                                {{ $payment->course->title }}
                                            </a>
                                        </h3>
                                        
                                        <div class="flex flex-wrap items-center gap-4 text-sm text-gray-400 mb-2">
                                            <span>Instructor: {{ $payment->course->instructor->name ?? 'Unknown' }}</span>
                                            <span>•</span>
                                            <span>{{ $payment->created_at->format('M j, Y') }}</span>
                                            <span>•</span>
                                            <span class="capitalize">{{ $payment->payment_method ?? 'PayPal' }}</span>
                                        </div>

                                        <!-- Payment Status -->
                                        <div class="flex items-center space-x-2">
                                            <span class="px-2 py-1 rounded-full text-xs font-medium
                                                @if($payment->status === 'completed') bg-green-900 text-green-300
                                                @elseif($payment->status === 'pending') bg-yellow-900 text-yellow-300
                                                @elseif($payment->status === 'failed') bg-red-900 text-red-300
                                                @elseif($payment->status === 'refunded') bg-blue-900 text-blue-300
                                                @else bg-gray-900 text-gray-300
                                                @endif">
                                                {{ ucfirst($payment->status) }}
                                            </span>
                                            
                                            @if($payment->paid_at)
                                                <span class="text-xs text-gray-500">
                                                    Paid on {{ $payment->paid_at->format('M j, Y g:i A') }}
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Amount and Actions -->
                            <div class="mt-4 lg:mt-0 lg:ml-6 flex flex-col lg:items-end space-y-2">
                                <div class="text-right">
                                    <div class="text-xl font-bold text-white">
                                        {{ $payment->formatted_amount }}
                                    </div>
                                    @if($payment->payment_id)
                                        <div class="text-xs text-gray-500">
                                            ID: {{ Str::limit($payment->payment_id, 20) }}
                                        </div>
                                    @endif
                                </div>

                                <div class="flex space-x-2">
                                    <a href="{{ route('payments.details', $payment) }}" 
                                       class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-white text-sm rounded transition-colors">
                                        View Details
                                    </a>
                                    
                                    @if($payment->status === 'completed')
                                        <a href="{{ route('my-courses.view', $payment->course) }}" 
                                           class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors">
                                            Access Course
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $payments->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <div class="mb-6">
                    <svg class="w-16 h-16 text-gray-600 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">No payments yet</h3>
                <p class="text-gray-400 mb-6">You haven't made any course purchases yet.</p>
                <a href="{{ route('courses.index') }}" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-md transition-colors">
                    Browse Courses
                </a>
            </div>
        @endif
    </div>
</div>
@endsection

@push('styles')
<style>
.pagination {
    @apply flex justify-center space-x-1;
}

.pagination .page-link {
    @apply px-3 py-2 text-gray-400 bg-gray-800 border border-gray-700 hover:bg-gray-700 hover:text-white transition-colors;
}

.pagination .page-item.active .page-link {
    @apply bg-red-600 text-white border-red-600;
}

.pagination .page-item.disabled .page-link {
    @apply text-gray-600 cursor-not-allowed;
}
</style>
@endpush
