<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, HasUuids, Notifiable;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    protected $fillable = [
        'name',
        'email',
        'password',
        'google_id',
        'role', // Keep for backward compatibility during transition
        'avatar',
        'bio'
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];
    
    public function enrollments()
    {
        return $this->hasMany(Enrollment::class);
    }

    public function courses()
    {
        return $this->hasMany(Course::class, 'instructor_id');
    }

    // Course structure relationships
    public function chapters()
    {
        return $this->hasMany(Chapter::class, 'instructor_id');
    }

    public function lectures()
    {
        return $this->hasMany(Lecture::class, 'instructor_id');
    }

    public function lectureProgress()
    {
        return $this->hasMany(LectureProgress::class);
    }

    // Payment relationships
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function instructorPayments()
    {
        return $this->hasMany(Payment::class, 'instructor_id');
    }

    // Review relationships
    public function courseReviews()
    {
        return $this->hasMany(CourseReview::class);
    }

    public function instructorReviews()
    {
        return $this->hasMany(CourseReview::class, 'instructor_id');
    }

    // RBAC Relationships

    /**
     * Get the roles assigned to this user.
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'user_roles')
            ->withPivot(['assigned_at', 'expires_at', 'assigned_by', 'notes'])
            ->withTimestamps();
    }

    /**
     * Get the active roles assigned to this user.
     */
    public function activeRoles()
    {
        return $this->belongsToMany(Role::class, 'user_roles')
            ->whereRaw('(user_roles.expires_at IS NULL OR user_roles.expires_at > ?)', [now()])
            ->withPivot(['assigned_at', 'expires_at', 'assigned_by', 'notes'])
            ->withTimestamps();
    }

    /**
     * Get the user role assignments.
     */
    public function userRoles()
    {
        return $this->hasMany(UserRole::class);
    }

    /**
     * Get the active user role assignments.
     */
    public function activeUserRoles()
    {
        return $this->hasMany(UserRole::class)->active();
    }

    // RBAC Helper Methods

    /**
     * Check if user has a specific role.
     */
    public function hasRole(string $role): bool
    {
        return $this->activeRoles()->where('name', $role)->exists();
    }

    /**
     * Check if user has any of the given roles.
     */
    public function hasAnyRole(array $roles): bool
    {
        return $this->activeRoles()->whereIn('name', $roles)->exists();
    }

    /**
     * Check if user has all of the given roles.
     */
    public function hasAllRoles(array $roles): bool
    {
        $userRoles = $this->activeRoles()->pluck('name')->toArray();
        return count(array_intersect($roles, $userRoles)) === count($roles);
    }

    /**
     * Get all permissions for this user through their roles.
     */
    public function getPermissions()
    {
        return Permission::whereHas('roles', function ($query) {
            $query->whereIn('roles.id', $this->activeRoles()->pluck('roles.id'));
        })->where('is_active', true);
    }

    /**
     * Check if user has a specific permission.
     */
    public function hasPermission(string $permission): bool
    {
        return $this->getPermissions()->where('name', $permission)->exists();
    }

    /**
     * Check if user has any of the given permissions.
     */
    public function hasAnyPermission(array $permissions): bool
    {
        return $this->getPermissions()->whereIn('name', $permissions)->exists();
    }

    /**
     * Check if user has all of the given permissions.
     */
    public function hasAllPermissions(array $permissions): bool
    {
        $userPermissions = $this->getPermissions()->pluck('name')->toArray();
        return count(array_intersect($permissions, $userPermissions)) === count($permissions);
    }

    /**
     * Assign a role to the user.
     */
    public function assignRole(string $roleName, ?string $assignedBy = null, ?\DateTime $expiresAt = null, ?string $notes = null): bool
    {
        $role = Role::findByName($roleName);
        if (!$role) {
            return false;
        }

        // Check if user already has this role
        if ($this->hasRole($roleName)) {
            return true;
        }

        $this->roles()->attach($role->id, [
            'assigned_at' => now(),
            'expires_at' => $expiresAt,
            'assigned_by' => $assignedBy,
            'notes' => $notes,
        ]);

        return true;
    }

    /**
     * Remove a role from the user.
     */
    public function removeRole(string $roleName): bool
    {
        $role = Role::findByName($roleName);
        if (!$role) {
            return false;
        }

        $this->roles()->detach($role->id);
        return true;
    }

    /**
     * Get the highest priority role for this user.
     */
    public function getPrimaryRole(): ?Role
    {
        return $this->activeRoles()->orderBy('priority', 'desc')->first();
    }

    // Legacy role methods for backward compatibility

    public function isAdmin()
    {
        return $this->hasRole(Role::ADMIN) || $this->hasRole(Role::SUPERADMIN) || $this->role === 'admin';
    }

    public function isSuperAdmin()
    {
        return $this->hasRole(Role::SUPERADMIN);
    }

    public function isInstructor()
    {
        return $this->hasRole(Role::INSTRUCTOR) || $this->role === 'instructor';
    }

    public function isStudent()
    {
        return $this->hasRole(Role::STUDENT) || $this->role === 'student';
    }

    // Google OAuth Helper Methods

    /**
     * Check if user has Google account linked
     */
    public function hasGoogleAccount(): bool
    {
        return !empty($this->google_id);
    }

    /**
     * Check if user can login with password (has password set)
     */
    public function hasPassword(): bool
    {
        return !empty($this->password) && $this->password !== '';
    }

    /**
     * Check if user is Google-only (no password set)
     */
    public function isGoogleOnly(): bool
    {
        return $this->hasGoogleAccount() && !$this->hasPassword();
    }

    /**
     * Get user's display avatar (Google avatar or default)
     */
    public function getAvatarUrl(): string
    {
        if ($this->avatar) {
            // If it's a full URL (like Google avatar), return as-is
            if (filter_var($this->avatar, FILTER_VALIDATE_URL)) {
                return $this->avatar;
            }

            // If it's a local file, return the full path
            return asset('storage/' . $this->avatar);
        }

        // Default avatar based on user's initials
        $initials = collect(explode(' ', $this->name))
            ->map(fn($word) => strtoupper(substr($word, 0, 1)))
            ->take(2)
            ->join('');

        return "https://ui-avatars.com/api/?name={$initials}&background=dc2626&color=ffffff&size=128";
    }

    /**
     * Check if user has administrative privileges.
     */
    public function hasAdminPrivileges(): bool
    {
        return $this->hasAnyRole([\App\Models\Role::ADMIN, \App\Models\Role::SUPERADMIN]);
    }


}
