<?php
// Debug script to test step 2 form submission
// Run this in the browser to simulate the form submission

// Simulate form data that should be sent from step 2
$testFormData = [
    'chapters' => [
        [
            'title' => 'Introduction to Programming',
            'description' => 'Basic concepts and fundamentals',
            'sub_chapters' => [
                [
                    'title' => 'What is Programming?',
                    'description' => 'Understanding programming basics'
                ],
                [
                    'title' => 'Setting up Environment',
                    'description' => 'Installing necessary tools'
                ]
            ]
        ],
        [
            'title' => 'Variables and Data Types',
            'description' => 'Working with different data types',
            'sub_chapters' => [
                [
                    'title' => 'Numbers and Strings',
                    'description' => 'Basic data types'
                ]
            ]
        ]
    ]
];

echo "<h2>Test Form Data Structure:</h2>";
echo "<pre>";
print_r($testFormData);
echo "</pre>";

echo "<h2>Validation Check:</h2>";

// Check if data meets validation requirements
$errors = [];

// Check chapters array
if (!isset($testFormData['chapters']) || !is_array($testFormData['chapters'])) {
    $errors[] = "chapters: required|array";
} else {
    $chaptersCount = count($testFormData['chapters']);
    if ($chaptersCount < 1) {
        $errors[] = "chapters: min:1";
    }
    if ($chaptersCount > 20) {
        $errors[] = "chapters: max:20";
    }
    
    // Check each chapter
    foreach ($testFormData['chapters'] as $index => $chapter) {
        $prefix = "chapters.{$index}";
        
        // Check chapter title
        if (!isset($chapter['title']) || empty(trim($chapter['title']))) {
            $errors[] = "{$prefix}.title: required";
        } elseif (strlen($chapter['title']) > 255) {
            $errors[] = "{$prefix}.title: max:255";
        }
        
        // Check chapter description (optional)
        if (isset($chapter['description']) && strlen($chapter['description']) > 1000) {
            $errors[] = "{$prefix}.description: max:1000";
        }
        
        // Check sub_chapters
        if (!isset($chapter['sub_chapters']) || !is_array($chapter['sub_chapters'])) {
            $errors[] = "{$prefix}.sub_chapters: required|array";
        } else {
            $subChaptersCount = count($chapter['sub_chapters']);
            if ($subChaptersCount < 1) {
                $errors[] = "{$prefix}.sub_chapters: min:1";
            }
            if ($subChaptersCount > 10) {
                $errors[] = "{$prefix}.sub_chapters: max:10";
            }
            
            // Check each sub-chapter
            foreach ($chapter['sub_chapters'] as $subIndex => $subChapter) {
                $subPrefix = "{$prefix}.sub_chapters.{$subIndex}";
                
                // Check sub-chapter title
                if (!isset($subChapter['title']) || empty(trim($subChapter['title']))) {
                    $errors[] = "{$subPrefix}.title: required";
                } elseif (strlen($subChapter['title']) > 255) {
                    $errors[] = "{$subPrefix}.title: max:255";
                }
                
                // Check sub-chapter description (optional)
                if (isset($subChapter['description']) && strlen($subChapter['description']) > 1000) {
                    $errors[] = "{$subPrefix}.description: max:1000";
                }
            }
        }
    }
}

if (empty($errors)) {
    echo "<p style='color: green;'>✓ All validation rules passed!</p>";
} else {
    echo "<p style='color: red;'>✗ Validation errors found:</p>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>{$error}</li>";
    }
    echo "</ul>";
}

echo "<h2>Expected HTML Form Structure:</h2>";
echo "<pre>";
echo "chapters[0][title] = 'Introduction to Programming'\n";
echo "chapters[0][description] = 'Basic concepts and fundamentals'\n";
echo "chapters[0][sub_chapters][0][title] = 'What is Programming?'\n";
echo "chapters[0][sub_chapters][0][description] = 'Understanding programming basics'\n";
echo "chapters[0][sub_chapters][1][title] = 'Setting up Environment'\n";
echo "chapters[0][sub_chapters][1][description] = 'Installing necessary tools'\n";
echo "chapters[1][title] = 'Variables and Data Types'\n";
echo "chapters[1][description] = 'Working with different data types'\n";
echo "chapters[1][sub_chapters][0][title] = 'Numbers and Strings'\n";
echo "chapters[1][sub_chapters][0][description] = 'Basic data types'\n";
echo "</pre>";
?>
