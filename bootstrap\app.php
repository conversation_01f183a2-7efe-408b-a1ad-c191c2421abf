<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->alias([
            'instructor' => \App\Http\Middleware\InstructorMiddleware::class,
            'permission' => \App\Http\Middleware\PermissionMiddleware::class,
            'rate.content' => \App\Http\Middleware\RateLimitContentCreation::class,
            'paypal.webhook' => \App\Http\Middleware\PayPalWebhookMiddleware::class,
            'secure.file' => \App\Http\Middleware\SecureFileAccess::class,
            'course.material' => \App\Http\Middleware\CourseMaterialAccess::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
