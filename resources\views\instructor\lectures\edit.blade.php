@extends('layouts.app')

@section('title', 'Edit Lecture - ' . $lecture->title)

@push('styles')
<style>
body {
    background-color: #000;
}

.content-editor {
    min-height: 300px;
}

.quiz-question {
    background-color: #374151;
    border: 1px solid #4b5563;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.resource-item {
    background-color: #374151;
    border: 1px solid #4b5563;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

/* Auto-save indicator styles for lecture edit */
.lecture-edit-save-indicator {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.lecture-edit-save-indicator.bg-blue-600 {
    background-color: rgba(37, 99, 235, 0.9);
}

.lecture-edit-save-indicator.bg-green-600 {
    background-color: rgba(22, 163, 74, 0.9);
}

.lecture-edit-save-indicator.bg-red-600 {
    background-color: rgba(220, 38, 38, 0.9);
}

.lecture-edit-save-indicator.bg-yellow-600 {
    background-color: rgba(217, 119, 6, 0.9);
}

/* Spinner animation */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.fa-spin {
    animation: spin 1s linear infinite;
}
</style>
@endpush

@section('content')
<div class="min-h-screen bg-black text-white">
    <!-- Header -->
    <div class="bg-gradient-to-br from-black via-gray-900 to-black border-b border-gray-800">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-4">
                        <a href="{{ route('instructor.courses.show', $course) }}" 
                           class="text-gray-400 hover:text-red-500 transition-colors">
                            <i class="fas fa-arrow-left text-lg"></i>
                        </a>
                        <div>
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    @if($lecture->type === 'video') bg-red-600 text-white
                                    @elseif($lecture->type === 'text') bg-blue-600 text-white
                                    @elseif($lecture->type === 'quiz') bg-green-600 text-white
                                    @elseif($lecture->type === 'assignment') bg-purple-600 text-white
                                    @else bg-gray-600 text-white @endif">
                                    {{ ucfirst($lecture->type) }}
                                </span>
                            </div>
                            <h1 class="text-3xl font-bold text-white">Edit <span class="text-red-500">Lecture</span></h1>
                            <p class="text-gray-400 mt-1">{{ $course->title }} → {{ $chapter->title }}</p>
                        </div>
                    </div>
                </div>

                <!-- Breadcrumb -->
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <div class="flex items-center">
                                <a href="{{ route('instructor.courses.show', $course) }}" class="text-sm font-medium text-gray-400 hover:text-red-500">
                                    Course Details
                                </a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-gray-600 mr-4"></i>
                                <span class="text-sm font-medium text-red-500">Edit Lecture</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Form -->
            <div class="lg:col-span-2">
                <div class="bg-gray-900 rounded-xl border border-gray-800 overflow-hidden">
                    <div class="p-6">
                        <form action="{{ route('instructor.courses.chapters.lectures.update', [$course, $chapter, $lecture]) }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            @method('PUT')

                            <!-- Lecture Title -->
                            <div class="mb-6">
                                <label for="title" class="block text-sm font-medium text-gray-300 mb-2">
                                    Lecture Title <span class="text-red-500">*</span>
                                </label>
                                <input type="text" 
                                       id="title" 
                                       name="title" 
                                       value="{{ old('title', $lecture->title) }}"
                                       class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                       placeholder="Enter lecture title"
                                       required>
                                @error('title')
                                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Lecture Type -->
                            <div class="mb-6">
                                <label for="type" class="block text-sm font-medium text-gray-300 mb-2">
                                    Lecture Type <span class="text-red-500">*</span>
                                </label>
                                <select id="type" 
                                        name="type" 
                                        class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                        required>
                                    @foreach($lectureTypes as $value => $label)
                                        <option value="{{ $value }}" {{ old('type', $lecture->type) === $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('type')
                                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Lecture Description -->
                            <div class="mb-6">
                                <label for="description" class="block text-sm font-medium text-gray-300 mb-2">
                                    Lecture Description
                                </label>
                                <textarea id="description" 
                                          name="description" 
                                          rows="3"
                                          class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                          placeholder="Brief description of this lecture">{{ old('description', $lecture->description) }}</textarea>
                                @error('description')
                                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Dynamic Content Based on Type -->
                            <div id="content-sections">
                                <!-- Video Content -->
                                <div id="video-content" class="content-section mb-6" style="display: {{ old('type', $lecture->type) === 'video' ? 'block' : 'none' }}">
                                    <label for="video_url" class="block text-sm font-medium text-gray-300 mb-2">
                                        Video URL <span class="text-red-500">*</span>
                                    </label>
                                    <input type="url"
                                           id="video_url"
                                           name="video_url"
                                           value="{{ old('video_url', $lecture->video_url) }}"
                                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                           placeholder="https://youtube.com/watch?v=... or direct video URL">
                                    @error('video_url')
                                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Text/Assignment Content -->
                                <div id="text-content" class="content-section mb-6" style="display: {{ in_array(old('type', $lecture->type), ['text', 'assignment']) ? 'block' : 'none' }}">
                                    <label for="content" class="block text-sm font-medium text-gray-300 mb-2">
                                        Content
                                    </label>
                                    <textarea id="content" 
                                              name="content" 
                                              rows="10"
                                              class="content-editor w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                              placeholder="Enter your content here...">{{ old('content', $lecture->content) }}</textarea>
                                    @error('content')
                                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Quiz Content -->
                                <div id="quiz-content" class="content-section mb-6" style="display: {{ old('type', $lecture->type) === 'quiz' ? 'block' : 'none' }}">
                                    <div class="mb-4">
                                        <label for="quiz_passing_score" class="block text-sm font-medium text-gray-300 mb-2">
                                            Passing Score (%)
                                        </label>
                                        <input type="number" 
                                               id="quiz_passing_score" 
                                               name="quiz_passing_score" 
                                               value="{{ old('quiz_passing_score', $lecture->quiz_passing_score ?? 70) }}"
                                               min="0" max="100"
                                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
                                    </div>
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" 
                                                   name="quiz_allow_retakes" 
                                                   value="1" 
                                                   {{ old('quiz_allow_retakes', $lecture->quiz_allow_retakes) ? 'checked' : '' }}
                                                   class="text-red-500 focus:ring-red-500 focus:ring-2">
                                            <span class="ml-2 text-gray-300">Allow retakes</span>
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-400">Quiz questions can be managed after saving the lecture.</p>
                                </div>

                                <!-- Resource Content -->
                                <div id="resource-content" class="content-section mb-6" style="display: {{ old('type', $lecture->type) === 'resource' ? 'block' : 'none' }}">
                                    <label for="resource_files" class="block text-sm font-medium text-gray-300 mb-2">
                                        Upload Resources
                                    </label>
                                    <input type="file" 
                                           id="resource_files" 
                                           name="resource_files[]" 
                                           multiple
                                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-red-600 file:text-white hover:file:bg-red-700">
                                    <p class="mt-1 text-sm text-gray-400">Select multiple files to upload as resources</p>
                                    @error('resource_files')
                                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <!-- Duration -->
                            <div class="mb-6" id="duration-field" style="display: {{ old('type', $lecture->type) === 'video' ? 'block' : 'none' }}">
                                <label for="duration_minutes" class="block text-sm font-medium text-gray-300 mb-2">
                                    Duration (minutes) <span class="text-red-500">*</span>
                                </label>
                                <input type="number"
                                       id="duration_minutes"
                                       name="duration_minutes"
                                       value="{{ old('duration_minutes', $lecture->duration_minutes) }}"
                                       min="1"
                                       class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                       placeholder="Estimated duration in minutes">
                                @error('duration_minutes')
                                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Publication Status -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Publication Status
                                </label>
                                <div class="flex items-center space-x-4">
                                    <label class="flex items-center">
                                        <input type="radio" 
                                               name="is_published" 
                                               value="1" 
                                               {{ old('is_published', $lecture->is_published) ? 'checked' : '' }}
                                               class="text-red-500 focus:ring-red-500 focus:ring-2">
                                        <span class="ml-2 text-gray-300">Published</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" 
                                               name="is_published" 
                                               value="0" 
                                               {{ !old('is_published', $lecture->is_published) ? 'checked' : '' }}
                                               class="text-red-500 focus:ring-red-500 focus:ring-2">
                                        <span class="ml-2 text-gray-300">Draft</span>
                                    </label>
                                </div>
                                @error('is_published')
                                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Free Preview -->
                            <div class="mb-8">
                                <label class="flex items-center">
                                    <input type="checkbox" 
                                           name="is_free_preview" 
                                           value="1" 
                                           {{ old('is_free_preview', $lecture->is_free_preview) ? 'checked' : '' }}
                                           class="text-red-500 focus:ring-red-500 focus:ring-2">
                                    <span class="ml-2 text-gray-300">Allow free preview of this lecture</span>
                                </label>
                                @error('is_free_preview')
                                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex items-center justify-between pt-6 border-t border-gray-800">
                                <a href="{{ route('instructor.courses.show', $course) }}" 
                                   class="px-6 py-3 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg font-medium transition-colors">
                                    Cancel
                                </a>
                                <button type="submit" 
                                        class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                                    <i class="fas fa-save mr-2"></i>Update Lecture
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <div class="bg-gray-900 rounded-xl border border-gray-800 p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Lecture Information</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <span class="text-sm text-gray-400">Chapter:</span>
                            <p class="text-white font-medium">{{ $chapter->title }}</p>
                        </div>
                        
                        <div>
                            <span class="text-sm text-gray-400">Course:</span>
                            <p class="text-white font-medium">{{ $course->title }}</p>
                        </div>
                        
                        <div>
                            <span class="text-sm text-gray-400">Current Status:</span>
                            <span class="inline-block px-2 py-1 text-xs font-medium rounded-full {{ $lecture->is_published ? 'bg-green-600 text-white' : 'bg-yellow-600 text-black' }}">
                                {{ $lecture->is_published ? 'Published' : 'Draft' }}
                            </span>
                        </div>
                        
                        @if($lecture->duration_minutes)
                        <div>
                            <span class="text-sm text-gray-400">Duration:</span>
                            <p class="text-white">{{ $lecture->duration_minutes }} minutes</p>
                        </div>
                        @endif
                        
                        <div>
                            <span class="text-sm text-gray-400">Created:</span>
                            <p class="text-white">{{ $lecture->created_at->format('M j, Y') }}</p>
                        </div>
                        
                        <div>
                            <span class="text-sm text-gray-400">Last Updated:</span>
                            <p class="text-white">{{ $lecture->updated_at->format('M j, Y') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('type');
    const contentSections = document.querySelectorAll('.content-section');
    
    function toggleContentSections() {
        const selectedType = typeSelect.value;
        const durationField = document.getElementById('duration-field');

        contentSections.forEach(section => {
            section.style.display = 'none';
        });

        if (selectedType === 'video') {
            document.getElementById('video-content').style.display = 'block';
            if (durationField) durationField.style.display = 'block';
        } else if (selectedType === 'text' || selectedType === 'assignment') {
            document.getElementById('text-content').style.display = 'block';
            if (durationField) durationField.style.display = 'none';
        } else if (selectedType === 'quiz') {
            document.getElementById('quiz-content').style.display = 'block';
            if (durationField) durationField.style.display = 'none';
        } else if (selectedType === 'resource') {
            document.getElementById('resource-content').style.display = 'block';
            if (durationField) durationField.style.display = 'none';
        }
    }
    
    typeSelect.addEventListener('change', toggleContentSections);
    toggleContentSections(); // Initialize on page load

    // Auto-save functionality for lecture edit
    function initializeLectureEditAutoSave() {
        const autoSaveDelay = 2000; // 2 seconds delay
        let autoSaveTimeout;
        let saveIndicator;
        let isSubmitting = false;

        // Create save indicator
        function createSaveIndicator() {
            if (saveIndicator) return saveIndicator;

            saveIndicator = document.createElement('div');
            saveIndicator.className = 'lecture-edit-save-indicator fixed top-4 right-4 px-4 py-2 rounded-lg text-sm font-medium z-50 transition-all duration-300 opacity-0';
            saveIndicator.style.transform = 'translateY(-10px)';
            document.body.appendChild(saveIndicator);
            return saveIndicator;
        }

        // Show save status
        function showSaveStatus(status, message) {
            const indicator = createSaveIndicator();

            // Update indicator appearance
            indicator.className = indicator.className.replace(/bg-\w+-\d+/g, '').replace(/text-\w+-\d+/g, '');

            switch (status) {
                case 'saving':
                    indicator.className += ' bg-blue-600 text-white';
                    indicator.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>' + message;
                    break;
                case 'saved':
                    indicator.className += ' bg-green-600 text-white';
                    indicator.innerHTML = '<i class="fas fa-check mr-2"></i>' + message;
                    break;
                case 'error':
                    indicator.className += ' bg-red-600 text-white';
                    indicator.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>' + message;
                    break;
                case 'validation-error':
                    indicator.className += ' bg-yellow-600 text-white';
                    indicator.innerHTML = '<i class="fas fa-exclamation-circle mr-2"></i>' + message;
                    break;
            }

            // Show indicator
            indicator.style.opacity = '1';
            indicator.style.transform = 'translateY(0)';

            // Auto-hide after delay (except for errors)
            if (status !== 'error' && status !== 'validation-error') {
                setTimeout(() => {
                    indicator.style.opacity = '0';
                    indicator.style.transform = 'translateY(-10px)';
                }, status === 'saving' ? 0 : 3000);
            } else {
                // Hide errors after 5 seconds
                setTimeout(() => {
                    indicator.style.opacity = '0';
                    indicator.style.transform = 'translateY(-10px)';
                }, 5000);
            }
        }

        // Auto-save function
        function autoSaveLecture() {
            if (isSubmitting) return; // Don't auto-save during form submission

            showSaveStatus('saving', 'Saving...');

            // Collect form data
            const form = document.getElementById('lecture-form');
            const formData = new FormData(form);
            formData.append('_method', 'PATCH');
            formData.append('auto_save', '1'); // Flag to indicate this is an auto-save
            formData.append('last_updated', '{{ $lecture->updated_at->toISOString() }}'); // For conflict detection

            // Send AJAX request
            fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSaveStatus('saved', 'Saved');
                } else if (data.conflict) {
                    showSaveStatus('error', 'Conflict detected - please refresh page');
                } else if (data.validation_errors) {
                    showSaveStatus('validation-error', 'Please fix validation errors');
                } else {
                    showSaveStatus('error', data.message || 'Save failed');
                }
            })
            .catch(error => {
                console.error('Auto-save error:', error);
                showSaveStatus('error', 'Network error - changes not saved');
            });
        }

        // Set up auto-save listeners
        const form = document.getElementById('lecture-form');
        const inputs = form.querySelectorAll('input, textarea, select');

        inputs.forEach(input => {
            const eventType = input.type === 'checkbox' || input.tagName === 'SELECT' ? 'change' : 'input';

            input.addEventListener(eventType, function() {
                // Clear existing timeout
                if (autoSaveTimeout) {
                    clearTimeout(autoSaveTimeout);
                }

                // Set new timeout
                autoSaveTimeout = setTimeout(() => {
                    autoSaveLecture();
                }, autoSaveDelay);
            });
        });

        // Prevent auto-save during form submission
        form.addEventListener('submit', function() {
            isSubmitting = true;
            if (autoSaveTimeout) {
                clearTimeout(autoSaveTimeout);
            }
            // Hide save indicator during submission
            if (saveIndicator) {
                saveIndicator.style.opacity = '0';
            }
        });

        // Cleanup function
        window.cleanupLectureEditAutoSave = function() {
            // Clear timeout
            if (autoSaveTimeout) {
                clearTimeout(autoSaveTimeout);
            }

            // Remove indicator
            if (saveIndicator && saveIndicator.parentNode) {
                saveIndicator.parentNode.removeChild(saveIndicator);
            }
        };

        // Handle page unload - try to save if there are unsaved changes
        window.addEventListener('beforeunload', function(e) {
            if (autoSaveTimeout) {
                // There are unsaved changes
                e.preventDefault();
                e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
                return e.returnValue;
            }
            // Cleanup on unload
            window.cleanupLectureEditAutoSave();
        });
    }

    initializeLectureEditAutoSave();
});
</script>
@endpush
@endsection
