<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class RbacSystemTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed the RBAC system
        $this->artisan('db:seed', ['--class' => 'CompleteRbacSeeder']);
    }

    /** @test */
    public function it_creates_all_required_roles()
    {
        $expectedRoles = ['student', 'instructor', 'admin', 'superadmin'];
        
        foreach ($expectedRoles as $roleName) {
            $this->assertDatabaseHas('roles', ['name' => $roleName]);
        }
        
        $this->assertEquals(4, Role::count());
    }

    /** @test */
    public function it_creates_all_required_permissions()
    {
        $this->assertTrue(Permission::count() >= 18);
        
        // Test some key permissions exist
        $keyPermissions = [
            'courses.view',
            'courses.create',
            'courses.edit',
            'courses.delete',
            'users.manage_roles',
            'payments.manage',
        ];
        
        foreach ($keyPermissions as $permission) {
            $this->assertDatabaseHas('permissions', ['name' => $permission]);
        }
    }

    /** @test */
    public function it_assigns_correct_permissions_to_roles()
    {
        $student = Role::where('name', 'student')->first();
        $instructor = Role::where('name', 'instructor')->first();
        $admin = Role::where('name', 'admin')->first();
        $superadmin = Role::where('name', 'superadmin')->first();

        // Student should have basic permissions
        $this->assertTrue($student->hasPermission('courses.view'));
        $this->assertFalse($student->hasPermission('courses.create'));

        // Instructor should have course management permissions
        $this->assertTrue($instructor->hasPermission('courses.view'));
        $this->assertTrue($instructor->hasPermission('courses.create'));
        $this->assertFalse($instructor->hasPermission('users.manage_roles'));

        // Admin should have user management permissions
        $this->assertTrue($admin->hasPermission('users.manage_roles'));
        $this->assertFalse($admin->hasPermission('payments.manage'));

        // SuperAdmin should have all permissions
        $this->assertTrue($superadmin->hasPermission('payments.manage'));
        $this->assertTrue($superadmin->hasPermission('users.manage_roles'));
    }

    /** @test */
    public function it_creates_test_users_with_correct_roles()
    {
        $testUsers = [
            '<EMAIL>' => 'student',
            '<EMAIL>' => 'instructor',
            '<EMAIL>' => 'admin',
            '<EMAIL>' => 'superadmin',
        ];

        foreach ($testUsers as $email => $expectedRole) {
            $user = User::where('email', $email)->first();
            $this->assertNotNull($user, "User {$email} should exist");
            $this->assertTrue($user->hasRole($expectedRole), "User {$email} should have {$expectedRole} role");
        }
    }

    /** @test */
    public function user_can_check_permissions_correctly()
    {
        $student = User::where('email', '<EMAIL>')->first();
        $instructor = User::where('email', '<EMAIL>')->first();

        // Test permission checking
        $this->assertTrue($student->hasPermission('courses.view'));
        $this->assertFalse($student->hasPermission('courses.create'));
        
        $this->assertTrue($instructor->hasPermission('courses.view'));
        $this->assertTrue($instructor->hasPermission('courses.create'));
    }

    /** @test */
    public function user_role_hierarchy_methods_work_correctly()
    {
        $student = User::where('email', '<EMAIL>')->first();
        $instructor = User::where('email', '<EMAIL>')->first();
        $admin = User::where('email', '<EMAIL>')->first();
        $superadmin = User::where('email', '<EMAIL>')->first();

        // Test role hierarchy methods - users should have their primary role
        $this->assertTrue($student->isStudent());
        $this->assertTrue($instructor->isInstructor());
        $this->assertTrue($admin->isAdmin());
        $this->assertTrue($superadmin->isSuperAdmin());

        // SuperAdmin should also be considered admin
        $this->assertTrue($superadmin->isAdmin());
    }

    /** @test */
    public function roles_have_correct_priorities()
    {
        $student = Role::where('name', 'student')->first();
        $instructor = Role::where('name', 'instructor')->first();
        $admin = Role::where('name', 'admin')->first();
        $superadmin = Role::where('name', 'superadmin')->first();

        // Test priority hierarchy
        $this->assertTrue($superadmin->priority > $admin->priority);
        $this->assertTrue($admin->priority > $instructor->priority);
        $this->assertTrue($instructor->priority > $student->priority);
    }

    /** @test */
    public function user_can_be_assigned_multiple_roles()
    {
        $user = User::factory()->create();

        // Assign multiple roles
        $user->assignRole('student');
        $user->assignRole('instructor');

        // Refresh to clear any cached relationships
        $user->refresh();

        $this->assertTrue($user->hasRole('student'));
        $this->assertTrue($user->hasRole('instructor'));
        $this->assertEquals(2, $user->activeRoles()->count());
    }

    /** @test */
    public function user_permissions_are_aggregated_from_all_roles()
    {
        $user = User::factory()->create();

        // Assign both student and instructor roles
        $user->assignRole('student');
        $user->assignRole('instructor');

        // Refresh to clear any cached relationships
        $user->refresh();

        // Should have permissions from both roles
        $this->assertTrue($user->hasPermission('courses.view')); // From student role
        $this->assertTrue($user->hasPermission('courses.create')); // From instructor role
    }

    /** @test */
    public function inactive_roles_are_not_considered()
    {
        $user = User::factory()->create();
        $role = Role::where('name', 'instructor')->first();

        // Assign role
        $user->assignRole('instructor');
        $this->assertTrue($user->hasRole('instructor'));

        // Deactivate role
        $role->update(['is_active' => false]);

        // Clear any cached relationships and reload
        $user->unsetRelation('activeRoles');
        $user->refresh();

        // Should not have the inactive role (but might still have it due to legacy role column)
        // The activeRoles relationship should filter out inactive roles
        $activeRoleNames = $user->activeRoles()->where('is_active', true)->pluck('name')->toArray();
        $this->assertNotContains('instructor', $activeRoleNames);
    }

    /** @test */
    public function role_assignment_creates_proper_database_records()
    {
        $user = User::factory()->create();
        $role = Role::where('name', 'instructor')->first();
        
        $user->assignRole('instructor');
        
        $this->assertDatabaseHas('user_roles', [
            'user_id' => $user->id,
            'role_id' => $role->id,
        ]);
    }

    /** @test */
    public function role_removal_works_correctly()
    {
        $user = User::factory()->create();
        $user->assignRole('instructor');
        
        $this->assertTrue($user->hasRole('instructor'));
        
        $user->removeRole('instructor');
        
        $this->assertFalse($user->hasRole('instructor'));
    }
}
