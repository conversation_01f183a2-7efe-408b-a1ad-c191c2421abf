<?php

namespace App\Http\Requests;

use App\Models\VideoContent;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreVideoContentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->isInstructor();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'slug' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-z0-9]+(?:-[a-z0-9]+)*$/',
                Rule::unique('video_contents', 'slug')
            ],
            'description' => 'nullable|string|max:1000',
            'youtube_url' => [
                'required',
                'url',
                'regex:/^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+$/',
                function ($attribute, $value, $fail) {
                    if (!VideoContent::isValidYouTubeUrl($value)) {
                        $fail('Please provide a valid YouTube URL.');
                    }
                }
            ],
            'course_id' => [
                'nullable',
                'exists:courses,id',
                function ($attribute, $value, $fail) {
                    if ($value && !auth()->user()->courses()->where('id', $value)->exists()) {
                        $fail('The selected course does not belong to you.');
                    }
                }
            ],
            'category' => 'nullable|string|max:100|alpha_dash',
            'tags' => 'nullable|string|max:500',
            'duration_seconds' => 'nullable|integer|min:1|max:86400', // Max 24 hours
            'status' => 'required|in:draft,published',
            'sort_order' => 'nullable|integer|min:0|max:9999',
            'is_featured' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'The video title is required.',
            'title.max' => 'The title cannot exceed 255 characters.',
            'slug.regex' => 'The slug must contain only lowercase letters, numbers, and hyphens.',
            'slug.unique' => 'This slug is already taken. Please choose a different one.',
            'youtube_url.required' => 'The YouTube URL is required.',
            'youtube_url.url' => 'Please provide a valid URL.',
            'youtube_url.regex' => 'Please provide a valid YouTube URL.',
            'description.max' => 'The description cannot exceed 1000 characters.',
            'category.alpha_dash' => 'The category can only contain letters, numbers, dashes, and underscores.',
            'duration_seconds.integer' => 'The duration must be a valid number of seconds.',
            'duration_seconds.min' => 'The duration must be at least 1 second.',
            'duration_seconds.max' => 'The duration cannot exceed 24 hours (86400 seconds).',
            'sort_order.integer' => 'The sort order must be a valid number.',
            'sort_order.min' => 'The sort order cannot be negative.',
            'sort_order.max' => 'The sort order cannot exceed 9999.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean up tags input
        if ($this->has('tags') && $this->tags) {
            $tags = array_map('trim', explode(',', $this->tags));
            $tags = array_filter($tags); // Remove empty tags
            $this->merge(['tags' => implode(',', $tags)]);
        }

        // Generate slug if not provided
        if (!$this->slug && $this->title) {
            $this->merge(['slug' => \Str::slug($this->title)]);
        }

        // Convert duration from minutes to seconds if needed
        if ($this->has('duration_minutes') && $this->duration_minutes) {
            $this->merge(['duration_seconds' => $this->duration_minutes * 60]);
        }
    }
}
