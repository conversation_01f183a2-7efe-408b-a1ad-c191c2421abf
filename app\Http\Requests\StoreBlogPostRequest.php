<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreBlogPostRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->isInstructor();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'slug' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-z0-9]+(?:-[a-z0-9]+)*$/',
                Rule::unique('blog_posts', 'slug')
            ],
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string|min:100',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'course_id' => [
                'nullable',
                'exists:courses,id',
                function ($attribute, $value, $fail) {
                    if ($value && !auth()->user()->courses()->where('id', $value)->exists()) {
                        $fail('The selected course does not belong to you.');
                    }
                }
            ],
            'category' => 'nullable|string|max:100|alpha_dash',
            'tags' => 'nullable|string|max:500',
            'status' => 'required|in:draft,published',
            'allow_comments' => 'boolean',
            'seo_meta' => 'nullable|array',
            'seo_meta.title' => 'nullable|string|max:60',
            'seo_meta.description' => 'nullable|string|max:160',
            'seo_meta.keywords' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'The blog post title is required.',
            'title.max' => 'The title cannot exceed 255 characters.',
            'slug.regex' => 'The slug must contain only lowercase letters, numbers, and hyphens.',
            'slug.unique' => 'This slug is already taken. Please choose a different one.',
            'content.required' => 'The blog post content is required.',
            'content.min' => 'The content must be at least 100 characters long.',
            'featured_image.image' => 'The featured image must be a valid image file.',
            'featured_image.mimes' => 'The featured image must be a JPEG, PNG, JPG, or GIF file.',
            'featured_image.max' => 'The featured image cannot exceed 2MB in size.',
            'category.alpha_dash' => 'The category can only contain letters, numbers, dashes, and underscores.',
            'seo_meta.title.max' => 'The SEO title cannot exceed 60 characters.',
            'seo_meta.description.max' => 'The SEO description cannot exceed 160 characters.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean up tags input
        if ($this->has('tags') && $this->tags) {
            $tags = array_map('trim', explode(',', $this->tags));
            $tags = array_filter($tags); // Remove empty tags
            $this->merge(['tags' => implode(',', $tags)]);
        }

        // Generate slug if not provided
        if (!$this->slug && $this->title) {
            $this->merge(['slug' => \Str::slug($this->title)]);
        }
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'seo_meta.title' => 'SEO title',
            'seo_meta.description' => 'SEO description',
            'seo_meta.keywords' => 'SEO keywords',
        ];
    }
}
