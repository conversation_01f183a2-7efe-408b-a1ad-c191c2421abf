/**
 * Authentication Forms JavaScript
 * Handles password visibility toggle functionality
 */

/**
 * Toggle password visibility for input fields
 * @param {string} fieldId - The ID of the password field
 */
function togglePasswordVisibility(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const eyeClosed = document.getElementById(fieldId + '-eye-closed');
    const eyeOpen = document.getElementById(fieldId + '-eye-open');
    
    if (!passwordField || !eyeClosed || !eyeOpen) {
        console.error('Password visibility toggle: Required elements not found for field:', fieldId);
        return;
    }
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        eyeClosed.classList.add('hidden');
        eyeOpen.classList.remove('hidden');
    } else {
        passwordField.type = 'password';
        eyeClosed.classList.remove('hidden');
        eyeOpen.classList.add('hidden');
    }
}

// Initialize password visibility toggles when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add keyboard support for password toggle buttons
    const toggleButtons = document.querySelectorAll('[onclick*="togglePasswordVisibility"]');
    
    toggleButtons.forEach(button => {
        // Add keyboard accessibility
        button.setAttribute('tabindex', '0');
        button.setAttribute('role', 'button');
        button.setAttribute('aria-label', 'Toggle password visibility');
        
        // Handle keyboard events
        button.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                button.click();
            }
        });
    });
});