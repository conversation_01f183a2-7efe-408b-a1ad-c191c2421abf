<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('courses', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('title');
            $table->string('subtitle')->nullable(); // Udemy-style subtitle
            $table->string('slug')->unique();
            $table->text('description');
            $table->longText('what_you_will_learn')->nullable(); // JSON array of learning outcomes
            $table->longText('requirements')->nullable(); // JSON array of requirements
            $table->longText('target_audience')->nullable(); // JSON array of target audience
            $table->string('category');
            $table->string('subcategory')->nullable();
            $table->string('language')->default('English');
            $table->enum('level', ['beginner', 'intermediate', 'advanced', 'all_levels']);
            $table->decimal('price', 8, 2)->default(0);
            $table->decimal('original_price', 8, 2)->nullable(); // For discounts
            $table->string('currency', 3)->default('USD');
            $table->string('image')->nullable(); // Course thumbnail
            $table->string('promotional_video')->nullable(); // Course preview video
            $table->boolean('featured')->default(false);
            $table->enum('status', ['draft', 'under_review', 'published', 'archived'])->default('draft');
            $table->uuid('instructor_id');
            $table->integer('total_duration_minutes')->default(0); // Auto-calculated
            $table->integer('total_lectures')->default(0); // Auto-calculated
            $table->integer('total_chapters')->default(0); // Auto-calculated
            $table->float('average_rating', 2, 1)->default(0); // Auto-calculated
            $table->integer('total_reviews')->default(0); // Auto-calculated
            $table->integer('total_students')->default(0); // Auto-calculated
            $table->timestamp('published_at')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('instructor_id')->references('id')->on('users')->onDelete('cascade');

            // Indexes for performance
            $table->index(['status', 'featured', 'created_at']);
            $table->index(['instructor_id', 'status']);
            $table->index(['category', 'status']);
            $table->index(['level', 'status']);
            $table->index(['price', 'status']);
            $table->index('slug');
            $table->index('published_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('courses');
    }
};
