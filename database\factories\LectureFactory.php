<?php

namespace Database\Factories;

use App\Models\Lecture;
use App\Models\Chapter;
use App\Models\Course;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Lecture>
 */
class LectureFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Lecture::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $type = $this->faker->randomElement(['video', 'text', 'quiz', 'assignment', 'resource']);
        
        return [
            'title' => $this->faker->sentence(4),
            'slug' => $this->faker->slug(),
            'description' => $this->faker->paragraph(),
            'type' => $type,
            'chapter_id' => Chapter::factory(),
            'course_id' => Course::factory(),
            'instructor_id' => User::factory(),
            'sort_order' => $this->faker->numberBetween(1, 20),
            'is_published' => $this->faker->boolean(70), // 70% chance of being published
            'is_free_preview' => $this->faker->boolean(15), // 15% chance of being free preview
            'duration_minutes' => $type === 'video' ? $this->faker->numberBetween(5, 60) : 0,
            'content' => $type === 'text' ? $this->faker->paragraphs(3, true) : null,
            'video_url' => $type === 'video' ? 'https://youtube.com/watch?v=' . $this->faker->regexify('[A-Za-z0-9_-]{11}') : null,
            'video_provider' => $type === 'video' ? 'youtube' : null,
            'video_id' => $type === 'video' ? $this->faker->regexify('[A-Za-z0-9_-]{11}') : null,
            'video_thumbnail' => $type === 'video' ? 'https://img.youtube.com/vi/' . $this->faker->regexify('[A-Za-z0-9_-]{11}') . '/maxresdefault.jpg' : null,
            'video_metadata' => $type === 'video' ? [
                'provider' => 'youtube',
                'video_id' => $this->faker->regexify('[A-Za-z0-9_-]{11}'),
                'thumbnail' => 'https://img.youtube.com/vi/' . $this->faker->regexify('[A-Za-z0-9_-]{11}') . '/maxresdefault.jpg'
            ] : null,
            'resources' => $type === 'resource' ? [
                [
                    'name' => $this->faker->words(3, true) . '.pdf',
                    'file_path' => 'courses/' . $this->faker->uuid() . '.pdf',
                    'file_size' => $this->faker->numberBetween(100000, 5000000),
                    'mime_type' => 'application/pdf'
                ]
            ] : null,
            'quiz_data' => $type === 'quiz' ? [
                'questions' => [
                    [
                        'question' => $this->faker->sentence() . '?',
                        'type' => 'multiple_choice',
                        'options' => [
                            $this->faker->sentence(3),
                            $this->faker->sentence(3),
                            $this->faker->sentence(3),
                            $this->faker->sentence(3)
                        ],
                        'correct_answer' => 0
                    ]
                ]
            ] : null,
            'quiz_passing_score' => $type === 'quiz' ? $this->faker->numberBetween(60, 90) : 80,
            'quiz_allow_retakes' => $type === 'quiz' ? $this->faker->boolean(80) : true,
            'is_mandatory' => $this->faker->boolean(85), // 85% chance of being mandatory
            'estimated_completion_minutes' => $type !== 'video' ? $this->faker->numberBetween(5, 30) : null,
        ];
    }

    /**
     * Indicate that the lecture is a video.
     */
    public function video(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'video',
            'duration_minutes' => $this->faker->numberBetween(5, 60),
            'video_url' => 'https://youtube.com/watch?v=' . $this->faker->regexify('[A-Za-z0-9_-]{11}'),
            'video_provider' => 'youtube',
            'video_id' => $this->faker->regexify('[A-Za-z0-9_-]{11}'),
            'video_thumbnail' => 'https://img.youtube.com/vi/' . $this->faker->regexify('[A-Za-z0-9_-]{11}') . '/maxresdefault.jpg',
            'video_metadata' => [
                'provider' => 'youtube',
                'video_id' => $this->faker->regexify('[A-Za-z0-9_-]{11}'),
                'thumbnail' => 'https://img.youtube.com/vi/' . $this->faker->regexify('[A-Za-z0-9_-]{11}') . '/maxresdefault.jpg'
            ],
            'content' => null,
            'resources' => null,
            'quiz_data' => null,
            'quiz_passing_score' => 80,
            'quiz_allow_retakes' => true,
        ]);
    }

    /**
     * Indicate that the lecture is text content.
     */
    public function text(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'text',
            'content' => $this->faker->paragraphs(5, true),
            'duration_minutes' => 0,
            'estimated_completion_minutes' => $this->faker->numberBetween(5, 20),
            'video_url' => null,
            'video_provider' => null,
            'video_id' => null,
            'video_thumbnail' => null,
            'video_metadata' => null,
            'resources' => null,
            'quiz_data' => null,
            'quiz_passing_score' => 80,
            'quiz_allow_retakes' => true,
        ]);
    }

    /**
     * Indicate that the lecture is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_published' => true,
        ]);
    }

    /**
     * Indicate that the lecture is a draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_published' => false,
        ]);
    }

    /**
     * Indicate that the lecture is a free preview.
     */
    public function freePreview(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_free_preview' => true,
        ]);
    }
}
