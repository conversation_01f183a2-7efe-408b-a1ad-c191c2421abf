@extends('instructor.layouts.app')

@section('title', $blogPost->title . ' - Blog Post Details')

@section('content')
<div class="py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-4 mb-4">
                <a href="{{ route('instructor.blog-posts.index') }}" class="text-gray-400 hover:text-white transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <div class="flex-1">
                    <h1 class="text-3xl font-bold text-white">{{ $blogPost->title }}</h1>
                    <div class="flex items-center space-x-4 mt-2">
                        <span class="px-3 py-1 bg-{{ $blogPost->is_published ? 'green' : 'yellow' }}-600 text-white rounded-full text-sm">
                            {{ $blogPost->is_published ? 'Published' : 'Draft' }}
                        </span>
                        @if($blogPost->category)
                            <span class="text-gray-400">{{ ucfirst($blogPost->category) }}</span>
                        @endif
                        <span class="text-gray-400">{{ $blogPost->created_at->format('M d, Y') }}</span>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('instructor.blog-posts.edit', $blogPost) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                        Edit Post
                    </a>
                    <form method="POST" action="{{ route('instructor.blog-posts.toggle-status', $blogPost) }}" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="bg-{{ $blogPost->is_published ? 'yellow' : 'green' }}-600 hover:bg-{{ $blogPost->is_published ? 'yellow' : 'green' }}-700 text-white px-4 py-2 rounded-lg transition-colors">
                            {{ $blogPost->is_published ? 'Unpublish' : 'Publish' }}
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Blog Post Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Featured Image -->
                @if($blogPost->featured_image_url)
                    <div class="mb-8">
                        <img src="{{ $blogPost->featured_image_url }}" alt="{{ $blogPost->title }}" class="w-full h-64 object-cover rounded-lg">
                    </div>
                @endif

                <!-- Excerpt -->
                @if($blogPost->excerpt)
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-8">
                        <h2 class="text-xl font-bold text-white mb-4">Excerpt</h2>
                        <p class="text-gray-300 leading-relaxed">{{ $blogPost->excerpt }}</p>
                    </div>
                @endif

                <!-- Content -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-8">
                    <h2 class="text-xl font-bold text-white mb-4">Content</h2>
                    <div class="prose prose-invert max-w-none">
                        {!! nl2br(e($blogPost->content)) !!}
                    </div>
                </div>

                <!-- SEO Meta -->
                @if($blogPost->meta_description)
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <h2 class="text-xl font-bold text-white mb-4">SEO Meta Description</h2>
                        <p class="text-gray-300">{{ $blogPost->meta_description }}</p>
                        <p class="text-gray-500 text-sm mt-2">Length: {{ strlen($blogPost->meta_description) }} characters</p>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Post Details -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-6">
                    <h3 class="text-lg font-bold text-white mb-4">Post Details</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Status</span>
                            <span class="text-white">{{ $blogPost->is_published ? 'Published' : 'Draft' }}</span>
                        </div>
                        @if($blogPost->category)
                            <div class="flex justify-between">
                                <span class="text-gray-400">Category</span>
                                <span class="text-white">{{ ucfirst($blogPost->category) }}</span>
                            </div>
                        @endif
                        @if($blogPost->course)
                            <div class="flex justify-between">
                                <span class="text-gray-400">Related Course</span>
                                <a href="{{ route('instructor.courses.show', $blogPost->course) }}" class="text-blue-400 hover:text-blue-300 transition-colors">{{ $blogPost->course->title }}</a>
                            </div>
                        @endif
                        <div class="flex justify-between">
                            <span class="text-gray-400">Created</span>
                            <span class="text-white">{{ $blogPost->created_at->format('M d, Y') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Last Updated</span>
                            <span class="text-white">{{ $blogPost->updated_at->format('M d, Y') }}</span>
                        </div>
                        @if($blogPost->published_at)
                            <div class="flex justify-between">
                                <span class="text-gray-400">Published</span>
                                <span class="text-white">{{ $blogPost->published_at->format('M d, Y') }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Tags -->
                @if($blogPost->tags && count($blogPost->tags) > 0)
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-6">
                        <h3 class="text-lg font-bold text-white mb-4">Tags</h3>
                        <div class="flex flex-wrap gap-2">
                            @foreach($blogPost->tags as $tag)
                                <span class="bg-gray-700 text-gray-300 px-3 py-1 rounded-full text-sm">{{ $tag }}</span>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Quick Actions -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-6">
                    <h3 class="text-lg font-bold text-white mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="{{ route('instructor.blog-posts.edit', $blogPost) }}" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors block text-center">
                            Edit Post
                        </a>
                        <form method="POST" action="{{ route('instructor.blog-posts.toggle-status', $blogPost) }}" class="w-full">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="w-full bg-{{ $blogPost->is_published ? 'yellow' : 'green' }}-600 hover:bg-{{ $blogPost->is_published ? 'yellow' : 'green' }}-700 text-white py-2 px-4 rounded-lg transition-colors">
                                {{ $blogPost->is_published ? 'Unpublish' : 'Publish' }}
                            </button>
                        </form>
                        <a href="{{ route('instructor.blog-posts.create') }}" class="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors block text-center">
                            Create New Post
                        </a>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    <h3 class="text-lg font-bold text-white mb-4">Statistics</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Word Count</span>
                            <span class="text-white">{{ str_word_count(strip_tags($blogPost->content)) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Character Count</span>
                            <span class="text-white">{{ strlen(strip_tags($blogPost->content)) }}</span>
                        </div>
                        @if($blogPost->excerpt)
                            <div class="flex justify-between">
                                <span class="text-gray-400">Excerpt Length</span>
                                <span class="text-white">{{ strlen($blogPost->excerpt) }} chars</span>
                            </div>
                        @endif
                        @if($blogPost->meta_description)
                            <div class="flex justify-between">
                                <span class="text-gray-400">Meta Description</span>
                                <span class="text-white">{{ strlen($blogPost->meta_description) }} chars</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.prose {
    color: #e5e7eb;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    color: #ffffff;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
}

.prose p {
    margin-bottom: 1em;
    line-height: 1.7;
}

.prose ul, .prose ol {
    margin: 1em 0;
    padding-left: 1.5em;
}

.prose li {
    margin-bottom: 0.5em;
}

.prose blockquote {
    border-left: 4px solid #ef4444;
    padding-left: 1em;
    margin: 1.5em 0;
    font-style: italic;
    color: #d1d5db;
}

.prose code {
    background-color: #374151;
    padding: 0.2em 0.4em;
    border-radius: 0.25rem;
    font-size: 0.875em;
}

.prose pre {
    background-color: #1f2937;
    padding: 1em;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1.5em 0;
}

.prose a {
    color: #60a5fa;
    text-decoration: underline;
}

.prose a:hover {
    color: #93c5fd;
}
</style>
@endpush
@endsection
