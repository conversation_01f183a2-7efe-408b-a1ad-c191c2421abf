@extends('instructor.layouts.app')

@section('title', 'Instructor Dashboard - Escape Matrix Academy')

@section('content')
<div class="py-12">
    <div class="container mx-auto px-4">
        <!-- Welcome Section -->
        <div class="mb-8">
            <h1 class="text-3xl md:text-4xl font-bold text-white mb-2">
                Welcome back, <span class="text-red-500">{{ $instructor->name }}</span>
            </h1>
            <p class="text-gray-400">Manage your educational content, track payments, and monitor student progress</p>
        </div>

        <!-- Quick Actions -->
        <div class="mb-8">
            <div class="flex flex-wrap gap-4">
                <a href="{{ route('instructor.users.index') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-users mr-2"></i>Manage Students
                </a>
                <a href="{{ route('instructor.payments.index') }}" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-dollar-sign mr-2"></i>View Payments
                </a>
                <a href="{{ route('instructor.videos.create') }}" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-video mr-2"></i>Add Video
                </a>
                <a href="{{ route('instructor.blog-posts.create') }}" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-blog mr-2"></i>Write Blog Post
                </a>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-600 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-2xl font-bold text-white">{{ $stats['courses'] }}</p>
                        <p class="text-gray-400">Courses</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-600 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-2xl font-bold text-white">{{ $stats['learning_materials'] }}</p>
                        <p class="text-gray-400">Materials</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-600 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-2xl font-bold text-white">{{ $stats['ebooks'] }}</p>
                        <p class="text-gray-400">Ebooks</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-red-600 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-2xl font-bold text-white">{{ $stats['total_students'] }}</p>
                        <p class="text-gray-400">Students</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <h3 class="text-xl font-bold text-white mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <a href="{{ route('instructor.learning-materials.create') }}" class="flex items-center p-3 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors">
                        <div class="p-2 bg-green-600 rounded-lg mr-3">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                        </div>
                        <span class="text-white">Add Learning Material</span>
                    </a>
                    
                    <a href="{{ route('instructor.ebooks.create') }}" class="flex items-center p-3 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors">
                        <div class="p-2 bg-purple-600 rounded-lg mr-3">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                        </div>
                        <span class="text-white">Upload Ebook</span>
                    </a>
                    
                    <a href="{{ route('instructor.resources.create') }}" class="flex items-center p-3 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors">
                        <div class="p-2 bg-blue-600 rounded-lg mr-3">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                        </div>
                        <span class="text-white">Add Resource</span>
                    </a>
                    
                    <a href="{{ route('instructor.content-files.create') }}" class="flex items-center p-3 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors">
                        <div class="p-2 bg-yellow-600 rounded-lg mr-3">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                        </div>
                        <span class="text-white">Upload File</span>
                    </a>
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <h3 class="text-xl font-bold text-white mb-4">Course Status</h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-400">Published Courses</span>
                        <span class="text-green-500 font-semibold">{{ $stats['published_courses'] }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-400">Draft Courses</span>
                        <span class="text-yellow-500 font-semibold">{{ $stats['draft_courses'] }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-400">Total Resources</span>
                        <span class="text-blue-500 font-semibold">{{ $stats['resources'] }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-400">Content Files</span>
                        <span class="text-purple-500 font-semibold">{{ $stats['content_files'] }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <h3 class="text-xl font-bold text-white mb-4">Recent Materials</h3>
                @if($recentMaterials->count() > 0)
                    <div class="space-y-3">
                        @foreach($recentMaterials as $material)
                            <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                                <div>
                                    <h4 class="text-white font-medium">{{ $material->title }}</h4>
                                    <p class="text-gray-400 text-sm">{{ $material->course->title ?? 'No course' }}</p>
                                </div>
                                <span class="text-xs text-gray-500">{{ $material->updated_at->diffForHumans() }}</span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-400">No materials yet. <a href="{{ route('instructor.learning-materials.create') }}" class="text-red-500 hover:underline">Create your first one</a>.</p>
                @endif
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <h3 class="text-xl font-bold text-white mb-4">Recent Ebooks</h3>
                @if($recentEbooks->count() > 0)
                    <div class="space-y-3">
                        @foreach($recentEbooks as $ebook)
                            <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                                <div>
                                    <h4 class="text-white font-medium">{{ $ebook->title }}</h4>
                                    <p class="text-gray-400 text-sm">{{ $ebook->author ?? 'No author' }}</p>
                                </div>
                                <span class="text-xs text-gray-500">{{ $ebook->updated_at->diffForHumans() }}</span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-400">No ebooks yet. <a href="{{ route('instructor.ebooks.create') }}" class="text-red-500 hover:underline">Upload your first one</a>.</p>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
