<?php

namespace Tests\Unit;

use App\Services\PayPalService;
use Tests\TestCase;
use ReflectionClass;
use Illuminate\Support\Facades\Http;
use Mockery;

class PayPalServiceTest extends TestCase
{
    /**
     * Test that getApprovalUrl method correctly handles PayPal API response format
     */
    public function test_get_approval_url_with_correct_format()
    {
        // Set up configuration
        config(['services.paypal' => [
            'mode' => 'sandbox',
            'client_id' => 'test_client_id',
            'client_secret' => 'test_client_secret',
            'currency' => 'USD',
            'platform_fee_percentage' => 20,
        ]]);

        // Create PayPal service instance
        $service = new PayPalService();
        
        // Use reflection to access private method
        $reflection = new ReflectionClass($service);
        $method = $reflection->getMethod('getApprovalUrl');
        $method->setAccessible(true);
        
        // Test with correct PayPal API response format (array of arrays)
        $links = [
            [
                'href' => 'https://api.sandbox.paypal.com/v2/checkout/orders/5XE84616NU588812F',
                'rel' => 'self',
                'method' => 'GET'
            ],
            [
                'href' => 'https://www.sandbox.paypal.com/checkoutnow?token=5XE84616NU588812F',
                'rel' => 'approve',
                'method' => 'GET'
            ],
            [
                'href' => 'https://api.sandbox.paypal.com/v2/checkout/orders/5XE84616NU588812F/capture',
                'rel' => 'capture',
                'method' => 'POST'
            ]
        ];
        
        $result = $method->invoke($service, $links);
        
        $this->assertEquals('https://www.sandbox.paypal.com/checkoutnow?token=5XE84616NU588812F', $result);
    }
    
    /**
     * Test that getApprovalUrl method returns null when no approve link is found
     */
    public function test_get_approval_url_returns_null_when_no_approve_link()
    {
        // Set up configuration
        config(['services.paypal' => [
            'mode' => 'sandbox',
            'client_id' => 'test_client_id',
            'client_secret' => 'test_client_secret',
            'currency' => 'USD',
            'platform_fee_percentage' => 20,
        ]]);

        $service = new PayPalService();
        
        $reflection = new ReflectionClass($service);
        $method = $reflection->getMethod('getApprovalUrl');
        $method->setAccessible(true);
        
        // Test with links that don't contain approve rel
        $links = [
            [
                'href' => 'https://api.sandbox.paypal.com/v2/checkout/orders/5XE84616NU588812F',
                'rel' => 'self',
                'method' => 'GET'
            ]
        ];
        
        $result = $method->invoke($service, $links);
        
        $this->assertNull($result);
    }
    
    /**
     * Test that getApprovalUrl method handles empty links array
     */
    public function test_get_approval_url_handles_empty_links()
    {
        // Set up configuration
        config(['services.paypal' => [
            'mode' => 'sandbox',
            'client_id' => 'test_client_id',
            'client_secret' => 'test_client_secret',
            'currency' => 'USD',
            'platform_fee_percentage' => 20,
        ]]);

        $service = new PayPalService();
        
        $reflection = new ReflectionClass($service);
        $method = $reflection->getMethod('getApprovalUrl');
        $method->setAccessible(true);
        
        $result = $method->invoke($service, []);
        
        $this->assertNull($result);
    }
    
    /**
     * Test that getApprovalUrl method handles malformed links gracefully
     */
    public function test_get_approval_url_handles_malformed_links()
    {
        // Set up configuration
        config(['services.paypal' => [
            'mode' => 'sandbox',
            'client_id' => 'test_client_id',
            'client_secret' => 'test_client_secret',
            'currency' => 'USD',
            'platform_fee_percentage' => 20,
        ]]);

        $service = new PayPalService();

        $reflection = new ReflectionClass($service);
        $method = $reflection->getMethod('getApprovalUrl');
        $method->setAccessible(true);

        // Test with malformed links (missing href)
        $links = [
            [
                'rel' => 'approve',
                'method' => 'GET'
                // Missing href
            ]
        ];

        $result = $method->invoke($service, $links);

        $this->assertNull($result);
    }

    /**
     * Test that capture request is sent without request body to avoid MALFORMED_REQUEST_JSON error
     */
    public function test_capture_order_sends_request_without_body()
    {
        // Set up configuration
        config(['services.paypal' => [
            'mode' => 'sandbox',
            'client_id' => 'test_client_id',
            'client_secret' => 'test_client_secret',
            'currency' => 'USD',
            'platform_fee_percentage' => 20,
        ]]);

        // Mock HTTP responses - the capture will fail due to no payment record, but we only care about the HTTP request format
        Http::fake([
            // Mock access token request
            'api.sandbox.paypal.com/v1/oauth2/token' => Http::response([
                'access_token' => 'test_access_token',
                'token_type' => 'Bearer',
                'expires_in' => 3600
            ], 200),

            // Mock capture request - should succeed without request body
            'api.sandbox.paypal.com/v2/checkout/orders/*/capture' => Http::response([
                'id' => 'test_order_id',
                'status' => 'COMPLETED',
                'purchase_units' => [
                    [
                        'payments' => [
                            'captures' => [
                                [
                                    'id' => 'test_capture_id',
                                    'status' => 'COMPLETED',
                                    'amount' => [
                                        'currency_code' => 'USD',
                                        'value' => '100.00'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);

        $service = new PayPalService();

        // This will fail because no payment record exists, but that's expected
        // We only care about verifying the HTTP request format
        $result = $service->captureOrder('test_order_id');

        // The result will be false due to no payment record, but the HTTP request should be correct
        $this->assertFalse($result['success']);
        $this->assertStringContainsString('Payment not found', $result['error']);

        // Most importantly, verify that the HTTP request was made without a request body
        // This is the key fix for the MALFORMED_REQUEST_JSON error
        Http::assertSent(function ($request) {
            return $request->url() === 'https://api.sandbox.paypal.com/v2/checkout/orders/test_order_id/capture'
                && $request->method() === 'POST'
                && $request->hasHeader('Content-Type', 'application/json')
                && $request->hasHeader('Authorization', 'Bearer test_access_token')
                && empty($request->body()); // This is the critical assertion - no request body
        });
    }
}
