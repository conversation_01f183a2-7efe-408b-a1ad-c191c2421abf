<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class LectureProgress extends Model
{
    use HasFactory;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'user_id',
        'lecture_id',
        'chapter_id',
        'course_id',
        'is_completed',
        'watch_time_seconds',
        'total_duration_seconds',
        'completion_percentage',
        'started_at',
        'completed_at',
        'last_accessed_at',
        'quiz_answers',
        'quiz_score',
        'quiz_passed',
        'quiz_attempts',
        'notes',
    ];

    protected $casts = [
        'is_completed' => 'boolean',
        'watch_time_seconds' => 'integer',
        'total_duration_seconds' => 'integer',
        'completion_percentage' => 'integer',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'last_accessed_at' => 'datetime',
        'quiz_answers' => 'array',
        'quiz_score' => 'integer',
        'quiz_passed' => 'boolean',
        'quiz_attempts' => 'integer',
        'notes' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
            if (empty($model->started_at)) {
                $model->started_at = now();
            }
        });

        static::saved(function ($model) {
            // Update enrollment progress when lecture progress is saved
            $model->updateEnrollmentProgress();
        });
    }

    /**
     * Get the user for this progress record.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the lecture for this progress record.
     */
    public function lecture(): BelongsTo
    {
        return $this->belongsTo(Lecture::class);
    }

    /**
     * Get the chapter for this progress record.
     */
    public function chapter(): BelongsTo
    {
        return $this->belongsTo(Chapter::class);
    }

    /**
     * Get the course for this progress record.
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Scope to filter completed progress.
     */
    public function scopeCompleted($query)
    {
        return $query->where('is_completed', true);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by course.
     */
    public function scopeByCourse($query, $courseId)
    {
        return $query->where('course_id', $courseId);
    }

    /**
     * Scope to filter by chapter.
     */
    public function scopeByChapter($query, $chapterId)
    {
        return $query->where('chapter_id', $chapterId);
    }

    /**
     * Check if the lecture is completed.
     */
    public function isCompleted(): bool
    {
        return $this->is_completed;
    }

    /**
     * Update watch time and completion percentage.
     */
    public function updateWatchTime(int $watchTimeSeconds): void
    {
        $this->watch_time_seconds = min($watchTimeSeconds, $this->total_duration_seconds);
        $this->completion_percentage = $this->calculateCompletionPercentage();
        $this->last_accessed_at = now();

        // Mark as completed if watch time is sufficient
        if ($this->completion_percentage >= 80 && !$this->is_completed) {
            $this->markAsCompleted();
        }

        $this->save();
    }

    /**
     * Calculate completion percentage based on watch time.
     */
    public function calculateCompletionPercentage(): int
    {
        if ($this->total_duration_seconds === 0) {
            return 0;
        }
        return min(100, round(($this->watch_time_seconds / $this->total_duration_seconds) * 100));
    }

    /**
     * Mark lecture as completed.
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'is_completed' => true,
            'completed_at' => now(),
            'completion_percentage' => 100,
        ]);
    }

    /**
     * Submit quiz answers.
     */
    public function submitQuizAnswers(array $answers): void
    {
        $this->quiz_attempts++;
        $this->quiz_answers = $answers;
        $this->quiz_score = $this->calculateQuizScore($answers);
        $this->quiz_passed = $this->quiz_score >= ($this->lecture->quiz_passing_score ?? 70);
        $this->last_accessed_at = now();

        // Mark as completed if quiz is passed
        if ($this->quiz_passed && !$this->is_completed) {
            $this->markAsCompleted();
        }

        $this->save();
    }

    /**
     * Calculate quiz score based on answers.
     */
    private function calculateQuizScore(array $answers): int
    {
        if (!$this->lecture->quiz_data) {
            return 0;
        }

        $questions = $this->lecture->quiz_data['questions'] ?? [];
        $totalQuestions = count($questions);
        $correctAnswers = 0;

        foreach ($questions as $index => $question) {
            $userAnswer = $answers[$index] ?? null;
            $correctAnswer = $question['correct_answer'] ?? null;

            if ($userAnswer === $correctAnswer) {
                $correctAnswers++;
            }
        }

        return $totalQuestions > 0 ? round(($correctAnswers / $totalQuestions) * 100) : 0;
    }

    /**
     * Add or update notes.
     */
    public function addNote(string $note, int $timestamp = null): void
    {
        $notes = $this->notes ?? [];
        $notes[] = [
            'content' => $note,
            'timestamp' => $timestamp ?? $this->watch_time_seconds,
            'created_at' => now()->toISOString(),
        ];
        $this->update(['notes' => $notes]);
    }

    /**
     * Get formatted watch time.
     */
    public function getFormattedWatchTime(): string
    {
        $minutes = floor($this->watch_time_seconds / 60);
        $seconds = $this->watch_time_seconds % 60;
        return sprintf('%02d:%02d', $minutes, $seconds);
    }

    /**
     * Get formatted total duration.
     */
    public function getFormattedTotalDuration(): string
    {
        $minutes = floor($this->total_duration_seconds / 60);
        $seconds = $this->total_duration_seconds % 60;
        return sprintf('%02d:%02d', $minutes, $seconds);
    }

    /**
     * Update enrollment progress when lecture progress changes.
     */
    private function updateEnrollmentProgress(): void
    {
        $enrollment = Enrollment::where('user_id', $this->user_id)
            ->where('course_id', $this->course_id)
            ->first();

        if ($enrollment) {
            // Update current lecture
            $enrollment->updateCurrentLecture($this->lecture_id);

            // Mark lecture as completed in enrollment if this progress is completed
            if ($this->is_completed) {
                $enrollment->markLectureCompleted($this->lecture_id);
            }

            // Update total watch time
            $totalWatchTime = LectureProgress::where('user_id', $this->user_id)
                ->where('course_id', $this->course_id)
                ->sum('watch_time_seconds');

            $enrollment->update([
                'total_watch_time_minutes' => round($totalWatchTime / 60),
            ]);

            // Check if course is completed
            $totalLectures = $enrollment->course->total_lectures;
            $completedLectures = LectureProgress::where('user_id', $this->user_id)
                ->where('course_id', $this->course_id)
                ->where('is_completed', true)
                ->count();

            if ($completedLectures >= $totalLectures && $enrollment->status !== 'completed') {
                $enrollment->markAsCompleted();
                $enrollment->issueCertificate();
            }
        }
    }
}
