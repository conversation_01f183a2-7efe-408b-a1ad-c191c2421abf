@extends('layouts.app')

@section('title', $course->title . ' - My Courses')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/course-viewer.css') }}">
@endpush

@push('scripts')
<script src="{{ asset('js/course-viewer.js') }}"></script>
@endpush

@section('content')
<!-- Meta tags for JavaScript -->
<meta name="course-id" content="{{ $course->slug }}">
@if(isset($currentLecture))
<meta name="current-lecture-id" content="{{ $currentLecture->id }}">
@endif

<!-- Course Viewer Container -->
<div class="course-viewer">
    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay"></div>

    <!-- Course Viewer Layout -->
    <div class="course-viewer-container">
        <!-- Course Sidebar -->
        <aside class="course-sidebar" role="complementary" aria-label="Course Navigation">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <button class="sidebar-close-btn" aria-label="Close sidebar">
                    <i class="fas fa-times"></i>
                </button>

                <h1 class="course-title">{{ $course->title }}</h1>

                <!-- Course Progress -->
                <div class="course-progress">
                    <div class="progress-bar-container">
                        <div class="progress-bar" id="progress-bar" style="width: {{ $enrollment->progress_percentage ?? 0 }}%"></div>
                    </div>
                    <div class="progress-text">
                        <span class="lecture-count">{{ $enrollment->completed_lectures ?? 0 }}/{{ $enrollment->total_lectures ?? 0 }} lessons</span>
                        <span class="progress-percentage" id="progress-percentage">{{ number_format($enrollment->progress_percentage ?? 0, 1) }}%</span>
                    </div>
                </div>
            </div>

            <!-- Curriculum Section -->
            <div class="curriculum-section">
                <h2 class="curriculum-title">
                    <button class="sidebar-hamburger-toggle" aria-label="Toggle sidebar">
                        <i class="fas fa-bars curriculum-icon"></i>
                    </button>
                    Course Content
                </h2>

                <!-- Chapters and Lectures -->
                @foreach($course->chapters as $chapterIndex => $chapter)
                    <div class="chapter {{ isset($currentLecture) && $chapter->lectures->contains('id', $currentLecture->id) ? 'expanded' : '' }}">
                        <div class="chapter-header">
                            <h3 class="chapter-title">
                                <span>{{ $chapterIndex + 1 }}. {{ $chapter->title }}</span>
                                <i class="fas fa-chevron-down chapter-toggle"></i>
                            </h3>
                            @if($chapter->description)
                                <p class="chapter-description">{{ $chapter->description }}</p>
                            @endif
                        </div>

                        <div class="lectures-list">
                            @foreach($chapter->lectures as $lectureIndex => $lecture)
                                @php
                                    $isCompleted = in_array($lecture->id, $completedLectureIds ?? []);
                                    $isCurrent = isset($currentLecture) && $currentLecture->id === $lecture->id;
                                    $lectureNumber = $lectureIndex + 1;
                                @endphp

                                <a href="{{ route('my-courses.lecture', [$course, $lecture]) }}"
                                   class="lecture-item {{ $isCurrent ? 'active' : ($isCompleted ? 'completed' : '') }}"
                                   data-lecture-id="{{ $lecture->id }}"
                                   data-lecture-title="{{ $lecture->title }}">

                                    <div class="lecture-status {{ $isCurrent ? 'current' : ($isCompleted ? 'completed' : 'pending') }}">
                                        @if($isCompleted)
                                            <i class="fas fa-check"></i>
                                        @elseif($isCurrent)
                                            <i class="fas fa-play"></i>
                                        @else
                                            {{ $lectureNumber }}
                                        @endif
                                    </div>

                                    <div class="lecture-content">
                                        <h4 class="lecture-title">{{ $lecture->title }}</h4>
                                        <div class="lecture-meta">
                                            @if($lecture->duration_minutes)
                                                <span class="lecture-duration">
                                                    <i class="fas fa-clock"></i>
                                                    {{ $lecture->duration_minutes }}min
                                                </span>
                                            @endif
                                            <span class="lecture-type">
                                                @switch($lecture->type)
                                                    @case('video')
                                                        <i class="fas fa-play-circle"></i> Video
                                                        @break
                                                    @case('text')
                                                        <i class="fas fa-file-text"></i> Reading
                                                        @break
                                                    @case('quiz')
                                                        <i class="fas fa-question-circle"></i> Quiz
                                                        @break
                                                    @case('assignment')
                                                        <i class="fas fa-tasks"></i> Assignment
                                                        @break
                                                    @case('resource')
                                                        <i class="fas fa-download"></i> Resource
                                                        @break
                                                    @default
                                                        <i class="fas fa-file"></i> Content
                                                @endswitch
                                            </span>
                                        </div>
                                    </div>
                                </a>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="course-content" role="main">
            <!-- Content Header -->
            <header class="content-header">
                <div class="header-controls">
                    <button class="sidebar-toggle" aria-label="Toggle sidebar">
                        <i class="fas fa-bars"></i>
                    </button>

                    @if(isset($currentLecture))
                        <!-- Navigation Controls -->
                        <nav class="lecture-navigation" aria-label="Lecture navigation">
                            @php
                                $allLectures = collect();
                                foreach($course->chapters as $chapter) {
                                    $allLectures = $allLectures->merge($chapter->lectures);
                                }
                                $currentIndex = $allLectures->search(function($lecture) use ($currentLecture) {
                                    return $lecture->id === $currentLecture->id;
                                });
                                $prevLecture = $currentIndex > 0 ? $allLectures[$currentIndex - 1] : null;
                                $nextLecture = $currentIndex < $allLectures->count() - 1 ? $allLectures[$currentIndex + 1] : null;
                            @endphp

                            @if($prevLecture)
                                <a href="{{ route('my-courses.lecture', [$course, $prevLecture]) }}" class="nav-btn">
                                    <i class="fas fa-chevron-left"></i>
                                    <span class="hidden sm:inline">Previous</span>
                                </a>
                            @endif

                            @if($nextLecture)
                                <a href="{{ route('my-courses.lecture', [$course, $nextLecture]) }}" class="nav-btn primary">
                                    <span class="hidden sm:inline">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            @endif
                        </nav>
                    @endif
                </div>
            </header>

            <!-- Lecture Content Area -->
            <div class="lecture-content-area">
                @if(isset($currentLecture))
                    <!-- Lecture Header -->
                    <header class="lecture-header">
                        <h1 class="lecture-title-main">{{ $currentLecture->title }}</h1>
                        <div class="lecture-meta-main">
                            @if($currentLecture->duration_minutes)
                                <div class="meta-item">
                                    <i class="fas fa-clock"></i>
                                    <span>{{ $currentLecture->duration_minutes }} minutes</span>
                                </div>
                            @endif
                            <div class="meta-item">
                                <i class="fas fa-{{ $currentLecture->type === 'video' ? 'play-circle' : ($currentLecture->type === 'text' ? 'file-text' : 'file') }}"></i>
                                <span>{{ ucfirst($currentLecture->type) }}</span>
                            </div>
                            @if($currentLecture->is_free)
                                <div class="meta-item">
                                    <i class="fas fa-unlock"></i>
                                    <span>Free Preview</span>
                                </div>
                            @endif
                        </div>
                    </header>

                    <!-- Lecture Content -->
                    <div class="content-wrapper">
                        @switch($currentLecture->type)
                            @case('video')
                                <div class="video-container">
                                    @if($currentLecture->video_url)
                                        @if(str_contains($currentLecture->video_url, 'youtube.com') || str_contains($currentLecture->video_url, 'youtu.be'))
                                            <div class="video-embed">
                                                <iframe src="{{ $currentLecture->video_url }}"
                                                        title="{{ $currentLecture->title }}"
                                                        allowfullscreen>
                                                </iframe>
                                            </div>
                                        @else
                                            <video class="video-player" controls>
                                                <source src="{{ $currentLecture->video_url }}" type="video/mp4">
                                                Your browser does not support the video tag.
                                            </video>
                                        @endif
                                    @else
                                        <div class="empty-state">
                                            <div class="empty-state-icon">🎥</div>
                                            <h3>Video Coming Soon</h3>
                                            <p>The video content for this lecture will be available soon.</p>
                                        </div>
                                    @endif
                                </div>
                                @break

                            @case('text')
                                <div class="text-content">
                                    {!! $currentLecture->content ?? '<p>Content coming soon...</p>' !!}
                                </div>
                                @break

                            @case('quiz')
                                <div class="quiz-content">
                                    <h3>Quiz: {{ $currentLecture->title }}</h3>
                                    @if($currentLecture->content)
                                        {!! $currentLecture->content !!}
                                    @else
                                        <p>Quiz content will be available soon.</p>
                                    @endif
                                </div>
                                @break

                            @case('assignment')
                                <div class="assignment-content">
                                    <h3>Assignment: {{ $currentLecture->title }}</h3>
                                    @if($currentLecture->content)
                                        {!! $currentLecture->content !!}
                                    @else
                                        <p>Assignment details will be available soon.</p>
                                    @endif
                                </div>
                                @break

                            @case('resource')
                                <div class="resource-content">
                                    <h3>Resource: {{ $currentLecture->title }}</h3>
                                    @if($currentLecture->content)
                                        {!! $currentLecture->content !!}
                                    @else
                                        <p>Resource will be available for download soon.</p>
                                    @endif
                                </div>
                                @break

                            @default
                                <div class="default-content">
                                    @if($currentLecture->content)
                                        {!! $currentLecture->content !!}
                                    @else
                                        <div class="empty-state">
                                            <div class="empty-state-icon">📄</div>
                                            <h3>Content Coming Soon</h3>
                                            <p>The content for this lecture will be available soon.</p>
                                        </div>
                                    @endif
                                </div>
                        @endswitch

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <div class="navigation-buttons">
                                @if($prevLecture)
                                    <a href="{{ route('my-courses.lecture', [$course, $prevLecture]) }}" class="nav-btn">
                                        <i class="fas fa-chevron-left"></i>
                                        <span>Previous Lesson</span>
                                    </a>
                                @endif

                                @if($nextLecture)
                                    <a href="{{ route('my-courses.lecture', [$course, $nextLecture]) }}" class="nav-btn primary">
                                        <span>Next Lesson</span>
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                @endif
                            </div>

                            <!-- Mark Complete Button -->
                            @if(!in_array($currentLecture->id, $completedLectureIds ?? []))
                                <button class="complete-btn" data-lecture-id="{{ $currentLecture->id }}">
                                    <i class="fas fa-check"></i>
                                    <span>Mark as Complete</span>
                                </button>
                            @else
                                <button class="complete-btn completed" disabled>
                                    <i class="fas fa-check"></i>
                                    <span>Completed</span>
                                </button>
                            @endif
                        </div>
                    </div>
                @else
                    <!-- Empty State - No Lecture Selected -->
                    <div class="empty-state">
                        <div class="empty-state-icon">🎓</div>
                        <h3>Ready to Start Learning?</h3>
                        <p>Select a lesson from the sidebar to begin your journey, or jump right into the first lesson.</p>
                        @if($course->chapters->isNotEmpty() && $course->chapters->first()->lectures->isNotEmpty())
                            <a href="{{ route('my-courses.lecture', [$course, $course->chapters->first()->lectures->first()]) }}"
                               class="complete-btn">
                                Start First Lesson
                                <i class="fas fa-play"></i>
                            </a>
                        @endif
                    </div>
                @endif
            </div>
        </main>
    </div>
</div>
@endsection
