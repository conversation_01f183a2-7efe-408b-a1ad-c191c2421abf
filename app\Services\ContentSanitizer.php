<?php

namespace App\Services;

use HTMLPurifier;
use HTMLPurifier_Config;

class ContentSanitizer
{
    private HTMLPurifier $purifier;

    public function __construct()
    {
        $config = HTMLPurifier_Config::createDefault();
        
        // Configure allowed HTML tags and attributes
        $config->set('HTML.Allowed', 
            'p,br,strong,em,u,ol,ul,li,a[href],img[src|alt|width|height],h1,h2,h3,h4,h5,h6,' .
            'blockquote,code,pre,table,thead,tbody,tr,td,th,div[class],span[class]'
        );
        
        // Allow some safe CSS properties
        $config->set('CSS.AllowedProperties', 'text-align,color,background-color,font-weight,font-style');
        
        // Configure URI filtering
        $config->set('URI.AllowedSchemes', ['http' => true, 'https' => true, 'mailto' => true]);
        $config->set('URI.DisableExternalResources', false);
        
        // Set cache directory (make sure this directory exists and is writable)
        $config->set('Cache.SerializerPath', storage_path('app/htmlpurifier'));
        
        $this->purifier = new HTMLPurifier($config);
    }

    /**
     * Sanitize HTML content for blog posts and rich text content.
     */
    public function sanitizeHtml(string $content): string
    {
        return $this->purifier->purify($content);
    }

    /**
     * Sanitize plain text content.
     */
    public function sanitizeText(string $text): string
    {
        // Remove any HTML tags
        $text = strip_tags($text);
        
        // Convert special characters to HTML entities
        $text = htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
        
        // Remove excessive whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        
        return trim($text);
    }

    /**
     * Sanitize and validate YouTube URL.
     */
    public function sanitizeYouTubeUrl(string $url): ?string
    {
        // Remove any potential XSS attempts
        $url = filter_var($url, FILTER_SANITIZE_URL);
        
        // Validate URL format
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return null;
        }

        // Check if it's a valid YouTube URL
        $patterns = [
            '/^https?:\/\/(www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})/',
            '/^https?:\/\/(www\.)?youtu\.be\/([a-zA-Z0-9_-]{11})/',
            '/^https?:\/\/(www\.)?youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return $url;
            }
        }

        return null;
    }

    /**
     * Sanitize file upload names.
     */
    public function sanitizeFileName(string $filename): string
    {
        // Remove path information
        $filename = basename($filename);
        
        // Remove any non-alphanumeric characters except dots, hyphens, and underscores
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
        
        // Limit length
        if (strlen($filename) > 255) {
            $extension = pathinfo($filename, PATHINFO_EXTENSION);
            $name = pathinfo($filename, PATHINFO_FILENAME);
            $filename = substr($name, 0, 255 - strlen($extension) - 1) . '.' . $extension;
        }

        return $filename;
    }

    /**
     * Sanitize tags input.
     */
    public function sanitizeTags(array $tags): array
    {
        $sanitizedTags = [];
        
        foreach ($tags as $tag) {
            // Remove HTML tags and special characters
            $tag = strip_tags($tag);
            $tag = preg_replace('/[^a-zA-Z0-9\s-_]/', '', $tag);
            $tag = trim($tag);
            
            // Skip empty tags or tags that are too short/long
            if (strlen($tag) >= 2 && strlen($tag) <= 50) {
                $sanitizedTags[] = strtolower($tag);
            }
        }

        // Remove duplicates and limit number of tags
        $sanitizedTags = array_unique($sanitizedTags);
        return array_slice($sanitizedTags, 0, 10); // Max 10 tags
    }

    /**
     * Sanitize search query.
     */
    public function sanitizeSearchQuery(string $query): string
    {
        // Remove HTML tags
        $query = strip_tags($query);
        
        // Remove special characters that could be used for SQL injection
        $query = preg_replace('/[^\w\s-]/', '', $query);
        
        // Limit length
        $query = substr($query, 0, 100);
        
        return trim($query);
    }

    /**
     * Sanitize category name.
     */
    public function sanitizeCategory(string $category): string
    {
        // Remove HTML tags and special characters
        $category = strip_tags($category);
        $category = preg_replace('/[^a-zA-Z0-9\s-_]/', '', $category);
        $category = trim($category);
        
        // Convert to lowercase and replace spaces with hyphens
        $category = strtolower(str_replace(' ', '-', $category));
        
        // Limit length
        return substr($category, 0, 50);
    }

    /**
     * Validate and sanitize SEO meta data.
     */
    public function sanitizeSeoMeta(array $meta): array
    {
        $sanitized = [];

        if (isset($meta['title'])) {
            $sanitized['title'] = $this->sanitizeText(substr($meta['title'], 0, 60));
        }

        if (isset($meta['description'])) {
            $sanitized['description'] = $this->sanitizeText(substr($meta['description'], 0, 160));
        }

        if (isset($meta['keywords'])) {
            $keywords = explode(',', $meta['keywords']);
            $keywords = array_map([$this, 'sanitizeText'], $keywords);
            $keywords = array_filter($keywords);
            $sanitized['keywords'] = implode(',', array_slice($keywords, 0, 10));
        }

        return $sanitized;
    }
}
