<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        $enrolledCourses = $user->enrollments()->with('course')->get();
        $recentCourses = $enrolledCourses->take(3);

        $stats = [
            'enrolled_courses' => $enrolledCourses->count(),
            'completed_courses' => $enrolledCourses->where('completed', true)->count(),
            'hours_learned' => $enrolledCourses->sum('hours_completed'),
            'certificates' => $enrolledCourses->where('completed', true)->count()
        ];

        return view('dashboard', compact('user', 'enrolledCourses', 'recentCourses', 'stats'));
    }
}
