<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lecture_progress', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->uuid('lecture_id');
            $table->uuid('chapter_id'); // Denormalized for performance
            $table->uuid('course_id'); // Denormalized for performance
            $table->boolean('is_completed')->default(false);
            $table->integer('watch_time_seconds')->default(0); // For video lectures
            $table->integer('total_duration_seconds')->default(0); // Snapshot of lecture duration
            $table->integer('completion_percentage')->default(0); // 0-100
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('last_accessed_at')->nullable();
            $table->json('quiz_answers')->nullable(); // For quiz lectures
            $table->integer('quiz_score')->nullable(); // Quiz score if applicable
            $table->boolean('quiz_passed')->nullable(); // Whether quiz was passed
            $table->integer('quiz_attempts')->default(0);
            $table->json('notes')->nullable(); // Student notes for this lecture
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('lecture_id')->references('id')->on('lectures')->onDelete('cascade');
            $table->foreign('chapter_id')->references('id')->on('chapters')->onDelete('cascade');
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('cascade');

            // Indexes for performance
            $table->index(['user_id', 'course_id', 'is_completed']);
            $table->index(['course_id', 'is_completed']);
            $table->index(['lecture_id', 'is_completed']);
            $table->index(['user_id', 'last_accessed_at']);
            $table->index('completion_percentage');
            
            // Unique constraint to prevent duplicate progress records
            $table->unique(['user_id', 'lecture_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lecture_progress');
    }
};
