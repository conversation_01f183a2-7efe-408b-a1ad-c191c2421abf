<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\Chapter;
use App\Models\Lecture;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LectureAutoSaveTest extends TestCase
{
    use RefreshDatabase;

    protected $instructor;
    protected $course;
    protected $chapter;
    protected $lecture;

    protected function setUp(): void
    {
        parent::setUp();

        $this->instructor = User::factory()->create([
            'role' => 'instructor',
            'email_verified_at' => now(),
        ]);

        $this->course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
        ]);

        $this->chapter = Chapter::factory()->create([
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
        ]);

        $this->lecture = Lecture::factory()->create([
            'chapter_id' => $this->chapter->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'type' => 'video',
        ]);
    }

    /** @test */
    public function instructor_can_get_lecture_data()
    {
        $this->actingAs($this->instructor);

        $response = $this->getJson(
            route('instructor.course-builder.lectures.get', [
                'course' => $this->course,
                'chapter' => $this->chapter,
                'lecture' => $this->lecture
            ])
        );

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ])
                ->assertJsonStructure([
                    'success',
                    'lecture' => [
                        'id',
                        'title',
                        'type',
                        'description',
                        'video_url',
                        'duration_minutes',
                        'content',
                        'quiz_data',
                        'quiz_passing_score',
                        'quiz_allow_retakes',
                        'resources',
                        'attachments',
                        'is_published',
                        'is_free_preview',
                        'is_mandatory',
                    ]
                ]);
    }

    /** @test */
    public function auto_save_works_for_video_lecture_fields()
    {
        $this->actingAs($this->instructor);

        $response = $this->post(
            route('instructor.course-builder.lectures.auto-save', [
                'course' => $this->course,
                'chapter' => $this->chapter,
                'lecture' => $this->lecture
            ]),
            [
                'title' => 'Updated Video Lecture',
                'type' => 'video',
                'video_url' => 'https://youtube.com/watch?v=test123',
                'duration_minutes' => 30,
                'description' => 'Updated description',
                'is_published' => '1',
                'is_free_preview' => '0',
                'is_mandatory' => '1',
            ],
            [
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        );

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Lecture saved automatically'
                ]);

        $this->assertDatabaseHas('lectures', [
            'id' => $this->lecture->id,
            'title' => 'Updated Video Lecture',
            'type' => 'video',
            'video_url' => 'https://youtube.com/watch?v=test123',
            'duration_minutes' => 30,
            'description' => 'Updated description',
            'is_published' => true,
            'is_free_preview' => false,
            'is_mandatory' => true,
        ]);
    }

    /** @test */
    public function auto_save_works_for_text_lecture_fields()
    {
        $this->actingAs($this->instructor);

        $textLecture = Lecture::factory()->create([
            'chapter_id' => $this->chapter->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'type' => 'text',
        ]);

        $response = $this->postJson(
            route('instructor.course-builder.lectures.auto-save', [
                'course' => $this->course,
                'chapter' => $this->chapter,
                'lecture' => $textLecture
            ]),
            [
                'title' => 'Updated Text Lecture',
                'type' => 'text',
                'content' => 'This is the updated lesson content with detailed information.',
                'estimated_completion_minutes' => 15,
                'description' => 'Updated text lecture description',
            ],
            [
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        );

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Lecture saved automatically'
                ]);

        $this->assertDatabaseHas('lectures', [
            'id' => $textLecture->id,
            'title' => 'Updated Text Lecture',
            'type' => 'text',
            'content' => 'This is the updated lesson content with detailed information.',
            'estimated_completion_minutes' => 15,
            'description' => 'Updated text lecture description',
        ]);
    }

    /** @test */
    public function auto_save_works_for_quiz_lecture_with_instructions()
    {
        $this->actingAs($this->instructor);

        $quizLecture = Lecture::factory()->create([
            'chapter_id' => $this->chapter->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'type' => 'quiz',
        ]);

        $response = $this->postJson(
            route('instructor.course-builder.lectures.auto-save', [
                'course' => $this->course,
                'chapter' => $this->chapter,
                'lecture' => $quizLecture
            ]),
            [
                'title' => 'Updated Quiz Lecture',
                'type' => 'quiz',
                'quiz_passing_score' => 85,
                'quiz_allow_retakes' => '1',
                'quiz_instructions' => 'Please read all questions carefully before answering.',
                'description' => 'Updated quiz description',
            ],
            [
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        );

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Lecture saved automatically'
                ]);

        $this->assertDatabaseHas('lectures', [
            'id' => $quizLecture->id,
            'title' => 'Updated Quiz Lecture',
            'type' => 'quiz',
            'quiz_passing_score' => 85,
            'quiz_allow_retakes' => true,
            'description' => 'Updated quiz description',
        ]);

        // Check that quiz instructions are stored in quiz_data JSON field
        $updatedLecture = $quizLecture->fresh();
        $this->assertEquals('Please read all questions carefully before answering.', $updatedLecture->quiz_data['instructions']);
    }

    /** @test */
    public function auto_save_works_for_assignment_lecture_fields()
    {
        $this->actingAs($this->instructor);

        $assignmentLecture = Lecture::factory()->create([
            'chapter_id' => $this->chapter->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'type' => 'assignment',
        ]);

        $response = $this->postJson(
            route('instructor.course-builder.lectures.auto-save', [
                'course' => $this->course,
                'chapter' => $this->chapter,
                'lecture' => $assignmentLecture
            ]),
            [
                'title' => 'Updated Assignment Lecture',
                'type' => 'assignment',
                'assignment_instructions' => 'Complete the project following the provided guidelines.',
                'assignment_max_points' => 150,
                'assignment_due_date' => '2024-12-31T23:59',
                'description' => 'Updated assignment description',
            ],
            [
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        );

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Lecture saved automatically'
                ]);

        $this->assertDatabaseHas('lectures', [
            'id' => $assignmentLecture->id,
            'title' => 'Updated Assignment Lecture',
            'type' => 'assignment',
            'description' => 'Updated assignment description',
        ]);

        // Check that assignment data is stored in attachments JSON field
        $updatedLecture = $assignmentLecture->fresh();
        $this->assertEquals('Complete the project following the provided guidelines.', $updatedLecture->attachments['instructions']);
        $this->assertEquals(150, $updatedLecture->attachments['max_points']);
        $this->assertEquals('2024-12-31T23:59', $updatedLecture->attachments['due_date']);
    }

    /** @test */
    public function auto_save_works_for_resource_lecture_fields()
    {
        $this->actingAs($this->instructor);

        $resourceLecture = Lecture::factory()->create([
            'chapter_id' => $this->chapter->id,
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
            'type' => 'resource',
        ]);

        $response = $this->postJson(
            route('instructor.course-builder.lectures.auto-save', [
                'course' => $this->course,
                'chapter' => $this->chapter,
                'lecture' => $resourceLecture
            ]),
            [
                'title' => 'Updated Resource Lecture',
                'type' => 'resource',
                'resource_description' => 'This resource contains helpful materials for the course.',
                'resource_url' => 'https://example.com/resource-file.pdf',
                'description' => 'Updated resource description',
            ],
            [
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        );

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Lecture saved automatically'
                ]);

        $this->assertDatabaseHas('lectures', [
            'id' => $resourceLecture->id,
            'title' => 'Updated Resource Lecture',
            'type' => 'resource',
            'description' => 'Updated resource description',
        ]);

        // Check that resource data is stored in resources JSON field
        $updatedLecture = $resourceLecture->fresh();
        $this->assertEquals('This resource contains helpful materials for the course.', $updatedLecture->resources['description']);
        $this->assertEquals('https://example.com/resource-file.pdf', $updatedLecture->resources['url']);
    }
}
