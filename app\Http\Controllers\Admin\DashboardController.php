<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use App\Models\Course;
use App\Models\Payment;
use App\Models\Enrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->isAdmin() && !auth()->user()->isSuperAdmin()) {
                abort(403, 'Access denied. Administrator privileges required.');
            }
            return $next($request);
        });
    }

    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        $stats = $this->getSystemStats();
        $recentActivity = $this->getRecentActivity();
        $chartData = $this->getChartData();

        return view('admin.dashboard', compact('stats', 'recentActivity', 'chartData'));
    }

    /**
     * Get system statistics.
     */
    private function getSystemStats()
    {
        $currentMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();

        return [
            'users' => [
                'total' => User::count(),
                'new_this_month' => User::where('created_at', '>=', $currentMonth)->count(),
                'verified' => User::whereNotNull('email_verified_at')->count(),
                'by_role' => [
                    'students' => User::whereHas('activeRoles', function ($q) {
                        $q->where('name', Role::STUDENT);
                    })->count(),
                    'instructors' => User::whereHas('activeRoles', function ($q) {
                        $q->where('name', Role::INSTRUCTOR);
                    })->count(),
                    'admins' => User::whereHas('activeRoles', function ($q) {
                        $q->whereIn('name', [Role::ADMIN, Role::SUPERADMIN]);
                    })->count(),
                ]
            ],
            'courses' => [
                'total' => Course::count(),
                'published' => Course::where('status', 'published')->count(),
                'draft' => Course::where('status', 'draft')->count(),
                'new_this_month' => Course::where('created_at', '>=', $currentMonth)->count(),
            ],
            'enrollments' => [
                'total' => Enrollment::count(),
                'active' => Enrollment::where('status', 'active')->count(),
                'completed' => Enrollment::where('status', 'completed')->count(),
                'new_this_month' => Enrollment::where('created_at', '>=', $currentMonth)->count(),
            ],
            'payments' => [
                'total' => Payment::count(),
                'completed' => Payment::where('status', Payment::STATUS_COMPLETED)->count(),
                'total_revenue' => Payment::where('status', Payment::STATUS_COMPLETED)->sum('amount'),
                'this_month_revenue' => Payment::where('status', Payment::STATUS_COMPLETED)
                    ->where('created_at', '>=', $currentMonth)
                    ->sum('amount'),
                'last_month_revenue' => Payment::where('status', Payment::STATUS_COMPLETED)
                    ->whereBetween('created_at', [$lastMonth, $currentMonth])
                    ->sum('amount'),
            ],
            'rbac' => [
                'roles' => Role::count(),
                'active_roles' => Role::where('is_active', true)->count(),
                'permissions' => Permission::count(),
                'active_permissions' => Permission::where('is_active', true)->count(),
            ]
        ];
    }

    /**
     * Get recent activity data.
     */
    private function getRecentActivity()
    {
        return [
            'recent_users' => User::with('activeRoles')
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get(),
            'recent_courses' => Course::with('instructor')
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get(),
            'recent_enrollments' => Enrollment::with(['user', 'course'])
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get(),
            'recent_payments' => Payment::with(['user', 'course'])
                ->where('status', Payment::STATUS_COMPLETED)
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get(),
        ];
    }

    /**
     * Get chart data for dashboard.
     */
    private function getChartData()
    {
        $last30Days = collect(range(0, 29))->map(function ($i) {
            return now()->subDays($i)->format('Y-m-d');
        })->reverse()->values();

        // User registrations over last 30 days
        $userRegistrations = User::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as count')
            )
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->pluck('count', 'date');

        // Course creations over last 30 days
        $courseCreations = Course::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as count')
            )
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->pluck('count', 'date');

        // Revenue over last 30 days
        $dailyRevenue = Payment::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(amount) as revenue')
            )
            ->where('status', Payment::STATUS_COMPLETED)
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->pluck('revenue', 'date');

        return [
            'dates' => $last30Days,
            'user_registrations' => $last30Days->map(function ($date) use ($userRegistrations) {
                return $userRegistrations->get($date, 0);
            }),
            'course_creations' => $last30Days->map(function ($date) use ($courseCreations) {
                return $courseCreations->get($date, 0);
            }),
            'daily_revenue' => $last30Days->map(function ($date) use ($dailyRevenue) {
                return $dailyRevenue->get($date, 0);
            }),
        ];
    }

    /**
     * Get system health status.
     */
    public function systemHealth()
    {
        $health = [
            'database' => $this->checkDatabaseHealth(),
            'storage' => $this->checkStorageHealth(),
            'cache' => $this->checkCacheHealth(),
            'rbac' => $this->checkRbacHealth(),
        ];

        return response()->json($health);
    }

    /**
     * Check database health.
     */
    private function checkDatabaseHealth()
    {
        try {
            DB::connection()->getPdo();
            return ['status' => 'healthy', 'message' => 'Database connection is working'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Database connection failed: ' . $e->getMessage()];
        }
    }

    /**
     * Check storage health.
     */
    private function checkStorageHealth()
    {
        try {
            $testFile = storage_path('app/health_check.txt');
            file_put_contents($testFile, 'health check');
            $content = file_get_contents($testFile);
            unlink($testFile);
            
            return ['status' => 'healthy', 'message' => 'Storage is writable'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Storage write failed: ' . $e->getMessage()];
        }
    }

    /**
     * Check cache health.
     */
    private function checkCacheHealth()
    {
        try {
            cache()->put('health_check', 'test', 60);
            $value = cache()->get('health_check');
            cache()->forget('health_check');
            
            if ($value === 'test') {
                return ['status' => 'healthy', 'message' => 'Cache is working'];
            } else {
                return ['status' => 'warning', 'message' => 'Cache read/write mismatch'];
            }
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Cache failed: ' . $e->getMessage()];
        }
    }

    /**
     * Check RBAC system health.
     */
    private function checkRbacHealth()
    {
        try {
            $issues = [];
            
            // Check if all system roles exist
            $systemRoles = [Role::STUDENT, Role::INSTRUCTOR, Role::ADMIN, Role::SUPERADMIN];
            foreach ($systemRoles as $roleName) {
                if (!Role::where('name', $roleName)->exists()) {
                    $issues[] = "Missing system role: {$roleName}";
                }
            }
            
            // Check if roles have permissions
            $rolesWithoutPermissions = Role::doesntHave('permissions')->count();
            if ($rolesWithoutPermissions > 0) {
                $issues[] = "{$rolesWithoutPermissions} roles have no permissions assigned";
            }
            
            // Check if users have roles
            $usersWithoutRoles = User::doesntHave('activeRoles')->count();
            if ($usersWithoutRoles > 0) {
                $issues[] = "{$usersWithoutRoles} users have no roles assigned";
            }
            
            if (empty($issues)) {
                return ['status' => 'healthy', 'message' => 'RBAC system is properly configured'];
            } else {
                return ['status' => 'warning', 'message' => 'RBAC issues: ' . implode(', ', $issues)];
            }
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'RBAC check failed: ' . $e->getMessage()];
        }
    }

    /**
     * Get system logs.
     */
    public function logs(Request $request)
    {
        $level = $request->get('level', 'all');
        $limit = $request->get('limit', 100);
        
        // This is a simplified log reader - in production you might want to use a proper log management solution
        $logFile = storage_path('logs/laravel.log');
        
        if (!file_exists($logFile)) {
            return response()->json(['error' => 'Log file not found']);
        }
        
        $logs = [];
        $handle = fopen($logFile, 'r');
        
        if ($handle) {
            $lines = [];
            while (($line = fgets($handle)) !== false) {
                $lines[] = $line;
            }
            fclose($handle);
            
            // Get last N lines
            $logs = array_slice($lines, -$limit);
        }
        
        return response()->json(['logs' => $logs]);
    }
}
