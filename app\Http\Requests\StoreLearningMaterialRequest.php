<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreLearningMaterialRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && (auth()->user()->isInstructor() || auth()->user()->isAdmin() || auth()->user()->isSuperAdmin());
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:2000',
            'content' => 'nullable|string',
            'type' => 'required|in:text,video,audio,document,presentation',
            'course_id' => 'nullable|exists:courses,id',
            'file' => 'nullable|file|max:100000', // 100MB max
            'sort_order' => 'nullable|integer|min:0',
            'is_published' => 'boolean'
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'The title field is required.',
            'title.max' => 'The title may not be greater than 255 characters.',
            'type.required' => 'Please select a material type.',
            'type.in' => 'The selected material type is invalid.',
            'course_id.exists' => 'The selected course is invalid.',
            'file.max' => 'The file may not be greater than 100MB.',
            'sort_order.integer' => 'The sort order must be a number.',
            'sort_order.min' => 'The sort order must be at least 0.'
        ];
    }
}
