<?php

namespace App\Http\Middleware;

use App\Models\Course;
use App\Models\LearningMaterial;
use App\Services\CourseMaterialAccessService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CourseMaterialAccess
{
    protected CourseMaterialAccessService $accessService;

    public function __construct(CourseMaterialAccessService $accessService)
    {
        $this->accessService = $accessService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Authentication required to access course materials.'], 401);
            }

            session(['url.intended' => $request->fullUrl()]);
            return redirect()->route('login')
                ->with('error', 'Please log in to access course materials.');
        }

        $user = Auth::user();
        
        // Extract course and material information from the request
        $courseId = $this->extractCourseId($request);
        $materialId = $this->extractMaterialId($request);
        
        if (!$courseId) {
            abort(400, 'Invalid course material request.');
        }

        // Check access permissions
        $accessResult = $this->accessService->checkAccess($user, $courseId, $materialId);
        
        if (!$accessResult['allowed']) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => $accessResult['message'],
                    'reason' => $accessResult['reason'],
                    'requires_payment' => $accessResult['requires_payment'] ?? false
                ], 403);
            }

            // Redirect with appropriate error message
            return $this->handleAccessDenied($accessResult, $courseId);
        }

        return $next($request);
    }

    /**
     * Extract course ID from the request
     */
    protected function extractCourseId(Request $request): ?string
    {
        // Check route parameters
        if ($request->route('course')) {
            $course = $request->route('course');
            return is_string($course) ? $course : $course->id;
        }

        // Check for material route parameter
        if ($request->route('material')) {
            $material = $request->route('material');
            if (is_string($material)) {
                $learningMaterial = LearningMaterial::find($material);
                return $learningMaterial?->course_id;
            }
            return $material->course_id;
        }

        // Extract from file path for secure file serving
        $filePath = $request->route('filePath');
        if ($filePath && str_contains($filePath, 'private/')) {
            // Parse file path: private/{user_id}/{course_id}/materials/...
            $pathParts = explode('/', $filePath);
            if (count($pathParts) >= 3 && $pathParts[0] === 'private') {
                return $pathParts[2]; // course_id is the third part
            }
        }

        return null;
    }

    /**
     * Extract material ID from the request
     */
    protected function extractMaterialId(Request $request): ?string
    {
        if ($request->route('material')) {
            $material = $request->route('material');
            return is_string($material) ? $material : $material->id;
        }

        return null;
    }

    /**
     * Handle access denied scenarios
     */
    protected function handleAccessDenied(array $accessResult, string $courseId): Response
    {
        $course = Course::find($courseId);
        
        if ($accessResult['requires_payment'] ?? false) {
            return redirect()->route('courses.show', $course)
                ->with('error', $accessResult['message'])
                ->with('requires_payment', true);
        }

        if ($accessResult['reason'] === 'course_not_published') {
            return redirect()->route('courses.index')
                ->with('error', $accessResult['message']);
        }

        return redirect()->route('courses.show', $course ?? $courseId)
            ->with('error', $accessResult['message']);
    }
}
